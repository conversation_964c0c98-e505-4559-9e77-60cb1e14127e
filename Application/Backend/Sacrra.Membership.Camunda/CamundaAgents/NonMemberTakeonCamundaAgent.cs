using System;
using System.Threading.Tasks;
using Camunda.Api.Client;
using Camunda.Api.Client.ExternalTask;
using Microsoft.Extensions.DependencyInjection;

namespace Sacrra.Membership.Camunda.CamundaAgents;

public class NonMemberTakeonCamundaAgent: BaseCamundaAgent
{
    public NonMemberTakeonCamundaAgent(CamundaClient camundaClient) : base(camundaClient) { }

    public override async Task Process(string topicName, ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        switch (topicName)
        {
            case "start-membership-type-change-work":
            case "complete-member-application-final-takeon": 
            case "cencel-member-application":
            case "awaiting-onboarding-payment":
            case "awaiting-assessment-payment":
            case "check-if-member-exists":
                throw new NotImplementedException();
        }
        
        task.Complete(completeExternalTask);
    }
}
