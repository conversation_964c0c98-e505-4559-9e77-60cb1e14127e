using Camunda.Api.Client;
using Camunda.Api.Client.ExternalTask;
using Microsoft.Extensions.DependencyInjection;
using System.Threading.Tasks;
using Sacrra.Membership.Business.Services;

namespace Sacrra.Membership.Camunda.CamundaAgents
{
    public class MemberStatusUpdateCamundaAgent : BaseCamundaAgent
    {
        public MemberStatusUpdateCamundaAgent(CamundaClient camundaClient) : base(camundaClient) { }

        public override async Task Process(string topicName, ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
        {
            switch (topicName)
            {
                case "email-member-to-state-removal":
                    EmailMemberToStateRemoval(task, completeExternalTask, serviceScope);
                    break;

                case "enable-or-disable-member-users":
                    EnableOrDisableMemberUsers(task, completeExternalTask, serviceScope);
                    break;
            }
            
            task.Complete(completeExternalTask);
        }

        private void EnableOrDisableMemberUsers(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
        {
            var memberId = GetMemberVariable(task, "memberId", serviceScope);
            var memberStatus = GetGenericVariable(task, "memberStatus", serviceScope);

            GetMemberStatusUpdateCamundaService(serviceScope).EnableOrDisableMemberUsers(memberId, memberStatus);
        }

        private void EmailMemberToStateRemoval(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
        {
            var memberId = GetMemberVariable(task, "memberId", serviceScope);
            var memberStatus = GetGenericVariable(task, "memberStatus", serviceScope);

            GetMemberStatusUpdateCamundaService(serviceScope).EmailMemberToStateRemovalOrReactivation(memberId, memberStatus);
        }
        
        private static MemberStatusUpdateCamundaService GetMemberStatusUpdateCamundaService(IServiceScope serviceScope)
        {
            return serviceScope.ServiceProvider.GetRequiredService<MemberStatusUpdateCamundaService>();
        }
    }
}

