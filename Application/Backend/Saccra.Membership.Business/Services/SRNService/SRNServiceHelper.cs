using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.Resources.BranchLocation;
using Sacrra.Membership.Business.Resources.Camunda.Task;
using Sacrra.Membership.Business.Resources.IdValuePair;
using Sacrra.Membership.Business.Resources.MemberChangeRequest;
using Sacrra.Membership.Business.Resources.SRN;
using Sacrra.Membership.Business.Resources.SRNStatus;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;
using Sacrra.Membership.Database.publicShared;
using Serilog;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Net.Http;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using Saccra.Membership.Business.DTOs.SRNSummaryDTOs;
using Sacrra.Membership.Business.DTOs;
using Sacrra.Membership.Business.DTOs.SRNUpdateDTOs;
using Sacrra.Membership.Business.Exceptions;
using Sacrra.Membership.Business.Repositories;
using AutoMapper.Execution;

namespace Sacrra.Membership.Business.Services
{
    public class SRNServiceHelper
    {
        private readonly ConfigSettings _configSettings;
        private readonly AppDbContext _dbContext;
        private LookupsRepository _lookupsRepo;
        public IMapper _mapper { get; }
        private readonly GlobalHelper _globalHelper;
        private readonly CamundaRepository _camundaRepository;

        public SRNServiceHelper(IOptions<ConfigSettings> configSettings, IMapper mapper, AppDbContext dbContext, GlobalHelper globalHelper,
            LookupsRepository lookupsRepo, CamundaRepository camundaRepository)
        {
            _configSettings = configSettings.Value;
            _mapper = mapper;
            _dbContext = dbContext;
            _globalHelper = globalHelper;
            _lookupsRepo = lookupsRepo;
            _camundaRepository = camundaRepository;
        }

        internal void DeleteProcessInstance(string processInstanceId)
        {
            using (var client = new HttpClient())
            {
                var content = new StringContent("", Encoding.UTF8, "application/json");
                var uri = _configSettings.CamundaBaseAddress + "/process-instance/" + processInstanceId;
                var result = client.DeleteAsync(uri).Result;
                result.EnsureSuccessStatusCode();
            }
        }

        internal bool IsPropertyForeignKey(PropertyInfo prop)
        {
            bool isForeignKey = false;
            var groupName = "";
            var attributes = prop.GetCustomAttributes(typeof(DisplayAttribute),
                false);

            if (attributes != null)
            {
                if (attributes.Length > 0)
                {
                    groupName = attributes.Cast<DisplayAttribute>().Single().GroupName;
                    if (groupName == "ForeignKey")
                        isForeignKey = true;
                }
            }

            return isForeignKey;
        }

        internal string GetPropertyVaueById(AppDbContext context, int id, string propertyName)
        {
            string propertyValue = string.Empty;
            try
            {
                switch (propertyName)
                {
                    case "LoanManagementSystemId":
                    case "LoanManagementSystemVendorId":
                        var entity = context.LoanManagementSystemVendors.FirstOrDefault(i => i.Id == id);
                        if (entity != null)
                            propertyValue = entity.Name;
                        break;

                    case "ThirdPartyVendorId":
                    case "SoftwareVendorId":
                        var vendor = context.SoftwareVendors.FirstOrDefault(i => i.Id == id);
                        if (vendor != null)
                            propertyValue = vendor.Name;
                        break;

                    case "AccountTypeId":
                        var type = context.AccountTypes.FirstOrDefault(i => i.Id == id);
                        if (type != null)
                            propertyValue = type.Name;
                        break;

                    case "NCRReportingAccountTypeClassificationId":
                        var typeClassification = context.NCRReportingAccountTypeClassifications.FirstOrDefault(i => i.Id == id);
                        if (typeClassification != null)
                            propertyValue = typeClassification.Name;
                        break;

                    case "SPNumberId":
                    case "SPGroupId":
                        var spGroup = context.SPGroups.FirstOrDefault(i => i.Id == id);
                        if (spGroup != null)
                            propertyValue = spGroup.SPNumber;
                        break;
                    case "SRNStatusId":
                        var srnStatus = context.SRNStatuses.FirstOrDefault(i => i.Id == id);
                        if (srnStatus != null)
                            propertyValue = srnStatus.Name;
                        break;
                    case "CreditInformationClassificationId":
                        var creditInformationClassification = context.CreditInformationClassifications.FirstOrDefault(i => i.Id == id);
                        if (creditInformationClassification != null)
                            propertyValue = creditInformationClassification.Name;
                        break;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, $"Unable to get property value by Id. Property name {propertyName}");
                throw;
            }


            return propertyValue;
        }

        internal void StartSRNUpdateWorkflow(SRN existingSRN, bool isApprovalRequired, int changeRequestId)
        {
            try
            {
                string requireApproval = isApprovalRequired ? "yes" : "no";

                var taskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
   {
       {
           "variables",
           new Dictionary<string, Dictionary<string, string>>
           {
               {
                   "SRNId",
                   new Dictionary<string, string>
                   {
                       { "value", existingSRN.Id.ToString() },
                       { "type", "Long" }
                   }
               },
               {
                   "requireApproval",
                   new Dictionary<string, string>
                   {
                       { "value", requireApproval },
                       { "type", "String" }
                   }
               },
               {
                   "ChangeRequestId",
                   new Dictionary<string, string>
                   {
                       { "value", changeRequestId.ToString() },
                       { "type", "Long" }
                   }
               }
           }
       }
   };

                using (var client = new HttpClient())
                {
                    var contractResolver = new DefaultContractResolver
                    {
                        NamingStrategy = new CamelCaseNamingStrategy()
                    };

                    var json = JsonConvert.SerializeObject(taskVariables, new JsonSerializerSettings
                    {
                        ContractResolver = contractResolver,
                        Formatting = Formatting.Indented
                    });

                    var content = new StringContent(json, Encoding.UTF8, "application/json");
                    var uri = _configSettings.CamundaBaseAddress + "/process-definition/key/SRN-Update-Details/start";

                    var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });

                    if (!result.IsSuccessStatusCode)
                    {
                        var responseContent = result.Content.ReadAsString();
                        throw new Exception($"Failed to start Camunda workflow: {result.StatusCode} - {responseContent}");
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Error starting SRN Update Workflow for SRN ID: {existingSRN.Id}, ChangeRequestId: {changeRequestId}");
            }
        }

        internal bool DoSRNChangesRequireApproval(SRN existingSRN, SRNUpdateInputDTO srnUpdateInputDTO)
        {
            var isApprovalRequired = false;

            var fieldSettings = _dbContext.SRNFieldUpdateSettings.ToList();

            if (existingSRN != null && srnUpdateInputDTO != null)
            {
                if (srnUpdateInputDTO != null)
                {
                    var updateModelType = srnUpdateInputDTO.GetType();
                    var srnModelType = existingSRN.GetType();
                    IList<PropertyInfo> updatedProperties = new List<PropertyInfo>(updateModelType.GetProperties());
                    IList<PropertyInfo> oldProperties = new List<PropertyInfo>(srnModelType.GetProperties());

                    foreach (PropertyInfo updatedProp in updatedProperties)
                    {
                        //If SRN dates from the linking table
                        if (updatedProp.DeclaringType == typeof(SRNDatesShared))
                        {
                            object updatedPropValue = updatedProp.GetValue(srnUpdateInputDTO, null);
                            var oldStatusUpdateProperty = oldProperties.FirstOrDefault(i => i.PropertyType == typeof(ICollection<SRNStatusUpdateHistory>));
                            var statusUpdateType = new SRNStatusUpdateHistory().GetType();
                            var oldStatusUpdateProperties = statusUpdateType.GetProperties();

                            var oldProp = oldStatusUpdateProperties.FirstOrDefault(i => i.Name == updatedProp.Name);

                            if (oldProp != null)
                            {
                                SRNStatusUpdateHistory recentSRNStatusUpdate = null;

                                //If there are no previous updates in the SRN Status Update History, create approval request
                                if (existingSRN.SRNStatusUpdates.Count <= 0 && updatedPropValue != null)
                                {
                                    isApprovalRequired = true;
                                    break;
                                }
                                else
                                {
                                    recentSRNStatusUpdate = existingSRN.SRNStatusUpdates.OrderByDescending(i => i.DateCreated).FirstOrDefault();
                                }

                                if (recentSRNStatusUpdate != null)
                                {
                                    object oldPropValue = oldProp.GetValue(recentSRNStatusUpdate, null);

                                    if (oldPropValue != null)
                                    {
                                        var propType = oldPropValue.GetType();

                                        if (propType == typeof(DateTime) || propType == typeof(DateTime?))
                                        {
                                            if (updatedPropValue == null && propType == typeof(DateTime?))
                                                updatedPropValue = "";

                                            if (oldPropValue == null && propType == typeof(DateTime?))
                                                oldPropValue = "";

                                            if (updatedPropValue.ToString() != oldPropValue.ToString())
                                            {
                                                var setting = fieldSettings.FirstOrDefault(i => i.FieldName == oldProp.Name);
                                                if (setting != null)
                                                {
                                                    if (setting.IsUpdatable && setting.IsApprovalRequired)
                                                    {
                                                        isApprovalRequired = true;
                                                        break;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        else
                        {
                            object updatedPropValue = updatedProp.GetValue(srnUpdateInputDTO, null);
                            var fieldUpdateSetting = fieldSettings.FirstOrDefault(i => i.FieldUpdateName == updatedProp.Name);
                            string fieldName = (fieldUpdateSetting != null) ? fieldUpdateSetting.FieldName : "";

                            var oldProp = oldProperties.FirstOrDefault(i => i.Name == fieldName);
                            if (oldProp != null)
                            {
                                object oldPropValue = oldProp.GetValue(existingSRN, null);

                                if (oldPropValue != null)
                                {
                                    var propType = oldPropValue.GetType();
                                    if (propType.IsPrimitive || propType == (typeof(System.String)))
                                    {
                                        if (updatedPropValue == null && propType == (typeof(int)))
                                            updatedPropValue = 0;

                                        if (oldPropValue == null && propType == (typeof(int)))
                                            oldPropValue = 0;

                                        if (updatedPropValue.ToString() != oldPropValue.ToString())
                                        {
                                            var setting = fieldSettings.FirstOrDefault(i => i.FieldName == oldProp.Name);
                                            if (setting != null)
                                            {
                                                if (setting.IsUpdatable && setting.IsApprovalRequired)
                                                {
                                                    isApprovalRequired = true;
                                                    break;
                                                }
                                            }
                                        }
                                    }
                                    else if (propType.IsEnum)
                                    {
                                        if ((int)updatedPropValue != (int)oldPropValue)
                                        {
                                            var setting = fieldSettings.FirstOrDefault(i => i.FieldName == oldProp.Name);
                                            if (setting != null)
                                            {
                                                if (setting.IsUpdatable && setting.IsApprovalRequired)
                                                {
                                                    isApprovalRequired = true;
                                                    break;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            return isApprovalRequired;
        }

        internal void PopulateSRNStatusUpdateHistory(SRNGetOutputDTO selectedRecord, SRNStatusFileTypes? fileType = null)
        {
            var recentStatusUpdate =new SRNStatusUpdateHistory();
            if (fileType != null)
            {
                 recentStatusUpdate = _dbContext.SRNStatusUpdateHistory
                .OrderByDescending(i => i.Id)
                .FirstOrDefault(i => i.SRNId == selectedRecord.Id && i.FileType == fileType);
            }
            else
            {
                 recentStatusUpdate = _dbContext.SRNStatusUpdateHistory
                .OrderByDescending(i => i.Id)
                .FirstOrDefault(i => i.SRNId == selectedRecord.Id);
            }

            if (recentStatusUpdate == null) return;

            selectedRecord.DailyFileDevelopmentStartDate = recentStatusUpdate.DailyFileDevelopmentStartDate != null ? string.Format("{0:yyyy-MM-dd}", DateTime.Parse(recentStatusUpdate.DailyFileDevelopmentStartDate.ToString())) : null;
            selectedRecord.DailyFileDevelopmentEndDate = recentStatusUpdate.DailyFileDevelopmentEndDate != null ? string.Format("{0:yyyy-MM-dd}", DateTime.Parse(recentStatusUpdate.DailyFileDevelopmentEndDate.ToString())) : null;
            selectedRecord.DailyFileTestStartDate = recentStatusUpdate.DailyFileTestStartDate != null ? string.Format("{0:yyyy-MM-dd}", DateTime.Parse(recentStatusUpdate.DailyFileTestStartDate.ToString())) : null;
            selectedRecord.DailyFileTestEndDate = recentStatusUpdate.DailyFileTestEndDate != null ? string.Format("{0:yyyy-MM-dd}", DateTime.Parse(recentStatusUpdate.DailyFileTestEndDate.ToString())) : null;
            selectedRecord.DailyFileGoLiveDate = recentStatusUpdate.DailyFileGoLiveDate != null ? string.Format("{0:yyyy-MM-dd}", DateTime.Parse(recentStatusUpdate.DailyFileGoLiveDate.ToString())) : null;

            selectedRecord.MonthlyFileDevelopmentStartDate = recentStatusUpdate.MonthlyFileDevelopmentStartDate != null ? string.Format("{0:yyyy-MM-dd}", DateTime.Parse(recentStatusUpdate.MonthlyFileDevelopmentStartDate.ToString())) : null;
            selectedRecord.MonthlyFileDevelopmentEndDate = recentStatusUpdate.MonthlyFileDevelopmentEndDate != null ? string.Format("{0:yyyy-MM-dd}", DateTime.Parse(recentStatusUpdate.MonthlyFileDevelopmentEndDate.ToString())) : null;
            selectedRecord.MonthlyFileTestStartDate = recentStatusUpdate.MonthlyFileTestStartDate != null ? string.Format("{0:yyyy-MM-dd}", DateTime.Parse(recentStatusUpdate.MonthlyFileTestStartDate.ToString())) : null;
            selectedRecord.MonthlyFileTestEndDate = recentStatusUpdate.MonthlyFileTestEndDate != null ? string.Format("{0:yyyy-MM-dd}", DateTime.Parse(recentStatusUpdate.MonthlyFileTestEndDate.ToString())) : null;
            selectedRecord.MonthlyFileGoLiveDate = recentStatusUpdate.MonthlyFileGoLiveDate != null ? string.Format("{0:yyyy-MM-dd}", DateTime.Parse(recentStatusUpdate.MonthlyFileGoLiveDate.ToString())) : null;
            selectedRecord.IsLiveFileSubmissionsSuspended = recentStatusUpdate.IsLiveFileSubmissionsSuspended;
            selectedRecord.IsDailyFileLive = recentStatusUpdate.IsDailyFileLive;
            selectedRecord.IsMonthlyFileLive = recentStatusUpdate.IsMonthlyFileLive;
            selectedRecord.UpdateType = (recentStatusUpdate.UpdateType != null) ? _lookupsRepo.GetEnumIdValuePair<SRNStatusTypes>((int)recentStatusUpdate.UpdateType) : new IdValuePairResource { Id = 0, Value = "N/A" };
        }

        internal void PopulateSRNStatusUpdateHistory(SRNViewOutputDTO srnGetResource, ICollection<SRNStatusUpdateHistory> srnStatusUpdates, SRNStatusFileTypes? fileType = null)
        {
            if (srnGetResource != null && srnStatusUpdates != null)
            {
                if (srnStatusUpdates.Count > 0)
                {
                    SRNStatusUpdateHistory recentStatusUpdate = null;

                    if (fileType == null)
                    {
                        recentStatusUpdate = srnStatusUpdates.OrderByDescending(i => i.DateCreated).First();
                    }
                    else
                    {
                        recentStatusUpdate = srnStatusUpdates.OrderByDescending(i => i.DateCreated).FirstOrDefault(i => i.FileType == fileType);
                    }

                    if (recentStatusUpdate != null)
                    {
                        srnGetResource.DailyFileDevelopmentStartDate = recentStatusUpdate.DailyFileDevelopmentStartDate != null ? string.Format("{0:yyyy-MM-dd}", DateTime.Parse(recentStatusUpdate.DailyFileDevelopmentStartDate.ToString())) : null;
                        srnGetResource.DailyFileDevelopmentEndDate = recentStatusUpdate.DailyFileDevelopmentEndDate != null ? string.Format("{0:yyyy-MM-dd}", DateTime.Parse(recentStatusUpdate.DailyFileDevelopmentEndDate.ToString())) : null;
                        srnGetResource.DailyFileTestStartDate = recentStatusUpdate.DailyFileTestStartDate != null ? string.Format("{0:yyyy-MM-dd}", DateTime.Parse(recentStatusUpdate.DailyFileTestStartDate.ToString())) : null;
                        srnGetResource.DailyFileTestEndDate = recentStatusUpdate.DailyFileTestEndDate != null ? string.Format("{0:yyyy-MM-dd}", DateTime.Parse(recentStatusUpdate.DailyFileTestEndDate.ToString())) : null;
                        srnGetResource.DailyFileGoLiveDate = recentStatusUpdate.DailyFileGoLiveDate != null ? string.Format("{0:yyyy-MM-dd}", DateTime.Parse(recentStatusUpdate.DailyFileGoLiveDate.ToString())) : null;

                        srnGetResource.MonthlyFileDevelopmentStartDate = recentStatusUpdate.MonthlyFileDevelopmentStartDate != null ? string.Format("{0:yyyy-MM-dd}", DateTime.Parse(recentStatusUpdate.MonthlyFileDevelopmentStartDate.ToString())) : null;
                        srnGetResource.MonthlyFileDevelopmentEndDate = recentStatusUpdate.MonthlyFileDevelopmentEndDate != null ? string.Format("{0:yyyy-MM-dd}", DateTime.Parse(recentStatusUpdate.MonthlyFileDevelopmentEndDate.ToString())) : null;
                        srnGetResource.MonthlyFileTestStartDate = recentStatusUpdate.MonthlyFileTestStartDate != null ? string.Format("{0:yyyy-MM-dd}", DateTime.Parse(recentStatusUpdate.MonthlyFileTestStartDate.ToString())) : null;
                        srnGetResource.MonthlyFileTestEndDate = recentStatusUpdate.MonthlyFileTestEndDate != null ? string.Format("{0:yyyy-MM-dd}", DateTime.Parse(recentStatusUpdate.MonthlyFileTestEndDate.ToString())) : null;
                        srnGetResource.MonthlyFileGoLiveDate = recentStatusUpdate.MonthlyFileGoLiveDate != null ? string.Format("{0:yyyy-MM-dd}", DateTime.Parse(recentStatusUpdate.MonthlyFileGoLiveDate.ToString())) : null;
                        srnGetResource.Comments = recentStatusUpdate.Comments;
                        srnGetResource.IsLiveFileSubmissionsSuspended = recentStatusUpdate.IsLiveFileSubmissionsSuspended;
                        srnGetResource.UpdateTypeId = recentStatusUpdate.UpdateType;
                        srnGetResource.IsDailyFileLive = recentStatusUpdate.IsDailyFileLive;
                        srnGetResource.IsMonthlyFileLive = recentStatusUpdate.IsMonthlyFileLive;
                    }
                }
            }
        }
        internal SRNStatusGetResource GetByName(string name)
        {
            var selectRecord = _dbContext.Set<SRNStatus>()
                    .AsNoTracking()
                .FirstOrDefault(s => s.Name.ToLower() == name.Trim().ToLower());

            var returnRecord = _mapper.Map<SRNStatusGetResource>(selectRecord);

            return returnRecord;
        }

        internal void CreateEventLog(AppDbContext _dbContext, int userId, string changeType, string entityName, string entityBlob, string changeBlob, int entityId, string entityTypeName)
        {
            var userFullName = "";
            if (userId > 0)
            {
                var user = _dbContext.Users.FirstOrDefault(i => i.Id == userId);
                if (user != null)
                    userFullName = user.FirstName + " " + user.LastName;
            }
            else
            {
                userFullName = "Internal System";
            }


            var entityTypeId = _globalHelper.GetEntityTypeId(_dbContext, entityTypeName);

            if (entityTypeId > 0)
            {
                var eventLog = new EventLog
                {
                    User = userFullName,
                    Date = DateTime.Now,
                    ChangeType = changeType,
                    EntityName = entityName,
                    EntityBlob = entityBlob,
                    ChangeBlob = changeBlob,
                    EntityId = entityId,
                    EntityTypeId = entityTypeId
                };

                _dbContext.Add(eventLog);
                _dbContext.SaveChanges();
            }
        }

        public MemberStagingChangeLogResource CreateSRNEventLog(SRNUpdateResource modelForUpdate, SRN srn, MemberStagingChangeLogResource memberStagingChangeLog)
        {
            if (modelForUpdate != null)
            {
                var updateModelType = modelForUpdate.GetType();
                var srnModelType = srn.GetType();
                IList<PropertyInfo> updateProperties = new List<PropertyInfo>(updateModelType.GetProperties());
                IList<PropertyInfo> srnProperties = new List<PropertyInfo>(srnModelType.GetProperties());
                List<StagingChange> stagingChangeList = new List<StagingChange>();

                foreach (PropertyInfo updateProp in updateProperties)
                {
                    if (updateProp.DeclaringType == typeof(SRNDatesShared))
                    {
                        object updatePropValue = updateProp.GetValue(modelForUpdate, null);

                        if (updatePropValue != null)
                        {
                            var stagingChange = new StagingChange
                            {
                                Name = _globalHelper.GetPropertyDisplayName(updateProp),
                                OldValue = "",
                                NewValue = string.Format("{0:yyyy-MM-dd}", updatePropValue)
                            };

                            stagingChangeList.Add(stagingChange);
                        }
                    }
                    else if (updateProp.Name != "Id")
                    {
                        object updatePropValue = updateProp.GetValue(modelForUpdate, null);

                        var srnProp = srnProperties.FirstOrDefault(i => i.Name == updateProp.Name);
                        if (srnProp != null)
                        {
                            object srnPropValue = srnProp.GetValue(srn, null);

                            if (srnPropValue != null)
                            {
                                var propType = srnPropValue.GetType();
                                if (propType.IsPrimitive || propType == (typeof(System.String)))
                                {
                                    string oldValue = "";
                                    string newValue = "";

                                    if (IsPropertyForeignKey(srnProp))
                                    {
                                        if (updatePropValue != null)
                                        {
                                            newValue = GetPropertyVaueById(_dbContext, (int)updatePropValue, updateProp.Name);
                                        }
                                    }
                                    else
                                    {
                                        newValue = updatePropValue.ToString();
                                    }

                                    var stagingChange = new StagingChange
                                    {
                                        Name = _globalHelper.GetPropertyDisplayName(updateProp),
                                        OldValue = oldValue,
                                        NewValue = newValue
                                    };

                                    stagingChangeList.Add(stagingChange);
                                }
                                else if (propType.IsEnum)
                                {
                                    var newValue = _globalHelper.GetEnumValue(srnProp.Name, (int)updatePropValue);
                                    var oldValue = "";

                                    var stagingChange = new StagingChange
                                    {
                                        Name = _globalHelper.GetPropertyDisplayName(updateProp),
                                        OldValue = oldValue,
                                        NewValue = newValue
                                    };

                                    stagingChangeList.Add(stagingChange);
                                }
                            }
                        }
                    }
                }

                memberStagingChangeLog.Changes.AddRange(stagingChangeList);
                return memberStagingChangeLog;
            }

            return null;
        }

        internal int CreateSRNChangeRequest(SRN existingSRN, SRNUpdateInputDTO srnUpdateInputDTO, User user)
        {
            int changeRequestId = 0;

            if (existingSRN != null && srnUpdateInputDTO != null)
            {
                var srnChangeRequest = _dbContext.Set<ChangeRequestStaging>()
                            .AsNoTracking()
                            .FirstOrDefault(i => i.ObjectId == existingSRN.Id);

                if (srnChangeRequest != null)
                {
                    changeRequestId = srnChangeRequest.Id;

                    var changedModel = _mapper.Map(srnUpdateInputDTO, srnChangeRequest);
                    srnChangeRequest.Status = ChangeRequestStatus.NotActioned;
                    srnChangeRequest.Id = changeRequestId;
                    srnChangeRequest.DateCreated = DateTime.Now;
                    srnChangeRequest.UserId = user.Id;

                    var oldDetails = _mapper.Map<SRNUpdateInputDTO>(existingSRN);
                    srnChangeRequest.OldDetailsBlob = JsonConvert.SerializeObject(oldDetails);
                    srnChangeRequest.UpdatedDetailsBlob = JsonConvert.SerializeObject(srnUpdateInputDTO);
                    srnChangeRequest.StagingDetailsBlob = JsonConvert.SerializeObject(CreateStagingChangeLog(existingSRN, srnUpdateInputDTO));

                    _dbContext.Set<ChangeRequestStaging>().Update(srnChangeRequest);
                    _dbContext.SaveChanges();
                }
                else
                {
                    var newChangeRequest = new ChangeRequestStaging
                    {
                        Type = ChangeObjectType.SRN,
                        Status = ChangeRequestStatus.NotActioned,
                        ObjectId = existingSRN.Id,
                        DateCreated = DateTime.Now,
                        UserId = user.Id
                    };

                    var oldDetails = _mapper.Map<SRNUpdateInputDTO>(existingSRN);
                    newChangeRequest.OldDetailsBlob = JsonConvert.SerializeObject(oldDetails);
                    newChangeRequest.UpdatedDetailsBlob = JsonConvert.SerializeObject(srnUpdateInputDTO);
                    newChangeRequest.StagingDetailsBlob = JsonConvert.SerializeObject(CreateStagingChangeLog(existingSRN, srnUpdateInputDTO));

                    _dbContext.Set<ChangeRequestStaging>().Add(newChangeRequest);

                    _dbContext.SaveChanges();
                    changeRequestId = newChangeRequest.Id;
                }
            }
            return changeRequestId;
        }

        private MemberStagingChangeLogResource CreateStagingChangeLog(SRN existingSRN, SRNUpdateInputDTO srnUpdateInputDTO)
        {
            if (existingSRN != null && srnUpdateInputDTO != null)
            {
                var updateModelType = srnUpdateInputDTO.GetType();
                var oldModelType = existingSRN.GetType();
                IList<PropertyInfo> updateProperties = new List<PropertyInfo>(updateModelType.GetProperties());
                IList<PropertyInfo> oldProperties = new List<PropertyInfo>(oldModelType.GetProperties());
                MemberStagingChangeLogResource memberStagingChangeLog = new MemberStagingChangeLogResource();
                List<StagingChange> stagingChangeList = new List<StagingChange>();

                var fieldSettings = _dbContext.SRNFieldUpdateSettings.ToList();

                foreach (PropertyInfo updateProp in updateProperties)
                {

                    if (updateProp.PropertyType == typeof(DateTime?) && (updateProp.Name.StartsWith("DailyFile") || updateProp.Name.StartsWith("MonthlyFile")))
                    {
                        object updatePropValue = updateProp.GetValue(srnUpdateInputDTO, null);
                        var oldStatusUpdateProperty = oldProperties.FirstOrDefault(i => i.PropertyType == typeof(ICollection<SRNStatusUpdateHistory>));
                        var statusUpdateType = new SRNStatusUpdateHistory().GetType();
                        var oldStatusUpdateProperties = statusUpdateType.GetProperties();

                        var oldProp = oldStatusUpdateProperties.FirstOrDefault(i => i.Name == updateProp.Name);

                        if (oldProp != null)
                        {
                            SRNStatusUpdateHistory recentSRNStatusUpdate = null;

                            //If there are previous updates in the SRN Status Update History
                            if (existingSRN.SRNStatusUpdates.Count > 0)
                            {
                                recentSRNStatusUpdate = existingSRN.SRNStatusUpdates.OrderByDescending(i => i.DateCreated).FirstOrDefault();
                            }

                            if (recentSRNStatusUpdate != null)
                            {
                                object oldPropValue = oldProp.GetValue(recentSRNStatusUpdate, null);

                                if (oldPropValue != null)
                                {
                                    var propType = oldPropValue.GetType();

                                    if (propType == typeof(DateTime) || propType == typeof(DateTime?))
                                    {
                                        var updateValue = (updatePropValue != null) ? updatePropValue.ToString() : "";
                                        var oldDbValue = (oldPropValue != null) ? oldPropValue.ToString() : "";

                                        if (updateValue != oldDbValue)
                                        {
                                            var stagingChange = new StagingChange
                                            {
                                                Name = _globalHelper.GetPropertyDisplayName(updateProp),
                                                OldValue = oldDbValue,
                                                NewValue = updateValue
                                            };

                                            stagingChangeList.Add(stagingChange);
                                        }
                                    }
                                }
                            }
                            else
                            {
                                if (updatePropValue != null)
                                {
                                    var stagingChange = new StagingChange
                                    {
                                        Name = _globalHelper.GetPropertyDisplayName(updateProp),
                                        OldValue = "",
                                        NewValue = updatePropValue.ToString()
                                    };

                                    stagingChangeList.Add(stagingChange);
                                }
                            }
                        }
                    }
                    else if (updateProp.PropertyType == typeof(List<ContactUpdateDTO>))
                    {
                        foreach (var updateContactDTO in srnUpdateInputDTO.Contacts)
                        {
                            var updatedContactType = updateContactDTO.GetType();
                            var oldContact = existingSRN.Contacts.FirstOrDefault(i => i.Id == updateContactDTO.Id);
                            var oldContactType = (oldContact != null) ? oldContact.GetType() : new SRNContact().GetType();

                            var updatedContactProperties = updatedContactType.GetProperties();
                            var oldContactProperties = oldContactType.GetProperties();

                            foreach (var contactUpdateProp in updatedContactProperties)
                            {
                                object updatePropValue = contactUpdateProp.GetValue(updateContactDTO, null);

                                var fieldUpdateSetting = fieldSettings.FirstOrDefault(i => i.FieldUpdateName == contactUpdateProp.Name);
                                string fieldName = (fieldUpdateSetting != null) ? fieldUpdateSetting.FieldName : "";

                                var oldProp = oldContactProperties.FirstOrDefault(i => i.Name == fieldName);
                                if (oldProp != null)
                                {
                                    object oldPropValue = (oldContact != null) ? oldProp.GetValue(oldContact, null) : "";

                                    if (oldPropValue != null)
                                    {
                                        var propType = oldPropValue.GetType();
                                        if (propType.IsPrimitive || propType == (typeof(System.String)))
                                        {
                                            var updateValue = (updatePropValue != null) ? updatePropValue.ToString() : "";
                                            var oldDbValue = (oldPropValue != null) ? oldPropValue.ToString() : "";

                                            if (updateValue != oldDbValue)
                                            {
                                                string oldValue = "";
                                                string newValue = "";

                                                if (IsPropertyForeignKey(oldProp))
                                                {
                                                    oldValue = (oldPropValue != null) ? GetPropertyVaueById(_dbContext, (int)oldPropValue, oldProp.Name) : "";
                                                    newValue = (updatePropValue != null) ? GetPropertyVaueById(_dbContext, (int)updatePropValue, updateProp.Name) : "";
                                                }

                                                else
                                                {
                                                    oldValue = (oldPropValue != null) ? oldPropValue.ToString() : "";
                                                    newValue = (updatePropValue != null) ? updatePropValue.ToString() : "";
                                                }

                                                var stagingChange = new StagingChange
                                                {
                                                    Name = _globalHelper.GetPropertyDisplayName(contactUpdateProp),
                                                    OldValue = oldValue,
                                                    NewValue = newValue
                                                };

                                                stagingChangeList.Add(stagingChange);
                                            }
                                        }
                                        else if (propType.IsEnum)
                                        {
                                            if ((int)updatePropValue != (int)oldPropValue)
                                            {
                                                var newValue = _globalHelper.GetPropertyDisplayName(contactUpdateProp);
                                                var oldValue = _globalHelper.GetPropertyDisplayName(oldProp);

                                                var stagingChange = new StagingChange
                                                {
                                                    Name = _globalHelper.GetPropertyDisplayName(contactUpdateProp),
                                                    OldValue = oldValue,
                                                    NewValue = newValue
                                                };

                                                stagingChangeList.Add(stagingChange);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                    else if (updateProp.PropertyType == typeof(List<BranchLocationUpdateResource>))
                    {
                        foreach (var updateBranchLocationDTO in srnUpdateInputDTO.BranchLocations)
                        {
                            var updatedBranchLocationType = updateBranchLocationDTO.GetType();
                            var oldBranchLocation = existingSRN.BranchLocations.FirstOrDefault(i => i.Id == updateBranchLocationDTO.Id);
                            var oldContactType = (oldBranchLocation != null) ? oldBranchLocation.GetType() : new BranchLocation().GetType();

                            var updatedBranchLocationProperties = updatedBranchLocationType.GetProperties();
                            var oldBranchLocationProperties = oldContactType.GetProperties();

                            foreach (var branchLocationUpdateProp in updatedBranchLocationProperties)
                            {
                                if (branchLocationUpdateProp.Name != "Id" && branchLocationUpdateProp.Name != "SRNId")
                                {
                                    object updatePropValue = branchLocationUpdateProp.GetValue(updateBranchLocationDTO, null);

                                    var fieldUpdateSetting = fieldSettings.FirstOrDefault(i => i.FieldUpdateName == branchLocationUpdateProp.Name);
                                    string fieldName = (fieldUpdateSetting != null) ? fieldUpdateSetting.FieldName : "";

                                    var oldProp = oldBranchLocationProperties.FirstOrDefault(i => i.Name == fieldName);
                                    if (oldProp != null)
                                    {
                                        object oldPropValue = (oldBranchLocation != null) ? oldProp.GetValue(oldBranchLocation, null) : "";

                                        if (oldPropValue != null)
                                        {
                                            var propType = oldPropValue.GetType();
                                            if (propType.IsPrimitive || propType == typeof(string))
                                            {
                                                var updateValue = (updatePropValue != null) ? updatePropValue.ToString() : "";
                                                var oldDbValue = (oldPropValue != null) ? oldPropValue.ToString() : "";

                                                if (updateValue != oldDbValue)
                                                {
                                                    string oldValue = "";
                                                    string newValue = "";

                                                    if (IsPropertyForeignKey(oldProp))
                                                    {
                                                        oldValue = (oldPropValue != null) ? GetPropertyVaueById(_dbContext, (int)oldPropValue, oldProp.Name) : "";
                                                        newValue = (updatePropValue != null) ? GetPropertyVaueById(_dbContext, (int)updatePropValue, updateProp.Name) : "";
                                                    }

                                                    else
                                                    {
                                                        oldValue = (oldPropValue != null) ? oldPropValue.ToString() : "";
                                                        newValue = (updatePropValue != null) ? updatePropValue.ToString() : "";
                                                    }

                                                    var stagingChange = new StagingChange
                                                    {
                                                        Name = _globalHelper.GetPropertyDisplayName(branchLocationUpdateProp),
                                                        OldValue = oldValue,
                                                        NewValue = newValue
                                                    };

                                                    stagingChangeList.Add(stagingChange);
                                                }
                                            }
                                            else if (propType.IsEnum)
                                            {
                                                if ((int)updatePropValue != (int)oldPropValue)
                                                {
                                                    var newValue = _globalHelper.GetPropertyDisplayName(branchLocationUpdateProp);
                                                    var oldValue = _globalHelper.GetPropertyDisplayName(oldProp);

                                                    var stagingChange = new StagingChange
                                                    {
                                                        Name = _globalHelper.GetPropertyDisplayName(branchLocationUpdateProp),
                                                        OldValue = oldValue,
                                                        NewValue = newValue
                                                    };

                                                    stagingChangeList.Add(stagingChange);
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    else
                    {
                        object updatePropValue = updateProp.GetValue(srnUpdateInputDTO, null);

                        var fieldUpdateSetting = fieldSettings.FirstOrDefault(i => i.FieldUpdateName == updateProp.Name);
                        string fieldName = (fieldUpdateSetting != null) ? fieldUpdateSetting.FieldName : "";

                        var oldProp = oldProperties.FirstOrDefault(i => i.Name == fieldName);
                        if (oldProp != null)
                        {
                            object oldPropValue = oldProp.GetValue(existingSRN, null);

                            if (oldPropValue != null)
                            {
                                var propType = oldPropValue.GetType();
                                if (propType.IsPrimitive || propType == (typeof(System.String)))
                                {
                                    var updateValue = (updatePropValue != null) ? updatePropValue.ToString() : "";
                                    var oldDbValue = (oldPropValue != null) ? oldPropValue.ToString() : "";

                                    if (updateValue != oldDbValue)
                                    {
                                        string oldValue = "";
                                        string newValue = "";

                                        if (IsPropertyForeignKey(oldProp))
                                        {
                                            oldValue = (oldPropValue != null) ? GetPropertyVaueById(_dbContext, (int)oldPropValue, oldProp.Name) : "";
                                            newValue = (updatePropValue != null) ? GetPropertyVaueById(_dbContext, (int)updatePropValue, updateProp.Name) : "";
                                        }

                                        else
                                        {
                                            oldValue = (oldPropValue != null) ? oldPropValue.ToString() : "";
                                            newValue = (updatePropValue != null) ? updatePropValue.ToString() : "";
                                        }

                                        var stagingChange = new StagingChange
                                        {
                                            Name = _globalHelper.GetPropertyDisplayName(updateProp),
                                            OldValue = oldValue,
                                            NewValue = newValue
                                        };

                                        stagingChangeList.Add(stagingChange);
                                    }
                                }
                                else if (propType.IsEnum)
                                {
                                    if ((int)updatePropValue != (int)oldPropValue)
                                    {
                                        var newValue = _globalHelper.GetPropertyDisplayName(updateProp);
                                        var oldValue = _globalHelper.GetPropertyDisplayName(oldProp);

                                        var stagingChange = new StagingChange
                                        {
                                            Name = _globalHelper.GetPropertyDisplayName(updateProp),
                                            OldValue = oldValue,
                                            NewValue = newValue
                                        };

                                        stagingChangeList.Add(stagingChange);
                                    }
                                }
                            }
                            else
                            {
                                var oldDbValue = "";
                                var updateValue = (updatePropValue != null) ? updatePropValue.ToString() : "";

                                if (updateValue != oldDbValue)
                                {
                                    string oldValue = "";
                                    string newValue = "";

                                    if (IsPropertyForeignKey(oldProp))
                                    {
                                        newValue = (updatePropValue != null) ? GetPropertyVaueById(_dbContext, (int)updatePropValue, updateProp.Name) : "";
                                    }

                                    else
                                    {
                                        newValue = (updatePropValue != null) ? updatePropValue.ToString() : "";
                                    }

                                    var stagingChange = new StagingChange
                                    {
                                        Name = _globalHelper.GetPropertyDisplayName(updateProp),
                                        OldValue = oldValue,
                                        NewValue = newValue
                                    };

                                    stagingChangeList.Add(stagingChange);
                                }
                            }
                        }
                    }
                }

                memberStagingChangeLog.Changes = stagingChangeList;
                return memberStagingChangeLog;
            }

            return null;
        }

        public void ApplySRNChanges(int existingSrnId, SRNUpdateInputDTO srnUpdateInputDTO, User user)
        {
            var srn = _dbContext.SRNs
                .Include(i => i.Member)
                .Include(i => i.Contacts)
                .Include(i => i.BranchLocations)
                .Include(i => i.SRNStatus)
                .Include(i => i.SRNStatusUpdates)
                .AsNoTracking()
                .FirstOrDefault(i => i.Id == existingSrnId);

            if (srn == null)
            {
                throw new Exception($"SRN with ID {existingSrnId} not found.");
            }

            var stagingChangeLog = new MemberStagingChangeLogResource();
            bool dateChangesAllowed = true;
            CreateSRNChangeLog(srnUpdateInputDTO, srn, stagingChangeLog);

            UpdateSRNContacts(srn, srnUpdateInputDTO, stagingChangeLog);
            UpdateSRNBranchLocations(srn, srnUpdateInputDTO, stagingChangeLog);

            srnUpdateInputDTO.LoanManagementSystemId = (srnUpdateInputDTO.LoanManagementSystemId > 0) ? srnUpdateInputDTO.LoanManagementSystemId : null;
            srnUpdateInputDTO.ThirdPartyVendorId = (srnUpdateInputDTO.ThirdPartyVendorId > 0) ? srnUpdateInputDTO.ThirdPartyVendorId : null;
            srnUpdateInputDTO.CreditInformationClassificationId = (srnUpdateInputDTO.CreditInformationClassificationId > 0) ? srnUpdateInputDTO.CreditInformationClassificationId : null;
            srnUpdateInputDTO.NCRReportingAccountTypeClassificationId = (srnUpdateInputDTO.NCRReportingAccountTypeClassificationId > 0) ? srnUpdateInputDTO.NCRReportingAccountTypeClassificationId : null;
            srnUpdateInputDTO.UserId = user.Id;

            if (srn.SRNStatusUpdates.Count > 0)
            {
                //Lets get the last two (recent) SRN files
                var srnStatusUpdates = srn.SRNStatusUpdates
                    .OrderByDescending(i => i.DateCreated)
                    .Take(2)
                    .ToList();

                //If update has both files but the DB has only one file, create the new file
                if (srnUpdateInputDTO.FileType == SRNStatusFileTypes.MonthlyAndDailyFile && srnStatusUpdates.Count == 1)
                {
                    if (user.RoleId != UserRoles.SACRRAAdministrator
                        && srn.SRNStatus.Name == "Live")
                    {
                        dateChangesAllowed = false;
                    }

                    if (!dateChangesAllowed)
                    {
                        throw new SRNUpdateNotAllowedException(0, "SRN Date changes not allowed for this user.", null);
                    }

                    if (dateChangesAllowed)
                    {
                        var updateHistory = srnStatusUpdates.FirstOrDefault();
                        var existingFileType = updateHistory.FileType;
                        SRNStatusFileTypes? newFileType = null;

                        if (existingFileType == SRNStatusFileTypes.DailyFile)
                        {
                            newFileType = SRNStatusFileTypes.MonthlyFile;
                        }
                        else if (existingFileType == SRNStatusFileTypes.MonthlyFile)
                        {
                            newFileType = SRNStatusFileTypes.DailyFile;
                        }

                        if (newFileType != null)
                        {
                            srn.SRNStatusUpdates.Add(new SRNStatusUpdateHistory
                            {
                                FileType = newFileType,
                                DailyFileDevelopmentStartDate = srnUpdateInputDTO.DailyFileDevelopmentStartDate,
                                DailyFileDevelopmentEndDate = srnUpdateInputDTO.DailyFileDevelopmentEndDate,
                                DailyFileTestStartDate = srnUpdateInputDTO.DailyFileTestStartDate,
                                DailyFileTestEndDate = srnUpdateInputDTO.DailyFileTestEndDate,
                                DailyFileGoLiveDate = srnUpdateInputDTO.DailyFileGoLiveDate,

                                MonthlyFileDevelopmentStartDate = srnUpdateInputDTO.MonthlyFileDevelopmentStartDate,
                                MonthlyFileDevelopmentEndDate = srnUpdateInputDTO.MonthlyFileDevelopmentEndDate,
                                MonthlyFileTestStartDate = srnUpdateInputDTO.MonthlyFileTestStartDate,
                                MonthlyFileTestEndDate = srnUpdateInputDTO.MonthlyFileTestEndDate,
                                MonthlyFileGoLiveDate = srnUpdateInputDTO.MonthlyFileGoLiveDate,
                                DateCreated = DateTime.Now,
                                SRNId = srn.Id,
                                SRNStatusReasonId = srn.SRNStatusReasonId,
                                SRNStatusId = srn.SRNStatusId,
                                IsComple = true,
                                DateCompleted = DateTime.Now,
                                UpdateNumber = updateHistory.UpdateNumber
                            });
                        }

                        //Update main SRN file
                        srn.FileType = SRNStatusFileTypes.MonthlyAndDailyFile;
                    }
                }

                foreach (var updateHistory in srnStatusUpdates)
                {
                    var currentTestEndDate = (updateHistory.FileType == SRNStatusFileTypes.MonthlyFile) ? updateHistory.MonthlyFileTestEndDate : (updateHistory.FileType == SRNStatusFileTypes.DailyFile) ? updateHistory.DailyFileTestEndDate : null;

                    if (user.RoleId != UserRoles.SACRRAAdministrator
                        && srn.SRNStatus.Name == "Live"
                        && user.FirstName != "Camunda"
                        && user.LastName != "User")
                    {
                        dateChangesAllowed = false;
                    }

                    if (!dateChangesAllowed)
                    {
                        throw new SRNUpdateNotAllowedException(0, "SRN Date changes not allowed for this user.", null);
                    }

                    stagingChangeLog = CreateSRNStatusUpdateHistoryEventLog(srnUpdateInputDTO, updateHistory, stagingChangeLog, user);

                    if (dateChangesAllowed)
                    {
                        UpdateSRNStatusHistory(updateHistory, srnUpdateInputDTO);

                        //Update camunda variables
                        if (!updateHistory.IsComple)
                        {
                            var testEndDate = "";
                            var goLiveDate = "";

                            if (updateHistory.FileType == SRNStatusFileTypes.DailyFile)
                            {
                                if (updateHistory.DailyFileTestEndDate > DateTime.Now)
                                {
                                    testEndDate = updateHistory.DailyFileTestEndDate.Value.AddDays(-3).ToString("yyyy-MM-dd");
                                    goLiveDate = updateHistory.DailyFileGoLiveDate.Value.AddDays(-3).ToString("yyyy-MM-dd");
                                }
                                else
                                {
                                    testEndDate = (updateHistory.DailyFileTestEndDate != null) ? updateHistory.DailyFileTestEndDate.Value.ToString("yyyy-MM-dd") : "";
                                    goLiveDate = (updateHistory.DailyFileGoLiveDate != null) ? updateHistory.DailyFileGoLiveDate.Value.ToString("yyyy-MM-dd") : "";
                                }
                            }
                            else if (updateHistory.FileType == SRNStatusFileTypes.MonthlyFile)
                            {
                                if (updateHistory.MonthlyFileTestEndDate > DateTime.Now)
                                {
                                    testEndDate = updateHistory.MonthlyFileTestEndDate.Value.AddDays(-3).ToString("yyyy-MM-dd");
                                    goLiveDate = updateHistory.MonthlyFileGoLiveDate.Value.AddDays(-3).ToString("yyyy-MM-dd");
                                }
                                else
                                {
                                    testEndDate = (updateHistory.MonthlyFileTestEndDate != null) ? updateHistory.MonthlyFileTestEndDate.Value.ToString("yyyy-MM-dd") : "";
                                    goLiveDate = (updateHistory.MonthlyFileGoLiveDate != null) ? updateHistory.MonthlyFileGoLiveDate.Value.ToString("yyyy-MM-dd") : "";
                                }
                            }

                            if (!string.IsNullOrEmpty(testEndDate))
                            {
                                if (Convert.ToDateTime(testEndDate).Date <= DateTime.Now.Date)
                                {
                                    _camundaRepository.ExecuteTimerEvents(new List<string> { updateHistory.ProcessInstanceId });
                                }
                            }

                            if ((srnUpdateInputDTO.MonthlyFileTestEndDate.HasValue && currentTestEndDate.HasValue
                                 && srnUpdateInputDTO.MonthlyFileTestEndDate.Value.Date != currentTestEndDate.Value.Date)
                            || (srnUpdateInputDTO.DailyFileTestEndDate.HasValue && currentTestEndDate.HasValue
                                    && srnUpdateInputDTO.DailyFileTestEndDate.Value.Date != currentTestEndDate.Value.Date))
                            {
                                var usertasks = GetTasksByBusinessKey(updateHistory.ProcessInstanceId, "SRN-Status-Update-To-Test", updateHistory.SRN.SRNNumber);

                                if (usertasks != null)
                                {
                                    if (usertasks.Count == 0)
                                    {
                                        usertasks = GetTasksByBusinessKey(updateHistory.ProcessInstanceId, "New-SRN-Application", updateHistory.SRN.SRNNumber);
                                    }
                                }

                                if (usertasks != null)
                                {
                                    if (usertasks.Count > 0)
                                    {
                                        foreach (var task in usertasks)
                                        {
                                            DateTime? endDate = null;
                                            DateTime? liveDate = null;

                                            if (updateHistory.FileType == SRNStatusFileTypes.MonthlyFile)
                                            {
                                                endDate = srnUpdateInputDTO.MonthlyFileTestEndDate;
                                                liveDate = srnUpdateInputDTO.MonthlyFileGoLiveDate;
                                            }
                                            else if (updateHistory.FileType == SRNStatusFileTypes.DailyFile)
                                            {
                                                endDate = srnUpdateInputDTO.DailyFileTestEndDate;
                                                liveDate = srnUpdateInputDTO.DailyFileGoLiveDate;
                                            }

                                            var confirmation = new TaskConfirmSRNTestingCompleteResource
                                            {
                                                TestEndDate = endDate,
                                                GoLiveDate = liveDate
                                            };

                                            using var client = new HttpClient();
                                            var variables = string.Empty;
                                            var uri = string.Empty;

                                            uri = _configSettings.CamundaBaseAddress + "/task?processInstanceId=" + task.Id;

                                            try
                                            {
                                                var result = client.Send(new HttpRequestMessage(HttpMethod.Get, uri));
                                                result.EnsureSuccessStatusCode();

                                                var resultString = result.Content.ReadAsString();
                                                var taskResource = JsonConvert.DeserializeObject<List<TaskGetResource>>(resultString);
                                                if (taskResource != null && taskResource.Count > 0)
                                                {
                                                    _camundaRepository.CompleteBuyerStakeHolderManagerSRNApplication_ConfirmSRNTesting(taskResource[0].Id, confirmation);
                                                }
                                            }
                                            catch (Exception ex)
                                            {
                                                throw new Exception( $"Failed to fetch or complete Camunda task for processInstanceId={updateHistory.ProcessInstanceId}");
                                                
                                            }
                                        }
                                    }
                                }
                            }


                            UpdateCamundaVariables(updateHistory.ProcessInstanceId, "testEndDate", testEndDate, "String");
                            UpdateCamundaVariables(updateHistory.ProcessInstanceId, "goLiveDate", goLiveDate, "String");
                        }
                    }
                }
            }

            var model = _mapper.Map(srnUpdateInputDTO, srn);

            if (dateChangesAllowed)
            {
                //If the SRN has no dates that were not catured before, create the SRN files with dates
                if (srn.SRNStatusUpdates.Count == 0)
                {
                    var srnStatusUpdates = CreateSRNStatusUpdatesV2(srnUpdateInputDTO);
                    foreach (var item in srnStatusUpdates)
                    {
                        item.SRNId = model.Id;
                        item.SRNStatusReasonId = model.SRNStatusReasonId;
                        item.SRNStatusId = model.SRNStatusId;
                        item.IsComple = true;
                        item.DateCompleted = DateTime.Now;
                        srn.SRNStatusUpdates.Add(item);
                    }
                }
            }

            //DO NOT update these fields
            model.SRNNumber = srn.SRNNumber;
            model.MemberId = srn.MemberId;
            model.SRNStatusReasonId = srn.SRNStatusReasonId;
            model.SRNStatusId = srn.SRNStatusId;
            model.AccountStatusDate = srn.AccountStatusDate;
            model.CreationDate = srn.CreationDate;
            model.StatusLastUpdatedAt = srn.StatusLastUpdatedAt;

            //_globalHelper.PrepareSRNForUpdate(_dbContext, model);
            _dbContext.Set<SRN>().Update(model);
            _dbContext.SaveChanges();
           

            var updateDetailsBlob = JsonConvert.SerializeObject(srnUpdateInputDTO);
            var stagingDetailsBlob = JsonConvert.SerializeObject(stagingChangeLog);

            if (stagingChangeLog.Changes.Count > 0)
            {
                _globalHelper.CreateEventLog(_dbContext, srnUpdateInputDTO.UserId, "SRN Update", srn.TradingName, updateDetailsBlob, stagingDetailsBlob, srn.Id, "SRN");
            }

        }

        private MemberStagingChangeLogResource CreateSRNChangeLog(SRNUpdateInputDTO srnUpdateInputDTO, SRN existingSrn, MemberStagingChangeLogResource stagingChangeLog)
        {
            if (srnUpdateInputDTO != null)
            {
                var updateModelType = srnUpdateInputDTO.GetType();
                var srnModelType = existingSrn.GetType();
                IList<PropertyInfo> updateProperties = new List<PropertyInfo>(updateModelType.GetProperties());
                IList<PropertyInfo> srnProperties = new List<PropertyInfo>(srnModelType.GetProperties());
                List<StagingChange> stagingChangeList = new List<StagingChange>();

                foreach (PropertyInfo updateProp in updateProperties)
                {
                    object updatePropValue = updateProp.GetValue(srnUpdateInputDTO, null);

                    var propAssociation = _globalHelper.GetPropertyAssociationName(updateProp);

                    var srnProp = srnProperties.FirstOrDefault(i => i.Name == (!string.IsNullOrEmpty(propAssociation) ? propAssociation : updateProp.Name));

                    if (srnProp != null)
                    {
                        //Primary keys don't get updated
                        if (srnProp.Name != "MemberId" && srnProp.Name != "SRNId")
                        {
                            object srnPropValue = srnProp.GetValue(existingSrn, null);
                            if (srnPropValue != null)
                            {
                                var propType = srnPropValue.GetType();
                                if (propType.IsPrimitive || propType == (typeof(System.String)))
                                {
                                    string oldValue = "";
                                    string newValue = "";

                                    if (updatePropValue == null)
                                        newValue = "";
                                    else
                                        newValue = updatePropValue.ToString();
                                    if (srnPropValue == null)
                                        oldValue = "";
                                    else
                                        oldValue = srnPropValue.ToString();

                                    if (newValue != oldValue)
                                    {
                                        var propGroup = _globalHelper.GetPropertyGroup(srnProp);

                                        //Foreign Keys
                                        if (propGroup == "ForeignKey")
                                        {
                                            if (!string.IsNullOrEmpty(oldValue))
                                                oldValue = _globalHelper.GetPropertyVaueById(_dbContext, (int)srnPropValue, srnProp.Name);
                                            if (!string.IsNullOrEmpty(newValue))
                                                newValue = _globalHelper.GetPropertyVaueById(_dbContext, (int)updatePropValue, srnProp.Name);
                                        }
                                        else
                                        {
                                            oldValue = srnPropValue.ToString();
                                            newValue = (updatePropValue != null) ? updatePropValue.ToString() : "";
                                        }

                                        var stagingChange = new StagingChange
                                        {
                                            Name = _globalHelper.GetPropertyDisplayName(updateProp),
                                            OldValue = oldValue,
                                            NewValue = newValue
                                        };

                                        stagingChangeList.Add(stagingChange);
                                    }
                                }
                                else if (propType.IsEnum)
                                {
                                    if ((int)updatePropValue != (int)srnPropValue)
                                    {
                                        var newValue = _globalHelper.GetEnumValue(srnProp.Name, (int)updatePropValue);
                                        var oldValue = _globalHelper.GetEnumValue(srnProp.Name, (int)srnPropValue);

                                        var stagingChange = new StagingChange
                                        {
                                            Name = _globalHelper.GetPropertyDisplayName(updateProp),
                                            OldValue = oldValue,
                                            NewValue = newValue
                                        };

                                        stagingChangeList.Add(stagingChange);
                                    }
                                }
                            }
                        }
                    }
                }

                stagingChangeLog.Changes.AddRange(stagingChangeList);
                return stagingChangeLog;
            }

            return null;
        }

        private void UpdateCamundaVariables(string taskId, string name, string value, string type)
        {
            using (var client = new HttpClient())
            {
                var tasks = GetTasksByProcessInstanceId(taskId, "SRN-Status-Update-To-Test");

                if (tasks != null)
                {
                    if (tasks.Count == 0)
                    {
                        tasks = GetTasksByProcessInstanceId(taskId, "New-SRN-Application");
                    }
                }

                if (tasks != null)
                {
                    if (tasks.Count > 0)
                    {
                        var variableValue = new { value = value, type = type };

                        var json = JsonConvert.SerializeObject(variableValue);
                        var content = new StringContent(json, Encoding.UTF8, "application/json");
                        var uri = _configSettings.CamundaBaseAddress + "/task/" + tasks[0].Id + "/variables/" + name;
                        var result = client.PutAsync(uri, content);
                        result.EnsureSuccessStatusCode();
                    }
                }
            }
        }

        private async Task<List<TaskGetResource>> GetTasksByProcessInstanceId(string processInstanceId, string processDefinitionKey, Dictionary<string, string> taskVariables = null)
        {
            using (var client = new HttpClient())
            {
                string variables = string.Empty;
                string uri = string.Empty;

                if (taskVariables != null)
                {
                    if (taskVariables.Count > 0)
                    {
                        foreach (var var in taskVariables)
                        {
                            variables += var.Key + "_eq_" + var.Value;
                        }
                    }
                }

                if (!string.IsNullOrEmpty(variables))
                {
                    uri = _configSettings.CamundaBaseAddress + "/task?processInstanceId=" + processInstanceId + "&processDefinitionKey=" + processDefinitionKey + "&taskVariables=" + variables;
                }
                else
                {
                    uri = _configSettings.CamundaBaseAddress + "/task?processInstanceId=" + processInstanceId + "&processDefinitionKey=" + processDefinitionKey;
                }

                var result = client.Send(new HttpRequestMessage(HttpMethod.Get, uri));
                result.EnsureSuccessStatusCode();

                var resultString = result.Content.ReadAsString();
                var taskResource = JsonConvert.DeserializeObject<List<TaskGetResource>>(resultString);

                return taskResource;
            }
        }

        private async Task<List<TaskGetResource>> GetTasksByBusinessKey(string processInstanceId, string processDefinitionKey, string businessKey)
        {
            using var client = new HttpClient();
            var variables = string.Empty;
            var uri = string.Empty;

            uri = _configSettings.CamundaBaseAddress + "/process-instance?businessKey=" + businessKey;

            var result = client.Send(new HttpRequestMessage(HttpMethod.Get, uri));
            result.EnsureSuccessStatusCode();

            var resultString = result.Content.ReadAsString();
            var taskResource = JsonConvert.DeserializeObject<List<TaskGetResource>>(resultString);

            return taskResource;
        }


        private void UpdateSRNStatusHistory(SRNStatusUpdateHistory updateHistory, SRNUpdateInputDTO srnUpdateInputDTO)
        {
            if (updateHistory != null && srnUpdateInputDTO != null)
            {
                updateHistory.DailyFileDevelopmentStartDate = srnUpdateInputDTO.DailyFileDevelopmentStartDate;
                updateHistory.DailyFileDevelopmentEndDate = srnUpdateInputDTO.DailyFileDevelopmentEndDate;
                updateHistory.DailyFileTestStartDate = srnUpdateInputDTO.DailyFileTestStartDate;
                updateHistory.DailyFileTestEndDate = srnUpdateInputDTO.DailyFileTestEndDate;
                updateHistory.DailyFileGoLiveDate = srnUpdateInputDTO.DailyFileGoLiveDate;

                updateHistory.MonthlyFileDevelopmentStartDate = srnUpdateInputDTO.MonthlyFileDevelopmentStartDate;
                updateHistory.MonthlyFileDevelopmentEndDate = srnUpdateInputDTO.MonthlyFileDevelopmentEndDate;
                updateHistory.MonthlyFileTestStartDate = srnUpdateInputDTO.MonthlyFileTestStartDate;
                updateHistory.MonthlyFileTestEndDate = srnUpdateInputDTO.MonthlyFileTestEndDate;
                updateHistory.MonthlyFileGoLiveDate = srnUpdateInputDTO.MonthlyFileGoLiveDate;

                _dbContext.Update(updateHistory);
            }
        }

        private MemberStagingChangeLogResource CreateSRNStatusUpdateHistoryEventLog(SRNUpdateInputDTO srnUpdateInputDTO, SRNStatusUpdateHistory updateHistory, MemberStagingChangeLogResource stagingChangeLog, User user)
        {
            if (srnUpdateInputDTO != null)
            {
                var updateModelType = srnUpdateInputDTO.GetType();
                var srnModelType = updateHistory.GetType();
                IList<PropertyInfo> updateProperties = new List<PropertyInfo>(updateModelType.GetProperties());
                IList<PropertyInfo> srnProperties = new List<PropertyInfo>(srnModelType.GetProperties());
                List<StagingChange> stagingChangeList = new List<StagingChange>();

                foreach (PropertyInfo updateProp in updateProperties)
                {
                    object updatePropValue = updateProp.GetValue(srnUpdateInputDTO, null);

                    var srnProp = srnProperties.FirstOrDefault(i => i.Name == updateProp.Name);
                    if (srnProp != null)
                    {
                        //Primary keys don't get updated
                        if (srnProp.Name != "MemberId" && srnProp.Name != "SRNId")
                        {
                            object srnPropValue = srnProp.GetValue(updateHistory, null);

                            if (srnPropValue != null)
                            {
                                var propType = srnPropValue.GetType();
                                if (srnProp.DeclaringType == typeof(SRNDatesShared))
                                {
                                    string oldValue = "";
                                    string newValue = "";

                                    if (updatePropValue == null)
                                        newValue = "";
                                    else
                                        newValue = updatePropValue.ToString();
                                    if (srnPropValue == null)
                                        oldValue = "";
                                    else
                                        oldValue = srnPropValue.ToString();

                                    if (newValue != oldValue)
                                    {
                                        oldValue = srnPropValue.ToString();
                                        newValue = updatePropValue.ToString();

                                        var stagingChange = new StagingChange
                                        {
                                            Name = _globalHelper.GetPropertyDisplayName(updateProp),
                                            OldValue = oldValue,
                                            NewValue = newValue
                                        };

                                        stagingChangeList.Add(stagingChange);
                                    }
                                }
                            }
                        }
                    }
                }

                stagingChangeLog.Changes.AddRange(stagingChangeList);
            }

            return stagingChangeLog;
        }

        private void CreateSRNStatusUpdateHistoryEventLogForSHM(SRNUpdateInputDTO srnUpdateInputDTO, SRNStatusUpdateHistory updateHistory, MemberStagingChangeLogResource stagingChangeLog)
        {
            if (srnUpdateInputDTO != null)
            {
                var updateModelType = srnUpdateInputDTO.GetType();
                var srnModelType = updateHistory.GetType();
                IList<PropertyInfo> updateProperties = new List<PropertyInfo>(updateModelType.GetProperties());
                IList<PropertyInfo> srnProperties = new List<PropertyInfo>(srnModelType.GetProperties());
                List<StagingChange> stagingChangeList = new List<StagingChange>();

                foreach (PropertyInfo updateProp in updateProperties)
                {
                    object updatePropValue = updateProp.GetValue(srnUpdateInputDTO, null);

                    var srnProp = srnProperties.FirstOrDefault(i => i.Name == updateProp.Name);
                    if (srnProp != null)
                    {
                        //Primary keys don't get updated
                        if (srnProp.Name != "MemberId" && srnProp.Name != "SRNId")
                        {
                            object srnPropValue = srnProp.GetValue(updateHistory, null);

                            if (srnPropValue != null)
                            {
                                var propType = srnPropValue.GetType();
                                if (srnProp.DeclaringType == typeof(SRNDatesShared))
                                {
                                    string oldValue = "";
                                    string newValue = "";

                                    if (updatePropValue == null)
                                        newValue = "";
                                    else
                                        newValue = updatePropValue.ToString();
                                    if (srnPropValue == null)
                                        oldValue = "";
                                    else
                                        oldValue = srnPropValue.ToString();

                                    if (newValue != oldValue)
                                    {
                                        oldValue = srnPropValue.ToString();
                                        newValue = updatePropValue.ToString();

                                        var stagingChange = new StagingChange
                                        {
                                            Name = _globalHelper.GetPropertyDisplayName(updateProp),
                                            OldValue = oldValue,
                                            NewValue = newValue
                                        };

                                        stagingChangeList.Add(stagingChange);
                                    }
                                }
                            }
                        }
                    }
                }

                stagingChangeLog.Changes.AddRange(stagingChangeList);
            }
        }

        private void UpdateSRNBranchLocations(SRN existingSrn, SRNUpdateInputDTO srnUpdateInputDTO, MemberStagingChangeLogResource stagingChangeLog)
        {
            if (srnUpdateInputDTO != null)
            {
                if (srnUpdateInputDTO.BranchLocations != null)
                {
                    //Add new branch locations
                    var newBranchLocations = new List<BranchLocationUpdateResource>();
                    foreach (var branch in srnUpdateInputDTO.BranchLocations)
                    {
                        if (!existingSrn.BranchLocations.Any(i => i.Id == branch.Id && i.Id > 0))
                        {
                            newBranchLocations.Add(branch);
                            AddChangeIfDifferent("SRN Branch Location", "", branch.Value, stagingChangeLog);
                            existingSrn.BranchLocations.Add(new BranchLocation { Name = branch.Value });
                        }

                        //update existing one
                        else
                        {
                            var existingBranch = existingSrn.BranchLocations
                                .FirstOrDefault(i => i.Id == branch.Id);

                            if (existingBranch.Name != branch.Value)
                            {
                                AddChangeIfDifferent("SRN Branch Location", existingBranch.Name, branch.Value, stagingChangeLog);
                            }
                            existingBranch.Name = branch.Value;
                        }
                    }
                }
            }

            //Delete all branch locations that were removed from the update recource
            var deletedBranchLocations = new List<BranchLocation>();

            foreach (var location in existingSrn.BranchLocations.Where(i => i.Id > 0))
            {
                var locationFound = srnUpdateInputDTO.BranchLocations
                    .FirstOrDefault(i => i.Id == location.Id);

                if (locationFound == null)
                    deletedBranchLocations.Add(location);
            }

            if (deletedBranchLocations.Count > 0)
            {
                _dbContext.BranchLocations.RemoveRange(deletedBranchLocations);
            }

            _dbContext.SaveChanges();
        }

        private void AddChangeIfDifferent(string fieldName, string oldValue, string newValue, MemberStagingChangeLogResource log)
        {
            if (!string.Equals(oldValue?.Trim(), newValue?.Trim(), StringComparison.OrdinalIgnoreCase))
            {
                log.Changes.Add(new StagingChange
                {
                    Name = fieldName,
                    OldValue = oldValue,
                    NewValue = newValue
                });
            }
        }
        private void UpdateSRNContacts(SRN existingSRN, SRNUpdateInputDTO srnUpdateInputDTO, MemberStagingChangeLogResource stagingChangeLog)
        {
            //Add new trading names and update existing ones
            var lstExistingContacts = new List<SRNContact>();
            if (srnUpdateInputDTO != null)
            {
                if (srnUpdateInputDTO.Contacts != null)
                {
                    var newContacts = new List<ContactUpdateDTO>();

                    foreach (var contact in srnUpdateInputDTO.Contacts)
                    {
                        var existingContact = existingSRN.Contacts
                               .FirstOrDefault(i => i.Id == contact.Id);
                        if (existingContact == null)
                        {
                            newContacts.Add(contact);
                            contact.SRNId = existingSRN.Id;
                            AddChangeIfDifferent("Contact First Name", "", contact.FirstName, stagingChangeLog);
                            AddChangeIfDifferent("Contact Surname", "", contact.LastName, stagingChangeLog);
                            AddChangeIfDifferent("Contact Job Title", "", contact.Designation, stagingChangeLog);
                            AddChangeIfDifferent("Contact - Contact Type", "", contact.ContactTypeId.ToString(), stagingChangeLog);
                            AddChangeIfDifferent("Contact Email", "", contact.EmailAddress, stagingChangeLog);
                            AddChangeIfDifferent("Contact Cell Number", "", contact.CellphoneNumber, stagingChangeLog);
                            AddChangeIfDifferent("Contact Office Tel Number", "", contact.OfficeNumber, stagingChangeLog);
                        }

                        else
                        {
                            AddChangeIfDifferent("Contact First Name", existingContact.FirstName, contact.FirstName, stagingChangeLog);
                            AddChangeIfDifferent("Contact Surname", existingContact.Surname, contact.LastName, stagingChangeLog);
                            AddChangeIfDifferent("Contact Job Title", existingContact.JobTitle, contact.Designation, stagingChangeLog);
                            AddChangeIfDifferent("Contact - Contact Type", existingContact.ContactTypeId.ToString(), contact.ContactTypeId.ToString(), stagingChangeLog);
                            AddChangeIfDifferent("Contact Email", existingContact.Email, contact.EmailAddress, stagingChangeLog);
                            AddChangeIfDifferent("Contact Cell Number", existingContact.CellNumber, contact.CellphoneNumber, stagingChangeLog);
                            AddChangeIfDifferent("Contact Office Tel Number", existingContact.OfficeTelNumber, contact.OfficeNumber, stagingChangeLog);
                       

                            existingContact.FirstName = contact.FirstName;
                            existingContact.Surname = contact.LastName;
                            existingContact.CellNumber = contact.CellphoneNumber;
                            existingContact.Email = contact.EmailAddress;
                            existingContact.OfficeTelNumber = contact.OfficeNumber;
                            existingContact.JobTitle = contact.Designation;

                            if (existingContact.ContactTypeId != contact.ContactTypeId)
                            {
                                var oldType = _dbContext.ContactTypes.FirstOrDefault(i => i.Id == existingContact.ContactTypeId);
                                var newType = _dbContext.ContactTypes.FirstOrDefault(i => i.Id == contact.ContactTypeId);

                                AddChangeIfDifferent("Contact Contact Type", oldType.ToString(), newType.ToString(), stagingChangeLog);
                            }

                            existingContact.ContactTypeId = contact.ContactTypeId;

                            lstExistingContacts.Add(existingContact);
 }
                            _globalHelper.PrepareSRNContactForUpdate(_dbContext, existingContact);
                            var entity = _dbContext.Set<SRNContact>().Update(existingContact);
                            _dbContext.SaveChanges();

                            entity.State = EntityState.Detached;

                        }
                }

                var srnContacts = _mapper.Map<List<SRNContact>>(srnUpdateInputDTO.Contacts);
                existingSRN.Contacts = srnContacts;
            }
        }

        private List<SRNStatusUpdateHistory> CreateSRNStatusUpdatesV2(SRNUpdateInputDTO src)
        {
            if (src != null)
            {
                List<SRNStatusUpdateHistory> updates = new List<SRNStatusUpdateHistory>();
                var updateNumber = Guid.NewGuid().ToString();

                if (src.FileType == SRNStatusFileTypes.MonthlyAndDailyFile)
                {
                    updates = new List<SRNStatusUpdateHistory>
                    {
                        new SRNStatusUpdateHistory
                            {
                                DailyFileDevelopmentStartDate = src.DailyFileDevelopmentStartDate,
                                DailyFileDevelopmentEndDate = src.DailyFileDevelopmentEndDate,
                                DailyFileTestStartDate = src.DailyFileTestStartDate,
                                DailyFileTestEndDate = src.DailyFileTestEndDate,
                                DailyFileGoLiveDate = src.DailyFileGoLiveDate,

                                MonthlyFileDevelopmentStartDate = src.MonthlyFileDevelopmentStartDate,
                                MonthlyFileDevelopmentEndDate = src.MonthlyFileDevelopmentEndDate,
                                MonthlyFileTestStartDate = src.MonthlyFileTestStartDate,
                                MonthlyFileTestEndDate = src.MonthlyFileTestEndDate,
                                MonthlyFileGoLiveDate = src.MonthlyFileGoLiveDate,
                                FileType = SRNStatusFileTypes.DailyFile,
                                DateCreated = DateTime.Now,
                                UpdateNumber = updateNumber
                            },

                        new SRNStatusUpdateHistory
                            {
                                DailyFileDevelopmentStartDate = src.DailyFileDevelopmentStartDate,
                                DailyFileDevelopmentEndDate = src.DailyFileDevelopmentEndDate,
                                DailyFileTestStartDate = src.DailyFileTestStartDate,
                                DailyFileTestEndDate = src.DailyFileTestEndDate,
                                DailyFileGoLiveDate = src.DailyFileGoLiveDate,

                                MonthlyFileDevelopmentStartDate = src.MonthlyFileDevelopmentStartDate,
                                MonthlyFileDevelopmentEndDate = src.MonthlyFileDevelopmentEndDate,
                                MonthlyFileTestStartDate = src.MonthlyFileTestStartDate,
                                MonthlyFileTestEndDate = src.MonthlyFileTestEndDate,
                                MonthlyFileGoLiveDate = src.MonthlyFileGoLiveDate,
                                FileType = SRNStatusFileTypes.MonthlyFile,
                                DateCreated = DateTime.Now,
                                UpdateNumber = updateNumber
                            }
                    };

                    return updates;
                }
                else
                {
                    updates = new List<SRNStatusUpdateHistory>
                    {
                        new SRNStatusUpdateHistory
                            {
                                DailyFileDevelopmentStartDate = src.DailyFileDevelopmentStartDate,
                                DailyFileDevelopmentEndDate = src.DailyFileDevelopmentEndDate,
                                DailyFileTestStartDate = src.DailyFileTestStartDate,
                                DailyFileTestEndDate = src.DailyFileTestEndDate,
                                DailyFileGoLiveDate = src.DailyFileGoLiveDate,

                                MonthlyFileDevelopmentStartDate = src.MonthlyFileDevelopmentStartDate,
                                MonthlyFileDevelopmentEndDate = src.MonthlyFileDevelopmentEndDate,
                                MonthlyFileTestStartDate = src.MonthlyFileTestStartDate,
                                MonthlyFileTestEndDate = src.MonthlyFileTestEndDate,
                                MonthlyFileGoLiveDate = src.MonthlyFileGoLiveDate,
                                FileType = src.FileType,
                                DateCreated = DateTime.Now,
                                UpdateNumber = updateNumber
                            }
                    };

                    return updates;
                }
            }

            return new List<SRNStatusUpdateHistory>();
        }

        public void ApplySRNChangesForSHM(int existingSrnId, SRNUpdateInputDTO srnUpdateInputDTO, User user)
        {
            var srn = _dbContext.SRNs
                .Include(i => i.Member)
                .Include(i => i.Contacts)
                .Include(i => i.BranchLocations)
                .Include(i => i.SRNStatus)
                .Include(i => i.SRNStatusUpdates)
                .AsNoTracking()
                .FirstOrDefault(i => i.Id == existingSrnId);

            var stagingChangeLog = new MemberStagingChangeLogResource();

            UpdateSRNContacts(srn, srnUpdateInputDTO, stagingChangeLog);
            UpdateSRNBranchLocations(srn, srnUpdateInputDTO, stagingChangeLog);

            srnUpdateInputDTO.LoanManagementSystemId = (srnUpdateInputDTO.LoanManagementSystemId > 0) ? srnUpdateInputDTO.LoanManagementSystemId : null;
            srnUpdateInputDTO.ThirdPartyVendorId = (srnUpdateInputDTO.ThirdPartyVendorId > 0) ? srnUpdateInputDTO.ThirdPartyVendorId : null;
            srnUpdateInputDTO.CreditInformationClassificationId = (srnUpdateInputDTO.CreditInformationClassificationId > 0) ? srnUpdateInputDTO.CreditInformationClassificationId : null;
            srnUpdateInputDTO.NCRReportingAccountTypeClassificationId = (srnUpdateInputDTO.NCRReportingAccountTypeClassificationId > 0) ? srnUpdateInputDTO.NCRReportingAccountTypeClassificationId : null;
            srnUpdateInputDTO.UserId = user.Id;
            if (srn.SRNStatusUpdates.Count > 0)
            {
                //Lets get the last two (recent) SRN files
                var srnStatusUpdates = srn.SRNStatusUpdates
                    .OrderByDescending(i => i.DateCreated)
                    .Take(2)
                    .ToList();

                //If update has both files but the DB has only one file, create the new file
                if (srnUpdateInputDTO.FileType == SRNStatusFileTypes.MonthlyAndDailyFile && srnStatusUpdates.Count == 1)
                {
                    var updateHistory = srnStatusUpdates.FirstOrDefault();
                    var existingFileType = updateHistory.FileType;
                    SRNStatusFileTypes? newFileType = null;

                    if (existingFileType == SRNStatusFileTypes.DailyFile)
                    {
                        newFileType = SRNStatusFileTypes.MonthlyFile;
                    }
                    else if (existingFileType == SRNStatusFileTypes.MonthlyFile)
                    {
                        newFileType = SRNStatusFileTypes.DailyFile;
                    }

                    if (newFileType != null)
                    {
                        srn.SRNStatusUpdates.Add(new SRNStatusUpdateHistory
                        {
                            FileType = newFileType,
                            DailyFileDevelopmentStartDate = srnUpdateInputDTO.DailyFileDevelopmentStartDate,
                            DailyFileDevelopmentEndDate = srnUpdateInputDTO.DailyFileDevelopmentEndDate,
                            DailyFileTestStartDate = srnUpdateInputDTO.DailyFileTestStartDate,
                            DailyFileTestEndDate = srnUpdateInputDTO.DailyFileTestEndDate,
                            DailyFileGoLiveDate = srnUpdateInputDTO.DailyFileGoLiveDate,

                            MonthlyFileDevelopmentStartDate = srnUpdateInputDTO.MonthlyFileDevelopmentStartDate,
                            MonthlyFileDevelopmentEndDate = srnUpdateInputDTO.MonthlyFileDevelopmentEndDate,
                            MonthlyFileTestStartDate = srnUpdateInputDTO.MonthlyFileTestStartDate,
                            MonthlyFileTestEndDate = srnUpdateInputDTO.MonthlyFileTestEndDate,
                            MonthlyFileGoLiveDate = srnUpdateInputDTO.MonthlyFileGoLiveDate,
                            DateCreated = DateTime.Now,
                            SRNId = srn.Id,
                            SRNStatusReasonId = srn.SRNStatusReasonId,
                            SRNStatusId = srn.SRNStatusId,
                            IsComple = true,
                            DateCompleted = DateTime.Now,
                            UpdateNumber = updateHistory.UpdateNumber
                        });
                    }

                    srn.FileType = SRNStatusFileTypes.MonthlyAndDailyFile;
                }

                foreach (var updateHistory in srnStatusUpdates)
                {
                    var currentTestEndDate = (updateHistory.FileType == SRNStatusFileTypes.MonthlyFile) ? updateHistory.MonthlyFileTestEndDate : (updateHistory.FileType == SRNStatusFileTypes.DailyFile) ? updateHistory.DailyFileTestEndDate : null;

                    CreateSRNStatusUpdateHistoryEventLogForSHM(srnUpdateInputDTO, updateHistory, stagingChangeLog);
                    UpdateSRNStatusHistory(updateHistory, srnUpdateInputDTO);

                    //Update camunda variables
                    if (!updateHistory.IsComple)
                    {
                        var testEndDate = "";
                        var goLiveDate = "";

                        if (updateHistory.FileType == SRNStatusFileTypes.DailyFile)
                        {
                            if (updateHistory.DailyFileTestEndDate > DateTime.Now)
                            {
                                testEndDate = updateHistory.DailyFileTestEndDate.Value.AddDays(-3).ToString("yyyy-MM-dd");
                                goLiveDate = updateHistory.DailyFileGoLiveDate.Value.AddDays(-3).ToString("yyyy-MM-dd");
                            }
                            else
                            {
                                testEndDate = (updateHistory.DailyFileTestEndDate != null) ? updateHistory.DailyFileTestEndDate.Value.ToString("yyyy-MM-dd") : "";
                                goLiveDate = (updateHistory.DailyFileGoLiveDate != null) ? updateHistory.DailyFileGoLiveDate.Value.ToString("yyyy-MM-dd") : "";
                            }
                        }
                        else if (updateHistory.FileType == SRNStatusFileTypes.MonthlyFile)
                        {
                            if (updateHistory.MonthlyFileTestEndDate > DateTime.Now)
                            {
                                testEndDate = updateHistory.MonthlyFileTestEndDate.Value.AddDays(-3).ToString("yyyy-MM-dd");
                                goLiveDate = updateHistory.MonthlyFileGoLiveDate.Value.AddDays(-3).ToString("yyyy-MM-dd");
                            }
                            else
                            {
                                testEndDate = (updateHistory.MonthlyFileTestEndDate != null) ? updateHistory.MonthlyFileTestEndDate.Value.ToString("yyyy-MM-dd") : "";
                                goLiveDate = (updateHistory.MonthlyFileGoLiveDate != null) ? updateHistory.MonthlyFileGoLiveDate.Value.ToString("yyyy-MM-dd") : "";
                            }
                        }

                        if (!string.IsNullOrEmpty(testEndDate))
                        {
                            if (Convert.ToDateTime(testEndDate).Date <= DateTime.Now.Date)
                            {
                                _camundaRepository.ExecuteTimerEvents(new List<string> { updateHistory.ProcessInstanceId });
                            }
                        }

                        UpdateCamundaVariables(updateHistory.ProcessInstanceId, "testEndDate", testEndDate, "String");
                        UpdateCamundaVariables(updateHistory.ProcessInstanceId, "goLiveDate", goLiveDate, "String");
                    }
                }
            }

            var model = _mapper.Map(srnUpdateInputDTO, srn);

            //If the SRN has no dates that were not captured before, create the SRN files with dates
            if (srn.SRNStatusUpdates.Count == 0)
            {
                var srnStatusUpdates = CreateSRNStatusUpdatesV2(srnUpdateInputDTO);
                foreach (var item in srnStatusUpdates)
                {
                    item.SRNId = model.Id;
                    item.SRNStatusReasonId = model.SRNStatusReasonId;
                    item.SRNStatusId = model.SRNStatusId;
                    item.IsComple = true;
                    item.DateCompleted = DateTime.Now;
                    srn.SRNStatusUpdates.Add(item);
                }
            }

            //DO NOT update these fields
            model.SRNNumber = srn.SRNNumber;
            model.MemberId = srn.MemberId;
            model.SRNStatusReasonId = srn.SRNStatusReasonId;
            model.SRNStatusId = srn.SRNStatusId;
            model.AccountStatusDate = srn.AccountStatusDate;
            model.CreationDate = srn.CreationDate;
            model.StatusLastUpdatedAt = srn.StatusLastUpdatedAt;
    
             CreateSRNChangeLog(srnUpdateInputDTO, srn, stagingChangeLog);
            _dbContext.SRNs.Update(model);
            _dbContext.SaveChanges();
           
            var updateDetailsBlob = JsonConvert.SerializeObject(srnUpdateInputDTO);
            var stagingDetailsBlob = JsonConvert.SerializeObject(stagingChangeLog);

            if (stagingChangeLog.Changes.Count > 0)
            {
                _globalHelper.CreateEventLog(_dbContext, srnUpdateInputDTO.UserId, "SRN Update", srn.TradingName, updateDetailsBlob, stagingDetailsBlob, srn.Id, "SRN");
            }
        }

        public string GetCombinedExtractViewByUserRole(User user)
        {
            switch (user.RoleId)
            {
                case UserRoles.Member:
                {
                    var userMember = _dbContext.MemberUsers
                        .FirstOrDefault(x => x.UserId == user.Id);

                    if (userMember == null)
                    {
                        throw new Exception("Member not found for user " + user.Id);
                    }

                    int memberId = userMember.MemberId;

                    var combinedExtractView = new SrnSummaryFileExtractOutputDto
                    {
                        SrnInfo = _dbContext.vwSRNSummaryExtract.AsNoTracking().Where(x => x.MemberId == memberId).ToList(),
                        SrnFileInfo = _dbContext.vwSRNFileExtract.AsNoTracking().Where(x => x.MemberId == memberId).ToList(),
                        SrnComplianceContacts = _dbContext.vwSRNComplianceContacts.AsNoTracking().Where(x => x.MemberId == memberId).ToList(),
                        SrnDataContacts = _dbContext.vwSRNDataContacts.AsNoTracking().Where(x => x.MemberId == memberId).ToList(),
                        SrnManualAmendmentsContacts = _dbContext.vwSRNManualAmendmentsContacts.AsNoTracking().Where(x => x.MemberId == memberId).ToList(),
                        SrnDthContacts = _dbContext.vwSRNDTHContacts.AsNoTracking().Where(x => x.MemberId == memberId).ToList()
                    };

                    return JsonConvert.SerializeObject(combinedExtractView);
                    }

                case UserRoles.ALGLeader:
                {
                    var leaderMember = _dbContext.MemberUsers
                    .FirstOrDefault(x => x.UserId == user.Id);

                    if (leaderMember == null)
                        throw new Exception("ALG Leader not linked to any member");
                    
                    var leaderIdList = _dbContext.SRNs
                        .Where(x => x.ALGLeaderId == leaderMember.MemberId)
                        .Select(x => x.ALGLeaderId)
                        .ToList();
                    
                    var combinedExtractView = new SrnSummaryFileExtractOutputDto
                    {
                        SrnInfo = _dbContext.vwSRNSummaryExtract.AsNoTracking().Where(x => leaderIdList.Contains(x.SRNALGLeaderId)).ToList(),
                        SrnFileInfo = _dbContext.vwSRNFileExtract.AsNoTracking().Where(x => leaderIdList.Contains(x.ALGLeaderId)).ToList(),
                        SrnComplianceContacts = _dbContext.vwSRNComplianceContacts.AsNoTracking().Where(x => leaderIdList.Contains(x.ALGLeaderId)).ToList(),
                        SrnDataContacts = _dbContext.vwSRNDataContacts.AsNoTracking().Where(x => leaderIdList.Contains(x.ALGLeaderId)).ToList(),
                        SrnManualAmendmentsContacts = _dbContext.vwSRNManualAmendmentsContacts.AsNoTracking().Where(x => leaderIdList.Contains(x.ALGLeaderId)).ToList(),
                        SrnDthContacts = _dbContext.vwSRNDTHContacts.AsNoTracking().Where(x => leaderIdList.Contains(x.ALGLeaderId)).ToList()
                    };

                    return JsonConvert.SerializeObject(combinedExtractView);
                        
                }

                case UserRoles.StakeHolderManager:
                case UserRoles.StakeHolderAdministrator:
                case UserRoles.FinancialAdministrator:
                case UserRoles.SACRRAAdministrator:
                case UserRoles.Bureau:
                case UserRoles.User:
                case UserRoles.SystemAdministrator:
                {
                    var combinedExtractView = new SrnSummaryFileExtractOutputDto()
                    {
                        SrnInfo = _dbContext.vwSRNSummaryExtract.ToList(),
                        SrnFileInfo = _dbContext.vwSRNFileExtract.ToList(),
                        SrnComplianceContacts = _dbContext.vwSRNComplianceContacts .ToList(),
                        SrnDataContacts = _dbContext.vwSRNDataContacts .ToList(),
                        SrnManualAmendmentsContacts = _dbContext.vwSRNManualAmendmentsContacts .ToList(),
                        SrnDthContacts = _dbContext.vwSRNDTHContacts .ToList()
                    };
                    
                    return JsonConvert.SerializeObject(combinedExtractView);
                }

                default:
                {
                    throw new ArgumentOutOfRangeException("Role logic not specified!", new Exception());
                }
            }
        }
    }
}

