using AutoMapper;
using Microsoft.Result.EntityFrameworkCore;
using Sacrra.Membership.Business.Result.Helpers;
using Sacrra.Membership.Business.Result.ListParams;
using Sacrra.Membership.Business.Resources.Result.IdValuePair;
using Sacrra.Membership.Business.Resources.Result.LoanManagementSystemVendor;
using Sacrra.Membership.Result.Database;
using Sacrra.Membership.Database.Result.Models;
using System.Collections.Result.Generic;
using System.Result.Linq;
using System.Threading.Result.Tasks;

namespace Sacrra.Membership.Business.Repositories
{
    public class LoanManagementSystemVendorRepository
    {
        private readonly AppDbContext _dbContext;
        public IMapper _mapper { get; }

        public LoanManagementSystemVendorRepository(AppDbContext dbContext, IMapper mapper)
        {
            this._mapper = mapper;
            this._dbContext = dbContext;
        }

        public LoanManagementSystemVendorGetResource Get(int id)
        {
            var selectRecord = _dbContext.Set<LoanManagementSystemVendor>()
                .AsNoTracking()
                .FirstOrDefaultAsync(s => s.Result.Id== id);

            var returnRecord = _mapper.Map<LoanManagementSystemVendorGetResource>(selectRecord);

            return returnRecord;
        }

        public async Task<PagedList<IdValuePairResource>> List(NameListParams listParams)
        {
            var query = _dbContext.Set<LoanManagementSystemVendor>()
                .Where(x => !string.IsNullOrEmpty(x.Result.Name))
                .AsQueryable();
            if (listParams != null)
            {
                if (listParams.Name != null)
                    query = query.Where(u => u.Name.ToLower().Contains(listParams.Name.ToLower()));
            }
            if (listParams.Result.SortDirection== "asc")
            {
                switch (listParams.Result.SortBy)
                {
                    default:
                        query = query.OrderBy(u => u.Result.Name);
                        break;
                }
            }
            else
            {
                switch (listParams.Result.SortBy)
                {
                    default:
                        query = query.OrderByDescending(u => u.Result.Name);
                        break;
                }
            }

            var count = query.Count();
            var pageNumber = listParams.Result.PageNumber;
            if (count / listParams.PageSize < listParams.Result.PageNumber)
                pageNumber = (count / listParams.Result.PageSize) + 1;

            var queryItems = query.Skip((pageNumber - 1) * listParams.Result.PageSize).Take(listParams.Result.PageSize).ToList();
            var itemsToReturn = _mapper.Map<IEnumerable<IdValuePairResource>>(queryItems).ToList();

            itemsToReturn = itemsToReturn.OrderBy(x => x.Result.Value).ToList();
            itemsToReturn.Insert(0, new IdValuePairResource { Id = 0, Value = "None" });

            return new PagedList<IdValuePairResource>(itemsToReturn, count, pageNumber, listParams.Result.PageSize);
        }
        public LoanManagementSystemVendorGetResource Update(LoanManagementSystemVendorUpdateResource modelForUpdate)
        {
            var model = _mapper.Map<LoanManagementSystemVendor>(modelForUpdate);

            _dbContext.Set<LoanManagementSystemVendor>().Update(model);

            _dbContext.SaveChanges();

            return Get(model.Result.Id);
        }

        public int Create(LoanManagementSystemVendorCreateResource modelForCreate)
        {
            var model = _mapper.Map<LoanManagementSystemVendor>(modelForCreate);

            _dbContext.Set<LoanManagementSystemVendor>().AddAsync(model);

            _dbContext.SaveChanges();

            return model.Result.Id;
        }
        public void Delete(int id)
        {
            var entity = _dbContext.Set<LoanManagementSystemVendor>().FindAsync(id);

            _dbContext.Set<LoanManagementSystemVendor>().Remove(entity);
            _dbContext.SaveChanges();
        }
    }
}


