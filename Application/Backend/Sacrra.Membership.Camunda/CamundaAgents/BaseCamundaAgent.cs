using Camunda.Api.Client.ExternalTask;
using Camunda.Api.Client.User;
using Camunda.Api.Client;
using Microsoft.Extensions.DependencyInjection;
using Sacrra.Membership.Business.Repositories;
using Serilog;
using System;
using System.Linq;
using System.Threading.Tasks;
using Sacrra.Membership.Business.Services;

namespace Sacrra.Membership.Camunda.CamundaAgents
{
    public class BaseCamundaAgent : ICamundaAgent
    {
        private readonly CamundaClient _camundaClient;

        public virtual Task Process(string topicName, ExternalTaskResource task, CompleteExternalTask completeExternalTask,
            IServiceScope serviceScope)
        {
            throw new NotImplementedException();
        }
        
        public BaseCamundaAgent(CamundaClient camundaClient)
        {
            _camundaClient = camundaClient;
        }

        protected static async Task<int> GetIntegerVariable(ExternalTaskResource task, string varibaleName, IServiceScope serviceScope)
        {
            var srnTask = task.Get();
            var srnVariables = GetCamundaRepository(serviceScope).GetVariables(srnTask.ProcessInstanceId);
            var srnVariable = srnVariables.FirstOrDefault(i => i.Name == varibaleName);
            var srnId = (srnVariable != null) ? srnVariable.Value : "0";

            return Convert.ToInt32(srnId);
        }

        protected static CamundaRepository GetCamundaRepository(IServiceScope serviceScope)
        {
            return serviceScope.ServiceProvider.GetRequiredService<CamundaRepository>();
        }

        protected UserProfileInfo GetRandomUser(string group)
        {
            var usersInGroup = _camundaClient.Users.Query(new UserQuery()
            {
                MemberOfGroup = group
            }).List();
            var randomUser = usersInGroup.PickRandom();

            return randomUser;
        }

        protected static SRNRepository GetSrnRepository(IServiceScope serviceScope)
        {
            return serviceScope.ServiceProvider.GetRequiredService<SRNRepository>();
        }

        protected static async Task CompleteTask(ExternalTaskResource task, CompleteExternalTask completeExternalTask)
        {
            try
            {
                task.Complete(completeExternalTask);
            }
            catch (Exception exception)
            {
                Log.Error(exception.Message);
            }
        }

        protected static async Task<int> GetMemberVariable(ExternalTaskResource task, string varibaleName, IServiceScope serviceScope)
        {
            var memberTask = task.Get();
            var memberVariables = GetCamundaRepository(serviceScope).GetMemberVariables(memberTask.ProcessInstanceId);
            var memberVariable = memberVariables.FirstOrDefault(i => i.Name == varibaleName);
            var memberId = (memberVariable != null) ? memberVariable.Value : "0";

            return Convert.ToInt32(memberId);
        }

        protected static async Task<string> GetGenericVariable(ExternalTaskResource task, string varibaleName, IServiceScope serviceScope)
        {
            var variableTask = task.Get();
            var taskVariables = GetCamundaRepository(serviceScope).GetMemberVariables(variableTask.ProcessInstanceId);
            var genericVariable = taskVariables.FirstOrDefault(i => i.Name == varibaleName);
            var variableValue = (genericVariable != null) ? genericVariable.Value : "0";

            return variableValue;
        }
        
        protected static SRNRepository GetSRNRepository(IServiceScope serviceScope)
        {
            return serviceScope.ServiceProvider.GetRequiredService<SRNRepository>();
        }
        
        protected static SRNService GetSRNService(IServiceScope serviceScope)
        {
            return serviceScope.ServiceProvider.GetRequiredService<SRNService>();
        }
    }
}

