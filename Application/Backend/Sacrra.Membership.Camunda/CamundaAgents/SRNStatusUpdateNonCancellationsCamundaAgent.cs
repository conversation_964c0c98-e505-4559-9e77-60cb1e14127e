using System;
using System.Threading.Tasks;
using Camunda.Api.Client;
using Camunda.Api.Client.ExternalTask;
using Microsoft.Extensions.DependencyInjection;
using Sacrra.Membership.Business.Services;

namespace Sacrra.Membership.Camunda.CamundaAgents;

public class SrnStatusUpdateNonCancellationsCamundaAgent: BaseCamundaAgent
{
    public SrnStatusUpdateNonCancellationsCamundaAgent(CamundaClient camundaClient) : base(camundaClient)
    {
    }

    public override async Task Process(string topicName, ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        switch (topicName)
        {
            case "update-srn-status-for-non-cancellations":
                UpdateSrnStatusForNonCancellations(task, completeExternalTask, serviceScope);
                break;
            
            case "email-member-about-srn-status-update-for-non-cancellations":
                EmailMemberAboutSrnStatusUpdateForNonCancellations(task, completeExternalTask, serviceScope);
                break;
            
            case "email-bureaus-about-srn-status-update-for-non-cancellations":
                EmailBureausAboutSrnStatusUpdateForNonCancellations(task, completeExternalTask, serviceScope);
                break;
        }

        task.Complete(completeExternalTask);
    }

    private void EmailBureausAboutSrnStatusUpdateForNonCancellations(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var srnId = GetGenericVariable(task, "SRNId", serviceScope);
        
        if (!string.IsNullOrEmpty(srnId))
        {
            GetSrnStatusUpdateNonCancellationsCamundaService(serviceScope).EmailBureausAboutSRNStatusUpdateForNonCancellations(Convert.ToInt32(srnId));
        }
    }

    private void EmailMemberAboutSrnStatusUpdateForNonCancellations(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var srnId = GetGenericVariable(task, "SRNId", serviceScope);
        
        if (!string.IsNullOrEmpty(srnId))
        {
            GetSrnStatusUpdateNonCancellationsCamundaService(serviceScope).EmailMemberAboutSRNStatusUpdateForNonCancellations(Convert.ToInt32(srnId));
        }
    }

    private void UpdateSrnStatusForNonCancellations(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var srnId = GetGenericVariable(task, "SRNId", serviceScope);
        var srnStatusName = GetGenericVariable(task, "SRNStatusName", serviceScope);
        var updatedByUserIdVariable = GetGenericVariable(task, "UpdatedByUserId", serviceScope);

        if (!string.IsNullOrEmpty(srnId) && !string.IsNullOrEmpty(srnStatusName))
        {
            int updatedByUserId = (!string.IsNullOrEmpty(updatedByUserIdVariable)) ? Convert.ToInt32(updatedByUserIdVariable) : 0;

            var taskInfo = task.Get();
            var processInstanceId = taskInfo.ProcessInstanceId;

            GetSrnStatusUpdateNonCancellationsCamundaService(serviceScope).UpdateSRNStatus(Convert.ToInt32(srnId), srnStatusName, updatedByUserId, processInstanceId);
        }
    }
    
    private static SrnStatusUpdateNonCancellationsCamundaService GetSrnStatusUpdateNonCancellationsCamundaService(IServiceScope serviceScope)
    {
        return serviceScope.ServiceProvider.GetRequiredService<SrnStatusUpdateNonCancellationsCamundaService>();
    }
}
