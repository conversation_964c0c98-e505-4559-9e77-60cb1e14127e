using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Sacrra.Membership.Business.DTOs;
using Sacrra.Membership.Business.DTOs.MemberUpdateDTOs;
using Sacrra.Membership.Business.DTOs.PaginationDTOs;
using Sacrra.Membership.Business.Services;
using Sacrra.Membership.Business.Services.MembersService;
using Sacrra.Membership.Business.Resources.IdValuePair;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Views;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Sacrra.Membership.Api.ControllersV2
{
    [ApiController]
    [ApiVersion("2")]
    [Route("api/v{v:apiVersion}/[controller]/[action]")]
    [Authorize(Roles = "Financial Administrator,User,Member,Stakeholder Manager,Group Stakeholder Manager,SACRRA Administrator,Bureau,ALG Leader")]
    public class MembersController : Controller
    {
        private readonly MembersService _membersService;
        private readonly GlobalHelper _globalHelper;

        public MembersController(MembersService membersService, GlobalHelper globalHelper)
        {
            _membersService = membersService;
            _globalHelper = globalHelper;
        }

        [HttpGet]
        [ProducesResponseType(type: typeof(List<MemberMyInfoOutputDTO>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult GetMyInformation()
        {
            var user = _globalHelper.GetUserByAuth0Id(User.Identity.Name);
            return Ok(_membersService.GetMyInformation(user));
        }

        [Authorize(Roles = "ALG Leader")]
        [HttpPost]
        [ProducesResponseType(type: typeof(Sacrra.Membership.Business.DTOs.PagedList<AlgLeaderClientOutputDTO>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult ListALGClients([FromBody] PaginationInputDTO paginationData, ApplicationStatuses? status = null)
        {
            var user = _globalHelper.GetUserByAuth0Id(User.Identity.Name);

            var data = _membersService.ListALGClients_V2(paginationData, user, status);

            var paginationInfo = new
            {
                data.TotalCount,
                data.PageSize,
                data.CurrentPage,
                data.TotalPages,
                data.HasNext,
                data.HasPrevious
            };

            Response.Headers.Add("Access-Control-Expose-Headers", "X-Pagination");
            Response.Headers.Add("X-Pagination", JsonConvert.SerializeObject(paginationInfo));

            return Ok(data);
        }

        [HttpPost]
        [ProducesResponseType(type: typeof(List<MemberRequestInputDTO>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult RequestMember([FromBody] MemberRequestInputDTO memberData)
        {
            var user = _globalHelper.GetUserByAuth0Id(User.Identity.Name);

            _membersService.RequestMember(memberData, user);
            return Ok();
        }

        [HttpPut]
        [ProducesResponseType(type: typeof(MemberUpdateMessageOutputDTO), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult UpdateMember([FromBody] MemberUpdateInputDTO memberUpdateInputDTO)
        {
            var user = _globalHelper.GetUserByAuth0Id(User.Identity.Name);

            return Ok(_membersService.UpdateMember(memberUpdateInputDTO, user));
        }


        [HttpGet("{id}")]
        [ProducesResponseType(type: typeof(MemberOutputDTO), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult GetMember(int id)
        {
            var user = _globalHelper.GetUserByAuth0Id(User.Identity.Name);

            var data = _membersService.GetMember(id, user);
            if (data == null)
                return NotFound();

            return Ok(data);
        }

        [HttpGet]
        [ProducesResponseType(type: typeof(List<IdValuePairResource>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult GetMemberList([FromQuery] ApplicationStatuses? status = null)
        {
            var user = _globalHelper.GetUserByAuth0Id(User.Identity.Name);

            var Members = _membersService.GetMemberList(status, user);

            return Ok(Members);
        }

        [HttpGet]
        [ProducesResponseType(typeof(List<ALGLeaderOutputDTO>), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult GetALGLeaders()
        {
            var user = _globalHelper.GetUserByAuth0Id(User.Identity.Name);

            return Ok(_membersService.GetALGLeaders(user));
        }

        [HttpGet]
        [ProducesResponseType(typeof(List<string>), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult GetAllCompanyRegistrationNumbers()
        {
            return Ok(_membersService.GetAllCompanyRegistrationNumbers());
        }

        [HttpGet("{registrationNumber}")]
        [ProducesResponseType(type: typeof(bool), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult DoesMemberExist(string registrationNumber)
        {
            return Ok(_membersService.DoesMemberExist(registrationNumber));
        }
        [HttpGet("{vatNumber}")]
        [ProducesResponseType(type: typeof(bool), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult DoesVATNumberExist(string vatNumber)
        {
            return Ok(_membersService.DoesVATNumberExist(vatNumber));
        }

        [Authorize(Roles = "Stakeholder Manager")]
        [HttpGet("{clientId}")]
        [ProducesResponseType(typeof(List<ALGLeaderIdValuePairOutputDTO>), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult GetALGLeaders(int clientId)
        {
            var user = _globalHelper.GetUserByAuth0Id(User.Identity.Name);

            return Ok(_membersService.GetALGLeaders(clientId, user));
        }

        [Authorize(Roles = "SACRRA Administrator,Financial Administrator")]
        [HttpGet]
        [ProducesResponseType(type: typeof(IEnumerable<dynamic>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult GetMonthlyMemberInvoices()
        {
            var invoices = _membersService.GetMonthlyMemberInvoices();
            return Ok(invoices);
        }

        [HttpGet]
        [ProducesResponseType(typeof(IEnumerable<vwMemberDetails>), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult GetAllMemberDetails()
        {
            try
            {
                var memberDetails = _membersService.GetAllMemberDetailsAsync();
                if (memberDetails == null || !memberDetails.Any())
                    return NoContent();

                return Ok(memberDetails);
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        [HttpGet("{srnNumber}")]
        [ProducesResponseType(typeof(vwMemberDetails), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult GetMemberDetailsBySRN(string srnNumber)
        {
            if (string.IsNullOrEmpty(srnNumber))
                return BadRequest("SRN number is required");

            var memberDetails = _membersService.GetMemberDetailsBySRNAsync(srnNumber);
            if (memberDetails == null)
                return NoContent();

            return Ok(memberDetails);
        }

        [HttpGet("{memberId}")]
        [ProducesResponseType(typeof(bool), 200)]
        [ProducesResponseType(400)]
        public IActionResult MemberSrnsActive(int memberId)
        {
            if (memberId <= 0)
            {
                return BadRequest("Invalid memberId.");
            }

            _membersService.MemberSrnsActive(memberId);
            return Ok();
        }
    }
}

