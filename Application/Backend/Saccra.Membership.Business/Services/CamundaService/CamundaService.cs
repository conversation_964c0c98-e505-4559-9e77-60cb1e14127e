using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;
using RestSharp;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.Resources.Camunda.Task;
using Sacrra.Membership.Business.Resources.Member;
using Sacrra.Membership.Business.Resources.MemberChangeRequest;
using Sacrra.Membership.Business.Resources.SRN;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Sacrra.Membership.Notification.Repositories;
using System.Net;
using Sacrra.Membership.Business.DTOs;
using Sacrra.Membership.Business.DTOs.AdHocFIlesDTO;
using Sacrra.Membership.Business.DTOs.MemberUpdateDTOs;
using Sacrra.Membership.Business.DTOs.ReplacementFileSubmissionDTOs;
using Sacrra.Membership.Business.DTOs.SRNHasTaskDTOs;
using Sacrra.Membership.Business.DTOs.TaskDTOs;

namespace Sacrra.Membership.Business.Services
{
    internal class CamundaTaskVariableInfo
    {
        string Type { get; set; }
        string Value { get; set; }
        object ValueInfo { get; set; }
    }

    public class CamundaService
    {
        public IMapper _mapper { get; }
        private readonly AppDbContext _dbContext;
        private readonly GlobalHelper _globalHelper;
        private readonly ConfigSettings _configSettings;
        private readonly SRNService _srnService;
        private readonly EmailService _emailService;
        private readonly CamundaServiceHelper _camundaServiceHelper;
        private readonly MemberServiceHelper _memberServiceHelper;
        private readonly DWExceptionService _dWExceptionService;
        private string _dwBaseApiUrl;
        private string DW_API_KEY;
        private string _dwDataset;

        public CamundaService(IMapper mapper, GlobalHelper globalHelper, AppDbContext dbContext, 
            IOptions<ConfigSettings> configSettings, SRNService srnService,
            EmailService emailService, CamundaServiceHelper camundaServiceHelper, 
            MemberServiceHelper memberServiceHelper, DWExceptionService dWExceptionService,
            IConfiguration configuration)
        {
            _mapper = mapper;
            _globalHelper = globalHelper;
            _dbContext = dbContext;
            _configSettings = configSettings.Value;
            _srnService = srnService;
            _emailService = emailService;
            _camundaServiceHelper = camundaServiceHelper;
            _memberServiceHelper = memberServiceHelper;
            _dWExceptionService = dWExceptionService;

            _dwBaseApiUrl = configuration.GetSection("ReportingAPISettings")["BaseApiUrl"];
            DW_API_KEY = configuration.GetSection("ReportingAPISettings")["ApiKey"];
            _dwDataset = configuration.GetSection("ReportingAPISettings")["Dataset"];
        }

        public async Task<List<ShmMemberTaskOutputDTO>> GetStakeholderManagerMemberTasks(User user, List<string> processDefinitionKeys = null)
        {
            var stakeHolderManagerTasks = new List<ShmMemberTaskOutputDTO>();

            foreach (var key in processDefinitionKeys)
            {
                var userTasks = _globalHelper.GetUserTasks(user.Id.ToString(), key);
                var mappedTasks = _mapper.Map<List<ShmMemberTaskOutputDTO>>(userTasks);

                stakeHolderManagerTasks.AddRange(mappedTasks);
            }

            return stakeHolderManagerTasks;
        }

        public async Task<List<SacrraAdminMyTasksOutputDTO>> GetSACRRAAdminTasks(User user, List<string> processDefinitionKeys = null)
        {
            var sacrraAdminTasks = new List<SacrraAdminMyTasksOutputDTO>();

            foreach (var key in processDefinitionKeys)
            {
                var userTasks = _globalHelper.GetUserTasks(user.Id.ToString(), key);
                var mappedTasks = _mapper.Map<List<SacrraAdminMyTasksOutputDTO>>(userTasks);

                sacrraAdminTasks.AddRange(mappedTasks);
            }

            return sacrraAdminTasks;
        }

        public async Task<List<FinancialAdminMemberTasksOutputDTO>> GetFinancialAdminMemberTasks(User user, List<string> processDefinitionKeys = null)
        {
            var financialAdminstratorManager = new List<FinancialAdminMemberTasksOutputDTO>();

            foreach (var key in processDefinitionKeys)
            {
                var userTasks = _globalHelper.GetUserTasks(user.Id.ToString(), key);
                var mappedTasks = _mapper.Map<List<FinancialAdminMemberTasksOutputDTO>>(userTasks);

                financialAdminstratorManager.AddRange(mappedTasks);
            }

            return financialAdminstratorManager;
        }

        public async Task<List<GSHMShmTasksOutputDTO>> GetGSHMShmTasks()
        {
            var users = _dbContext.Users.Where(x => x.RoleId == UserRoles.StakeHolderManager).ToList();
            var taskList = new List<TaskListResource>();

            foreach (var user in users)
            {
                var shmTaskList = _globalHelper.GetUserTasks(user.Id.ToString());
                taskList.AddRange(shmTaskList);
            }

            var mappedTaskList = _mapper.Map<List<GSHMShmTasksOutputDTO>>(taskList);

            return mappedTaskList;
        }

        public async Task<List<GSHMUnassignedTasksOutputDTO>> GetGSHMUnassignedTasks(User user)
        {
            var unassignedTaskList = _globalHelper.GetUserTasks(user.Id.ToString(), null);

            return _mapper.Map<List<GSHMUnassignedTasksOutputDTO>>(unassignedTaskList);
        }
        
        public async Task<List<ShmSRNTaskOutputDTO>> GetStakeHolderManagerSRNTasks(User user, List<string> processDefinitionKeys = null)
        {
            var stakeholderManager = new List<ShmSRNTaskOutputDTO>();

            foreach (var key in processDefinitionKeys)
            {
                var userTasks = _globalHelper.GetUserTasks(user.Id.ToString(), key);
                var mappedTasks = _mapper.Map<List<ShmSRNTaskOutputDTO>>(userTasks);

                stakeholderManager.AddRange(mappedTasks);
            }

            return stakeholderManager;
        }
        
        public void CompleteSHMReviewMemberApplicationTask(string taskId, SHMMemberReviewInputDTO reviewInputDTO, User user)
        {
            var contractResolver = new DefaultContractResolver
            {
                NamingStrategy = new CamelCaseNamingStrategy()
            };

            var task = _globalHelper.GetTaskAsync(taskId);
            var variables = _globalHelper.GetVariablesAsync(task.ProcessInstanceId);
            var shmDecision = "";

            if (variables.Count > 0)
            {
                var memberId = variables.FirstOrDefault(i => i.Name == "OrganisationID").Value;

                var member = await _dbContext.Members
                    .AsNoTracking()
                    .FirstOrDefaultAsync(i => i.Id == Convert.ToInt32(memberId));

                var oldMembershipType = member.MembershipTypeId;
                var oldMemberStatus = member.ApplicationStatusId;

                if (reviewInputDTO.ReviewDecision == AllMemberReviewDecisionsEnum.Disqualified)
                {
                    shmDecision = "disqualified";
                    if (member != null)
                    {
                        //member.DisqualificationReason = reviewInputDTO.RejectReason;
                        member.ApplicationStatusId = ApplicationStatuses.MemberRegistrationDisqualified;
                    }
                }
                else if (reviewInputDTO.ReviewDecision == AllMemberReviewDecisionsEnum.FullMember)
                {
                    shmDecision = "fullMember";
                    member.MembershipTypeId = MembershipTypes.FullMember;
                    member.ApplicationStatusId = ApplicationStatuses.MemberRegistrationWaitingForInvoiceToBeGenerated;
                }
                else if (reviewInputDTO.ReviewDecision == AllMemberReviewDecisionsEnum.NonMember)
                {
                    shmDecision = "nonMember";
                    member.MembershipTypeId = MembershipTypes.NonMember;
                    member.ApplicationStatusId = ApplicationStatuses.MemberRegistrationAwaitingOnboardingInvoicePayment;
                }
                else if (reviewInputDTO.ReviewDecision == AllMemberReviewDecisionsEnum.ALGClient)
                {
                    shmDecision = "algClient";
                    member.MembershipTypeId = MembershipTypes.ALGClient;
                    member.ApplicationStatusId = ApplicationStatuses.MemberRegistrationCompleted;
                }

                _dbContext.Set<Member>().Update(member);
                _dbContext.SaveChanges();

                MemberStagingChangeLogResource stagingChangeLog = new();

                stagingChangeLog.Changes.Add(new StagingChange
                {
                    Name = "Application Status",
                    OldValue = EnumHelper.GetEnumIdValuePair<ApplicationStatuses>((int)oldMemberStatus).Value,
                    NewValue = EnumHelper.GetEnumIdValuePair<ApplicationStatuses>((int)member.ApplicationStatusId).Value
                });

                stagingChangeLog.Changes.Add(new StagingChange
                {
                    Name = "Membeship Type",
                    OldValue = EnumHelper.GetEnumIdValuePair<MembershipTypes>((int)oldMembershipType).Value,
                    NewValue = EnumHelper.GetEnumIdValuePair<MembershipTypes>((int)member.MembershipTypeId).Value
                });

                CreateMemberStatusUpdateEventLog(member, stagingChangeLog, user);

                var taskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                        {
                            {
                                "variables",
                                new Dictionary<string, Dictionary<string, string>>
                                {
                                    {
                                        "reviewApplicationDecision",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", shmDecision },
                                            { "type", "String" }
                                        }
                                    }
                                }
                            }
                        };

                using (var client = new HttpClient())
                {
                    var json = JsonConvert.SerializeObject(taskVariables, new JsonSerializerSettings
                    {
                        Formatting = Formatting.Indented
                    });
                    var content = new StringContent(json, Encoding.UTF8, "application/json");
                    var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                    var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });
                    result.EnsureSuccessStatusCode();
                }
            }
        }

        public void CompleteStakeHolderManagerReviewMemberChangesTask(string taskId, StakeholderManagerMemberUpdateReviewInputDTO reviewDTO, User user)
        {
            var contractResolver = new DefaultContractResolver
            {
                NamingStrategy = new CamelCaseNamingStrategy()
            };

            var task = _globalHelper.GetTaskAsync(taskId);
            var variables = _globalHelper.GetVariablesAsync(task.ProcessInstanceId);

            if (variables.Count > 0)
            {
                var reviewDescision = reviewDTO.ReviewDecision == MemberUpdateReviewOptions.Accepted ? "accepted" : "rejected";

                var taskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                        {
                            {
                                "variables",
                                new Dictionary<string, Dictionary<string, string>>
                                {
                                    {
                                        "memberChangesDecision",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", reviewDescision },
                                            { "type", "String" }
                                        }
                                    }
                                }
                            }
                        };

                var memberVariables = variables.FirstOrDefault(i => i.Name == "ChangeRequestId");
                var requestId = (memberVariables != null) ? memberVariables.Value : "0";
                int memberId = 0;

                if (!string.IsNullOrEmpty(requestId))
                {
                    var changeRequest = _camundaServiceHelper.GetMemberChangeRequest(Convert.ToInt32(requestId));
                    if (changeRequest != null)
                    {
                        memberId = changeRequest.ObjectId;

                        var member = _dbContext.Members
                            .Include(x => x.TradingNames)
                            .AsNoTracking()
                            .FirstOrDefault(i => i.Id == memberId);

                        if (reviewDescision == "accepted")
                        {
                            if (member != null)
                            {
                                if (changeRequest != null)
                                {
                                    changeRequest.Status = ChangeRequestStatus.Accepted;
                                    _dbContext.Set<ChangeRequestStaging>().Update(changeRequest);
                                }

                                var modelForUpdate = JsonConvert.DeserializeObject<MemberUpdateInputDTO>(changeRequest.UpdatedDetailsBlob);
                                _memberServiceHelper.ApplyMemberChanges(_dbContext, member, _mapper, modelForUpdate, user);
                                _camundaServiceHelper.NotifyApplicantOfMemberUpdateAccepted(member.Id);
                            }
                        }

                        else if (reviewDescision == "rejected")
                        {
                            _camundaServiceHelper.NotifyApplicantOfMemberUpdateDecline(member.Id);
                        }

                        _dbContext.Remove(changeRequest);
                    }
                }

                _dbContext.SaveChanges();

                using (var client = new HttpClient())
                {
                    var json = JsonConvert.SerializeObject(taskVariables, new JsonSerializerSettings
                    {
                        Formatting = Formatting.Indented
                    });
                    var content = new StringContent(json, Encoding.UTF8, "application/json");
                    var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                    var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });
                    result.EnsureSuccessStatusCode();
                }
            }
        }

        public void CompleteFinancialAdminCheckPaymentTask(string taskId, FinancialAdminMemberReviewInputDTO taskUpdateResource, User user)
        {
            var contractResolver = new DefaultContractResolver
            {
                NamingStrategy = new CamelCaseNamingStrategy()
            };

            var task = _globalHelper.GetTaskAsync(taskId);
            var variables = _globalHelper.GetVariablesAsync(task.ProcessInstanceId);

            var memberId = variables.FirstOrDefault(i => i.Name == "OrganisationID").Value;

            var member = _dbContext.Members
            .AsNoTracking()
            .FirstOrDefault(i => i.Id == Convert.ToInt32(memberId));

            ApplicationStatuses oldMemberStatus = member.ApplicationStatusId;

            string paymentReceived = "";

            if (taskUpdateResource.ReviewDescision == MemberApplicationPaymentEnum.Received)
            {
                member.ApplicationStatusId = ApplicationStatuses.MemberRegistrationPaymentReceived;
                paymentReceived = "received";
            }
            //Pending
            else if (taskUpdateResource.ReviewDescision == MemberApplicationPaymentEnum.Pending)
            {
                member.ApplicationStatusId = ApplicationStatuses.MemberRegistrationAwaitingPayment;
                paymentReceived = "pending";
            }
            //Expired
            else if (taskUpdateResource.ReviewDescision == MemberApplicationPaymentEnum.Expired)
            {
                member.ApplicationStatusId = ApplicationStatuses.MemberRegistrationDisqualified;
                member.DisqualificationReason = "Payment not received within grace period";
                paymentReceived = "notReceived";
            }

            _dbContext.Set<Member>().Update(member);
            _dbContext.SaveChanges();

            MemberStagingChangeLogResource stagingChangeLog = new();

            stagingChangeLog.Changes.Add(new StagingChange
            {
                Name = "Application Status",
                OldValue = EnumHelper.GetEnumIdValuePair<ApplicationStatuses>((int)oldMemberStatus).Value,
                NewValue = EnumHelper.GetEnumIdValuePair<ApplicationStatuses>((int)member.ApplicationStatusId).Value
            });

            if (!string.IsNullOrWhiteSpace(member.DisqualificationReason))
            {
                stagingChangeLog.Changes.Add(new StagingChange
                {
                    Name = "Disqualification Reason",
                    OldValue = "",
                    NewValue = member.DisqualificationReason
                });
            }

            CreateMemberStatusUpdateEventLog(member, stagingChangeLog, user);

            using (var client = new HttpClient())
            {
                var taskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                        {
                            {
                                "variables",
                                new Dictionary<string, Dictionary<string, string>>
                                {
                                    {
                                        "fullMemberPaymentReceived",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", paymentReceived },
                                            { "type", "String" }
                                        }
                                    }
                                }
                            }
                        };

                var json = JsonConvert.SerializeObject(taskVariables, new JsonSerializerSettings
                {
                    ContractResolver = contractResolver,
                    Formatting = Formatting.Indented
                });
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });

                result.EnsureSuccessStatusCode();
            }
        }

        public void CompleteFinancialAdminCheckAssessmentInvoicePaymentTask(string taskId, FinancialAdminMemberReviewInputDTO reviewDTO, User user)
        {
            var contractResolver = new DefaultContractResolver
            {
                NamingStrategy = new CamelCaseNamingStrategy()
            };

            var task = _globalHelper.GetTaskAsync(taskId);
            var variables = _globalHelper.GetVariablesAsync(task.ProcessInstanceId);

            var memberId = variables.FirstOrDefault(i => i.Name == "OrganisationID").Value;

            string paymentReceived = "";

            var member = _dbContext.Members
                .AsNoTracking()
                .FirstOrDefault(i => i.Id == Convert.ToInt32(memberId));

            ApplicationStatuses oldMemberStatus = member.ApplicationStatusId;

            if (reviewDTO.ReviewDescision == MemberApplicationPaymentEnum.Received)
            {
                paymentReceived = "yes";
                member.ApplicationStatusId = ApplicationStatuses.MemberRegistrationSHMFinalReview;
            }
            //Pending
            else if (reviewDTO.ReviewDescision == MemberApplicationPaymentEnum.Pending)
            {
                paymentReceived = "pending";
            }
            else if (reviewDTO.ReviewDescision == MemberApplicationPaymentEnum.Expired)
            {
                paymentReceived = "no";
                member.ApplicationStatusId = ApplicationStatuses.MemberRegistrationCancelled_InitialAssessmentInvoiceNotPaid;
                SendMemberApplicationCancellationEmail(member.Id);
            }

            var taskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                        {
                            {
                                "variables",
                                new Dictionary<string, Dictionary<string, string>>
                                {
                                    {
                                        "initialAssessmentInvoicePaid",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", paymentReceived },
                                            { "type", "String" }
                                        }
                                    }
                                }
                            }
                        };

            using (var client = new HttpClient())
            {
                var json = JsonConvert.SerializeObject(taskVariables, new JsonSerializerSettings
                {
                    ContractResolver = contractResolver,
                    Formatting = Formatting.Indented
                });
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                //  send a POST request
                var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });

                // on error throw a exception
                result.EnsureSuccessStatusCode();
            }

            _dbContext.Set<Member>().Update(member);
            _dbContext.SaveChanges();


            MemberStagingChangeLogResource stagingChangeLog = new();

            stagingChangeLog.Changes.Add(new StagingChange
            {
                Name = "Application Status",
                OldValue = EnumHelper.GetEnumIdValuePair<ApplicationStatuses>((int)oldMemberStatus).Value,
                NewValue = EnumHelper.GetEnumIdValuePair<ApplicationStatuses>((int)member.ApplicationStatusId).Value
            });

            CreateMemberStatusUpdateEventLog(member, stagingChangeLog, user);
        }

        public void SendMemberApplicationCancellationEmail(int memberId)
        {
            try
            {
                var member = await _dbContext.Members
                    .AsNoTracking()
                    .FirstOrDefaultAsync(i => i.Id == memberId);

                if (member != null)
                {
                    var mainContact = Helpers.Helpers.GetMemberMainContact(_dbContext, memberId);

                    if (mainContact != null)
                    {
                        var applicant = mainContact;
                        var placeholders = new List<KeyValuePair<string, string>>
                        {
                            new KeyValuePair<string, string>("[MemberRegisteredName]", member.RegisteredName)
                        };

                        _emailService.SendEmail(applicant.Email, applicant.FirstName, "Member Application Cancellation", "MemberApplicationCancellationApplicant.html", placeholders);
                    }
                }
            }
            catch (Exception ex)
            {
                var message = "Unable to email applicant for member application cancellation. Member Id " + memberId;
                Helpers.Helpers.LogError(_dbContext, ex, message);
                throw new Exception(message);
            }
        }

        public void CompleteSHMSRNSecondReviewTask(string taskId, SHMSRNReviewInputDTO reviewDTO, User user)
        {
            using (var client = new HttpClient())
            {
                var task = _globalHelper.GetTaskAsync(taskId);
                var currentTaskVariables = _globalHelper.GetVariablesAsync(task.ProcessInstanceId);

                SRN srn = null;
                var recentSRNStatusUpdate = new SRNStatusUpdateHistory();

                if (currentTaskVariables.Count > 0)
                {
                    var srnId = currentTaskVariables.FirstOrDefault(i => i.Name == "SRNId").Value;

                    srn = _dbContext.Set<SRN>()
                        .Include(i => i.SRNStatus)
                        .Include(i => i.SRNStatusUpdates)
                        .FirstOrDefault(i => i.Id == Convert.ToInt32(srnId));

                    recentSRNStatusUpdate = srn.SRNStatusUpdates.FirstOrDefault(i => i.ProcessInstanceId == task.ProcessInstanceId);
                }

                var newTaskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                    {
                        {
                            "variables",
                            new Dictionary<string, Dictionary<string, string>>
                            {
                                {
                                    "srnVerified2",
                                    new Dictionary<string, string>()
                                    {
                                        { "value", reviewDTO.IsVerified },
                                        { "type", "String" }
                                    }
                                }
                            }
                        }
                    };

                if (srn != null)
                {
                    var srnUpdateResource = _mapper.Map<SRNUpdateResource>(srn);
                    var entityBlob = JsonConvert.SerializeObject(srnUpdateResource);


                    var stagingChangeLog = new MemberStagingChangeLogResource();

                    var stagingChange = new StagingChange
                    {
                        Name = "SRN Status",
                        OldValue = srn.SRNStatus.Name
                    };

                    if (reviewDTO.IsVerified == "yes")
                    {
                        var status = _dbContext.SRNStatuses
                                .AsNoTracking()
                                .FirstOrDefault(i => i.Name == "Second Verification");

                        if (srn.FileType == SRNStatusFileTypes.MonthlyAndDailyFile && srn.SRNStatusId == recentSRNStatusUpdate.SRNStatusId)
                        {
                            _srnService.UpdateSRNStatus("Second Verification", srn);
                            stagingChange.NewValue = "Second Verification";
                        }
                        else if (srn.FileType == SRNStatusFileTypes.DailyFile || srn.FileType == SRNStatusFileTypes.MonthlyFile)
                        {
                            _srnService.UpdateSRNStatus("Second Verification", srn);
                            stagingChange.NewValue = "Second Verification";
                        }

                        recentSRNStatusUpdate.SRNStatusId = status.Id;
                    }
                    else if (reviewDTO.IsVerified == "no")
                    {
                        var status = _dbContext.SRNStatuses
                                .AsNoTracking()
                                .FirstOrDefault(i => i.Name == "Second Verification");

                        if (srn.FileType == SRNStatusFileTypes.MonthlyAndDailyFile && srn.SRNStatusId == recentSRNStatusUpdate.SRNStatusId)
                        {
                            _srnService.UpdateSRNStatus("Rejected", srn);
                            stagingChange.NewValue = "Rejected";
                        }
                        else if (srn.FileType == SRNStatusFileTypes.DailyFile || srn.FileType == SRNStatusFileTypes.MonthlyFile)
                        {
                            _srnService.UpdateSRNStatus("Rejected", srn);
                            stagingChange.NewValue = "Rejected";
                        }

                        recentSRNStatusUpdate.SRNStatusId = status.Id;
                    }

                    stagingChangeLog.Changes.Add(stagingChange);

                    _dbContext.SaveChanges();

                    var changeBlob = JsonConvert.SerializeObject(stagingChangeLog);

                    _globalHelper.CreateEventLog(_dbContext, user.Id, "SRN Update", srn.TradingName, entityBlob, changeBlob, srn.Id, "SRN");
                }

                var json = JsonConvert.SerializeObject(newTaskVariables);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });
                result.EnsureSuccessStatusCode();
            }
        }

        public void CompleteFinancialAdminCheckOnboardingInvoicePaymentTask(string taskId, FinancialAdminMemberReviewInputDTO reviewDTO, User user)
        {
            var contractResolver = new DefaultContractResolver
            {
                NamingStrategy = new CamelCaseNamingStrategy()
            };

            var task = _globalHelper.GetTaskAsync(taskId);
            var variables = _globalHelper.GetVariablesAsync(task.ProcessInstanceId);

            var memberId = variables.FirstOrDefault(i => i.Name == "OrganisationID").Value;

            var member = await _dbContext.Members
            .AsNoTracking()
            .FirstOrDefaultAsync(i => i.Id == Convert.ToInt32(memberId));

            ApplicationStatuses oldMemberStatus = member.ApplicationStatusId;

            string paymentReceived = "";

            if (reviewDTO.ReviewDescision == MemberApplicationPaymentEnum.Received)
            {
                paymentReceived = "yes";
                member.ApplicationStatusId = ApplicationStatuses.MemberRegistrationSHMFinalReview;
            }
            //Pending
            else if (reviewDTO.ReviewDescision == MemberApplicationPaymentEnum.Pending)
            {
                paymentReceived = "pending";
            }
            //Expired
            else if (reviewDTO.ReviewDescision == MemberApplicationPaymentEnum.Expired)
            {
                paymentReceived = "no";
                member.ApplicationStatusId = ApplicationStatuses.MemberRegistrationDisqualified;
                member.DisqualificationReason = "Onboarding invoice not paid";
            }

            var taskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                        {
                            {
                                "variables",
                                new Dictionary<string, Dictionary<string, string>>
                                {
                                    {
                                        "onboardingPaymentReceived",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", paymentReceived },
                                            { "type", "String" }
                                        }
                                    }
                                }
                            }
                        };


            _dbContext.Set<Member>().Update(member);
            _dbContext.SaveChanges();

            MemberStagingChangeLogResource stagingChangeLog = new();

            stagingChangeLog.Changes.Add(new StagingChange
            {
                Name = "Application Status",
                OldValue = EnumHelper.GetEnumIdValuePair<ApplicationStatuses>((int)oldMemberStatus).Value,
                NewValue = EnumHelper.GetEnumIdValuePair<ApplicationStatuses>((int)member.ApplicationStatusId).Value
            });

            if (!string.IsNullOrWhiteSpace(member.DisqualificationReason))
            {
                stagingChangeLog.Changes.Add(new StagingChange
                {
                    Name = "Disqualification Reason",
                    OldValue = "",
                    NewValue = member.DisqualificationReason
                });
            }

            CreateMemberStatusUpdateEventLog(member, stagingChangeLog, user);

            using (var client = new HttpClient())
            {
                var json = JsonConvert.SerializeObject(taskVariables, new JsonSerializerSettings
                {
                    ContractResolver = contractResolver,
                    Formatting = Formatting.Indented
                });
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                //  send a POST request
                var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });

                // on error throw a exception
                result.EnsureSuccessStatusCode();
            }

            if (paymentReceived == "no")
            {
                SendMemberApplicationCancellationEmail(member.Id);
            }
            
        }

        public void CompleteFinancialAdminCheckFullMemberPayment(string taskId, FinancialAdminMemberReviewInputDTO reviewDTO, User user)
        {
            var contractResolver = new DefaultContractResolver
            {
                NamingStrategy = new CamelCaseNamingStrategy()
            };

            var task = _globalHelper.GetTaskAsync(taskId);
            var variables = _globalHelper.GetVariablesAsync(task.ProcessInstanceId);

            var memberId = variables.FirstOrDefault(i => i.Name == "OrganisationID").Value;

            var member = await _dbContext.Members
            .AsNoTracking()
            .FirstOrDefaultAsync(i => i.Id == Convert.ToInt32(memberId));

            ApplicationStatuses oldMemberStatus = member.ApplicationStatusId;

            string paymentReceived = "";

            if (reviewDTO.ReviewDescision == MemberApplicationPaymentEnum.Received)
            {
                member.ApplicationStatusId = ApplicationStatuses.MemberRegistrationPaymentReceived;
                paymentReceived = "received";
            }
            //Pending
            else if (reviewDTO.ReviewDescision == MemberApplicationPaymentEnum.Pending)
            {
                member.ApplicationStatusId = ApplicationStatuses.MemberRegistrationAwaitingPayment;
                paymentReceived = "pending";
            }
            else if (reviewDTO.ReviewDescision == MemberApplicationPaymentEnum.Expired)
            {
                member.ApplicationStatusId = ApplicationStatuses.MemberRegistrationDisqualified;
                member.DisqualificationReason = "Payment not received within grace period";
                paymentReceived = "notReceived";
            }

            _dbContext.Set<Member>().Update(member);
            _dbContext.SaveChanges();

            MemberStagingChangeLogResource stagingChangeLog = new();

            stagingChangeLog.Changes.Add(new StagingChange
            {
                Name = "Application Status",
                OldValue = EnumHelper.GetEnumIdValuePair<ApplicationStatuses>((int)oldMemberStatus).Value,
                NewValue = EnumHelper.GetEnumIdValuePair<ApplicationStatuses>((int)member.ApplicationStatusId).Value
            });

            if (!string.IsNullOrWhiteSpace(member.DisqualificationReason))
            {
                stagingChangeLog.Changes.Add(new StagingChange
                {
                    Name = "Disqualification Reason",
                    OldValue = "",
                    NewValue = member.DisqualificationReason
                });
            }

            CreateMemberStatusUpdateEventLog(member, stagingChangeLog, user);

            using (var client = new HttpClient())
            {
                var taskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                        {
                            {
                                "variables",
                                new Dictionary<string, Dictionary<string, string>>
                                {
                                    {
                                        "fullMemberPaymentReceived",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", paymentReceived },
                                            { "type", "String" }
                                        }
                                    }
                                }
                            }
                        };

                var json = JsonConvert.SerializeObject(taskVariables, new JsonSerializerSettings
                {
                    ContractResolver = contractResolver,
                    Formatting = Formatting.Indented
                });
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });

                result.EnsureSuccessStatusCode();
            }
        }

        public void CompleteReviewSRNChangesTask(string taskId, TaskSRNChangesReviewDTO taskChangesReviewDTO)
        {
            var contractResolver = new DefaultContractResolver
            {
                NamingStrategy = new CamelCaseNamingStrategy()
            };

            var task = _globalHelper.GetTaskAsync(taskId);
            var variables = _globalHelper.GetVariablesAsync(task.ProcessInstanceId);

            string srnChangesDecision = "";
            if (taskChangesReviewDTO.ReviewDecision == SRNUpdateDetailsReviewOptions.Accepted)
                srnChangesDecision = "accepted";
            else if (taskChangesReviewDTO.ReviewDecision == SRNUpdateDetailsReviewOptions.Rejected)
                srnChangesDecision = "rejected";

            if (variables.Count > 0 && !string.IsNullOrEmpty(srnChangesDecision))
            {
                var taskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                        {
                            {
                                "variables",
                                new Dictionary<string, Dictionary<string, string>>
                                {
                                    {
                                        "srnChangesDecision",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srnChangesDecision },
                                            { "type", "String" }
                                        }
                                    }
                                }
                            }
                        };

                var srnVariables = variables.FirstOrDefault(i => i.Name == "ChangeRequestId");
                var requestId = (srnVariables != null) ? srnVariables.Value : "0";

                if (!string.IsNullOrEmpty(requestId))
                {
                    var changeRequest = _camundaServiceHelper.GetMemberChangeRequest(Convert.ToInt32(requestId));
                    if (changeRequest != null)
                    {
                        if (taskChangesReviewDTO.ReviewDecision == SRNUpdateDetailsReviewOptions.Accepted)
                        {
                            changeRequest.Status = ChangeRequestStatus.Accepted;
                            _dbContext.Set<ChangeRequestStaging>().Update(changeRequest);
                        }

                        _dbContext.SaveChanges();
                    }
                }

                using (var client = new HttpClient())
                {
                    var json = JsonConvert.SerializeObject(taskVariables, new JsonSerializerSettings
                    {
                        Formatting = Formatting.Indented
                    });
                    var content = new StringContent(json, Encoding.UTF8, "application/json");
                    var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                    var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });
                    result.EnsureSuccessStatusCode();
                }
            }
        }

        public async Task<List<string>> CompleteTasksInBulk(string taskDefinitionKey, Dictionary<string, Dictionary<string, Dictionary<string, string>>> taskVariables, User user)
        {
            using var client = new HttpClient();
            var uri = _configSettings.CamundaBaseAddress + "/task?taskDefinitionKey=" + taskDefinitionKey;
            HttpResponseMessage taskListHTTPResult;
            var tasksThatCouldNotBeCompleted = new List<string>();
            string taskListResultString;
            var taskList = new List<TaskGetResource>();

            taskListHTTPResult = client.Send(new HttpRequestMessage(HttpMethod.Get, uri));
            taskListHTTPResult.EnsureSuccessStatusCode();
            taskListResultString = taskListHTTPResult.Content.ReadAsString();
            taskList = JsonConvert.DeserializeObject<List<TaskGetResource>>(taskListResultString);

            foreach (var task in taskList)
            {
                var taskVariablesResultWithProcInstId = client.Send(new HttpRequestMessage(HttpMethod.Get, _configSettings.CamundaBaseAddress + "/variable-instance?processInstanceIdIn=" + task.ProcessInstanceId));
                var taskVariablesResultWithTaskId = client.Send(new HttpRequestMessage(HttpMethod.Get, _configSettings.CamundaBaseAddress + "/task/" + task.Id + "/variables"));
                var taskVariableStringWithProcInstId = taskVariablesResultWithProcInstId.Content.ReadAsString();
                var taskVariableStringWithTaskId = taskVariablesResultWithTaskId.Content.ReadAsString();
                var jsonObject = JObject.Parse(taskVariableStringWithTaskId);
                var jsonArray = JArray.Parse(taskVariableStringWithProcInstId);
                var found = false;

                foreach (JObject content in jsonArray.Children<JObject>())
                {
                    foreach (JProperty prop in content.Properties())
                    {
                        if (prop.Name == "type" && prop.FirstOrDefault().Value<string>() == "Object")
                        {
                            content.Remove();
                            found = true;

                            break;
                        }
                    }

                    if (found)
                    {
                        break;
                    }
                }

                var variablesResultStringModified = jsonArray.ToString();
                var variablesResourceList = JsonConvert.DeserializeObject<List<VariableInstanceGetResource>>(variablesResultStringModified);
                var json = JsonConvert.SerializeObject(taskVariables);
                var httpContent = new StringContent(json, Encoding.UTF8, "application/json");
                var completeTaskURL = _configSettings.CamundaBaseAddress + "/task/" + task.Id + "/complete";
                var entityId = GetEntityId(task, variablesResourceList);
                var entityName = GetEntityName(task, variablesResourceList, ref tasksThatCouldNotBeCompleted);
                var changeType = GetChangeType(task);

                if (entityName != null)
                {

                    var completeTaskResult = client.Send(new HttpRequestMessage(HttpMethod.Post, completeTaskURL) { Content = httpContent });

                    if (completeTaskResult.StatusCode != System.Net.HttpStatusCode.OK && completeTaskResult.StatusCode != System.Net.HttpStatusCode.NoContent)
                    {
                        tasksThatCouldNotBeCompleted.Add(completeTaskResult.Content.ReadAsString());
                        continue;
                    }

                    Helpers.Helpers.CreateEventLog(_dbContext, user.Id, changeType, entityName, JsonConvert.SerializeObject(jsonObject), null, entityId, "Automated Task Completion");
                }
            }

            return tasksThatCouldNotBeCompleted;
        }

        internal int GetEntityId(TaskGetResource task, List<VariableInstanceGetResource> variablesResourceList)
        {
            var entityId = 0;

            switch (task.TaskDefinitionKey)
            {
                // SRN Application
                case "UserTask_ConfirmMigrationTesting":
                case "Task_CompleteAndUpdateDTH":
                case "Task_AddTestSRNToDTH":
                case "Task_IsSRNLive":
                case "Task_SHMReview":
                {
                    if (variablesResourceList.Find(x => x.Name == "SRNId") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Name == "SRNId").Value);
                    }
                    else
                    {
                        entityId = -1;
                    }
                    break;
                }

                // member-auto-close-on-srn-closure
                case "Task_cancelMember":
                {
                    if (variablesResourceList.Find(x => x.Name == "MemberId") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Name == "MemberId").Value);
                    }
                    else if (variablesResourceList.Find(x => x.Name == "OrganisationID") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Name == "OrganisationID").Value);
                    }
                    else if (variablesResourceList.Find(x => x.Name == "memberId") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Name == "memberId").Value);
                    }
                    else
                    {
                        entityId = -1;
                    }
                    break;
                }

                // member-status-update-V1
                case "Task_RemoveMemberFromBilling":
                case "Task_RemoveMemberUsersFromDTH":
                {
                    if (variablesResourceList.Find(x => x.Name == "MemberId") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Name == "MemberId").Value);
                    }
                    else if (variablesResourceList.Find(x => x.Name == "OrganisationID") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Name == "OrganisationID").Value);
                    }
                    else if (variablesResourceList.Find(x => x.Name == "memberId") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Name == "memberId").Value);
                    }
                    else
                    {
                        entityId = -1;
                    }
                    break;
                }

                // member-update-details-V1
                case "Task_reviewMemberDetailsUpdate":
                {
                    if (variablesResourceList.Find(x => x.Name == "MemberId") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Name == "MemberId").Value);
                    }
                    else if (variablesResourceList.Find(x => x.Name == "OrganisationID") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Name == "OrganisationID").Value);
                    }
                    else if (variablesResourceList.Find(x => x.Name == "memberId") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Name == "memberId").Value);
                    }
                    else
                    {
                        entityId = -1;
                    }
                    break;
                }

                // new-member-takeon-V5
                case "Task_allocateStakeHolderManagerManager":
                case "Task_ReviewMemberApplication":
                case "Task_generateInvoice":
                case "Task_checkIfPaymentReceived":
                case "Task_complete":
                case "UserTask_0lowgau":
                case "UserTask_0zkahxr":
                case "UserTask_0gomw05":
                case "Task_0tatwfs":
                {
                    if (variablesResourceList.Find(x => x.Name == "MemberId") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Name == "MemberId").Value);
                    } else if (variablesResourceList.Find(x => x.Name == "OrganisationID") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Name == "OrganisationID").Value);
                    } else if (variablesResourceList.Find(x => x.Name == "memberId") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Name == "memberId").Value);
                    } else
                    {
                        entityId = -1;
                    }

                    break;
                }

                // srn-status-update-non-cancellations-V1
                case "Task_SRN_Status_Changed_Confirm_Updated_DTH":
                {
                    if (variablesResourceList.Find(x => x.Name == "SRNId") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Name == "SRNId").Value);
                    }
                    else
                    {
                        entityId = -1;
                    }
                    break;
                }

                // srn-status-update-V1
                case "Task_0hfuhxh":
                case "Task_16s7ny3":
                {
                    if (variablesResourceList.Find(x => x.Name == "SRNId") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Name == "SRNId").Value);
                    }
                    else
                    {
                        entityId = -1;
                    }
                    break;
                }

                // srn-update-details-V2
                case "Task_reviewSRNDetailsUpdate":
                {
                    if (variablesResourceList.Find(x => x.Name == "SRNId") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Name == "SRNId").Value);
                    }
                    else
                    {
                        entityId = -1;
                    }
                    break;
                }

                default:
                    break;
            }

            return entityId;
        }

        internal string GetEntityName(TaskGetResource task, List<VariableInstanceGetResource> variablesResourceList, ref List<string> tasksThatCouldNotBeCompleted)
        {
            var entityId = 0;
            var entityName = "";

            switch (task.TaskDefinitionKey)
            {
                // SRN Application
                case "UserTask_ConfirmMigrationTesting":
                case "Task_CompleteAndUpdateDTH":
                case "Task_AddTestSRNToDTH":
                case "Task_IsSRNLive":
                case "Task_SHMReview":
                {
                    if (variablesResourceList.Find(x => x.Name == "SRNId") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Name == "SRNId").Value);
                    }
                    else
                    {
                        entityId = -1;
                    }

                    if (entityId > 0)
                    {
                        var srn = _dbContext.SRNs.Where(x => x.Id == entityId).FirstOrDefault();

                        if (srn == null)
                        {
                            tasksThatCouldNotBeCompleted.Add("Unable to complete task for SRN ID " + entityId + ". SRN does not exist.");
                            return null;
                        }

                        entityName = srn.TradingName;
                    } else
                    {
                        entityName = "UNKNOWN ENTITY";
                    }
                    break;
                }

                // member-auto-close-on-srn-closure
                case "Task_cancelMember":
                {
                    if (variablesResourceList.Find(x => x.Name == "MemberId") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Name == "MemberId").Value);
                    }
                    else if (variablesResourceList.Find(x => x.Name == "OrganisationID") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Name == "OrganisationID").Value);
                    }
                    else if (variablesResourceList.Find(x => x.Name == "memberId") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Name == "memberId").Value);
                    }
                    else
                    {
                        entityId = -1;
                    }

                    if (entityId > 0)
                    {
                        var member = _dbContext.Members.Where(x => x.Id == entityId).FirstOrDefault();

                        if (member == null)
                        {
                            tasksThatCouldNotBeCompleted.Add("Unable to complete task for member ID " + entityId + ". Member does not exist.");
                            return null;
                        }

                        entityName = member.RegisteredName;
                    } else
                    {
                        entityName = "UNKNOWN ENTITY";
                    }
                    break;
                }

                // member-status-update-V1
                case "Task_RemoveMemberFromBilling":
                case "Task_RemoveMemberUsersFromDTH":
                {
                    if (variablesResourceList.Find(x => x.Name == "MemberId") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Name == "MemberId").Value);
                    }
                    else if (variablesResourceList.Find(x => x.Name == "OrganisationID") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Name == "OrganisationID").Value);
                    }
                    else if (variablesResourceList.Find(x => x.Name == "memberId") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Name == "memberId").Value);
                    }
                    else
                    {
                        entityId = -1;
                    }

                    if (entityId > 0)
                    {
                        var member = _dbContext.Members.Where(x => x.Id == entityId).FirstOrDefault();

                        if (member == null)
                        {
                            tasksThatCouldNotBeCompleted.Add("Unable to complete task for member ID " + entityId + ". Member does not exist.");
                            return null;
                        }

                        entityName = member.RegisteredName;
                    }
                    else
                    {
                        entityName = "UNKNOWN ENTITY";
                    }
                    break;
                }

                // member-update-details-V1
                case "Task_reviewMemberDetailsUpdate":
                {
                    if (variablesResourceList.Find(x => x.Name == "MemberId") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Name == "MemberId").Value);
                    }
                    else if (variablesResourceList.Find(x => x.Name == "OrganisationID") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Name == "OrganisationID").Value);
                    }
                    else if (variablesResourceList.Find(x => x.Name == "memberId") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Name == "memberId").Value);
                    }
                    else
                    {
                        entityId = -1;
                    }

                    if (entityId > 0)
                    {
                        var member = _dbContext.Members.Where(x => x.Id == entityId).FirstOrDefault();

                        if (member == null)
                        {
                            tasksThatCouldNotBeCompleted.Add("Unable to complete task for member ID " + entityId + ". Member does not exist.");
                            return null;
                        }

                        entityName = member.RegisteredName;
                    }
                    else
                    {
                        entityName = "UNKNOWN ENTITY";
                    }
                    break;
                }

                // new-member-takeon-V5
                case "Task_allocateStakeHolderManagerManager":
                case "Task_ReviewMemberApplication":
                case "Task_generateInvoice":
                case "Task_checkIfPaymentReceived":
                case "Task_complete":
                case "UserTask_0lowgau":
                case "UserTask_0zkahxr":
                case "UserTask_0gomw05":
                case "Task_0tatwfs":
                {
                    if (variablesResourceList.Find(x => x.Name == "MemberId") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Name == "MemberId").Value);
                    }
                    else if (variablesResourceList.Find(x => x.Name == "OrganisationID") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Name == "OrganisationID").Value);
                    }
                    else if (variablesResourceList.Find(x => x.Name == "memberId") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Name == "memberId").Value);
                    }
                    else
                    {
                        entityId = -1;
                    }

                    if (entityId > 0)
                    {
                        var member = _dbContext.Members.Where(x => x.Id == entityId).FirstOrDefault();

                        if (member == null)
                        {
                            tasksThatCouldNotBeCompleted.Add("Unable to complete task for member ID " + entityId + ". Member does not exist.");
                            return null;
                        }

                        entityName = member.RegisteredName;
                    }
                    else
                    {
                        entityName = "UNKNOWN ENTITY";
                    }
                    break;
                }

                // srn-status-update-non-cancellations-V1
                case "Task_SRN_Status_Changed_Confirm_Updated_DTH":
                {
                    if (variablesResourceList.Find(x => x.Name == "SRNId") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Name == "SRNId").Value);
                    }
                    else
                    {
                        entityId = -1;
                    }

                    if (entityId > 0)
                    {
                        var srn = _dbContext.SRNs.Where(x => x.Id == entityId).FirstOrDefault();

                        if (srn == null)
                        {
                            tasksThatCouldNotBeCompleted.Add("Unable to complete task for SRN ID " + entityId + ". SRN does not exist.");
                            return null;
                        }

                        entityName = srn.TradingName;
                    }
                    else
                    {
                        entityName = "UNKNOWN ENTITY";
                    }
                    break;
                }

                // srn-status-update-V1
                case "Task_0hfuhxh":
                case "Task_16s7ny3":
                {
                    if (variablesResourceList.Find(x => x.Name == "SRNId") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Name == "SRNId").Value);
                    }
                    else
                    {
                        entityId = -1;
                    }

                    if (entityId > 0)
                    {
                        var srn = _dbContext.SRNs.Where(x => x.Id == entityId).FirstOrDefault();

                        if (srn == null)
                        {
                            tasksThatCouldNotBeCompleted.Add("Unable to complete task for SRN ID " + entityId + ". SRN does not exist.");
                            return null;
                        }

                        entityName = srn.TradingName;
                    }
                    else
                    {
                        entityName = "UNKNOWN ENTITY";
                    }
                    break;
                }

                // srn-update-details-V2
                case "Task_reviewSRNDetailsUpdate":
                {
                    if (variablesResourceList.Find(x => x.Name == "SRNId") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Name == "SRNId").Value);
                    }
                    else
                    {
                        entityId = -1;
                    }

                    if (entityId > 0)
                    {
                        var srn = _dbContext.SRNs.Where(x => x.Id == entityId).FirstOrDefault();

                        if (srn == null)
                        {
                            tasksThatCouldNotBeCompleted.Add("Unable to complete task for SRN ID " + entityId + ". SRN does not exist.");
                            return null;
                        }

                        entityName = srn.TradingName;
                    }
                    else
                    {
                        entityName = "UNKNOWN ENTITY";
                    }
                    break;
                }

                default:
                    break;
            }

            return entityName;
        }

        internal string GetChangeType(TaskGetResource task)
        {
            var changeType = "";

            switch (task.TaskDefinitionKey)
            {
                // SRN Application
                case "UserTask_ConfirmMigrationTesting":
                {
                    changeType = "Is testing/ migration confirmed?";
                    break;
                }

                case "Task_CompleteAndUpdateDTH":
                {
                    changeType = "Make it live on DTH";
                    break;
                }

                case "Task_AddTestSRNToDTH":
                {
                    changeType = "Create Test SRN on DTH";
                    break;
                }

                case "Task_IsSRNLive":
                {
                    changeType = "Is SRN Live?";
                    break;
                }

                case "Task_SHMReview":
                {
                    changeType = "SHM Reviews SRN(s) Application";
                    break;
                }

                // member-auto-close-on-srn-closure
                case "Task_cancelMember":
                {
                    changeType = "Member has no active SRNs. Cancel Member?";
                    break;
                }

                // member-status-update-V1
                case "Task_RemoveMemberFromBilling":
                {
                    changeType = "Add/remove member from billing";
                    break;
                }

                case "Task_RemoveMemberUsersFromDTH":
                {
                    changeType = "Add/remove member user(s) from DTH";
                    break;
                }

                // member-update-details-V1
                case "Task_reviewMemberDetailsUpdate":
                {
                    changeType = "SHM review and approves update";
                    break;
                }

                // new-member-takeon-V5
                case "Task_allocateStakeHolderManagerManager":
                {
                    changeType = "Group Stakeholder Manager Allocates SHM";
                    break;
                }

                case "Task_ReviewMemberApplication":
                {
                    changeType = "Review and facilitate a Member Application";
                    break;
                }

                case "Task_generateInvoice":
                {
                    changeType = "Generate full member invoice";
                    break;
                }

                case "Task_checkIfPaymentReceived":
                {
                    changeType = "Check payment and acceptance of constitution review";
                    break;
                }

                case "Task_complete":
                {
                    changeType = "Complete final take on";
                    break;
                }

                case "UserTask_0lowgau":
                {
                    changeType = "Generate assessment invoice";
                    break;
                }

                case "UserTask_0zkahxr":
                {
                    changeType = "Check if assessement invoice paid";
                    break;
                }

                case "UserTask_0gomw05":
                {
                    changeType = "Create onboarding Invoice";
                    break;
                }

                case "Task_0tatwfs":
                {
                    changeType = "Check if onboarding invoice paid";
                    break;
                }

                // srn-status-update-non-cancellations-V1
                case "Task_SRN_Status_Changed_Confirm_Updated_DTH":
                {
                    changeType = "SRN Status Changed Confirm Updated DTH";
                    break;
                }

                // srn-status-update-V1
                case "Task_0hfuhxh":
                {
                    changeType = "Confirm file submission";
                    break;
                }

                case "Task_16s7ny3":
                {
                    changeType = "SRN Status Changed Confirm Updated DTH";
                    break;
                }

                // srn-update-details-V2
                case "Task_reviewSRNDetailsUpdate":
                {
                    changeType = "SHM review and approves update";
                    break;
                }

                default:
                    break;
            }

            return "Bulk Task Complete - " + changeType;
        }

        public void CompleteSACRRAAdminUpdateMemberUsersOnDTHTask(string taskId)
        {
            using (var client = new HttpClient())
            {
                var task = _globalHelper.GetTaskAsync(taskId);
                var currentTaskVariables = _globalHelper.GetVariablesAsync(task.ProcessInstanceId);

                if (currentTaskVariables.Count > 0)
                {
                    var memberIdVariable = currentTaskVariables.FirstOrDefault(i => i.Name == "memberId").Value;
                    int memberId = (!string.IsNullOrEmpty(memberIdVariable)) ? Convert.ToInt32(memberIdVariable) : 0;

                    if (memberId > 0)
                    {
                        var newTaskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                            {
                                {
                                    "variables",
                                    new Dictionary<string, Dictionary<string, string>>{}
                                }
                            };

                        var json = JsonConvert.SerializeObject(newTaskVariables);
                        var content = new StringContent(json, Encoding.UTF8, "application/json");
                        var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                        var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });
                        result.EnsureSuccessStatusCode();
                    }
                }
            }
        }

        public void CompleteFinancialdminUpdateMemberBillingTask(string taskId)
        {
            using (var client = new HttpClient())
            {
                var task = _globalHelper.GetTaskAsync(taskId);
                var currentTaskVariables = _globalHelper.GetVariablesAsync(task.ProcessInstanceId);

                if (currentTaskVariables.Count > 0)
                {
                    var memberIdVariable = currentTaskVariables.FirstOrDefault(i => i.Name == "memberId").Value;
                    int memberId = (!string.IsNullOrEmpty(memberIdVariable)) ? Convert.ToInt32(memberIdVariable) : 0;

                    if (memberId > 0)
                    {
                        var newTaskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                            {
                                {
                                    "variables",
                                    new Dictionary<string, Dictionary<string, string>>{}
                                }
                            };

                        var json = JsonConvert.SerializeObject(newTaskVariables);
                        var content = new StringContent(json, Encoding.UTF8, "application/json");
                        var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                        var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });
                        result.EnsureSuccessStatusCode();
                    }
                }
            }
        }

        public async Task<List<DWExceptionTaskItemDTO>> GetDWExceptionTasks(User user)
        {
            return GetDWExceptionUserTasks(user.Id.ToString());
        }
        private async Task<List<DWExceptionTaskItemDTO>> GetDWExceptionUserTasks(string assignee)
        {
            try
            {
                using (var client = new HttpClient())
                {
                    string definitionKey = "&processDefinitionKey=New-DW-Exception";

                    var uri = _configSettings.CamundaBaseAddress + "/task?assignee=" + assignee + definitionKey;
                    var result = client.Send(new HttpRequestMessage(HttpMethod.Get, uri));

                    result.EnsureSuccessStatusCode();

                    var resultString = result.Content.ReadAsString();
                    var tasksResourceList = JsonConvert.DeserializeObject<List<TaskGetResource>>(resultString);

                    if (tasksResourceList.Count > 0)
                    {
                        var dwtaskItems = PopulateDWExceptionTaskDetails(tasksResourceList);
                        return dwtaskItems;
                    }

                    return new List<DWExceptionTaskItemDTO>();
                }
            }
            catch (Exception ex)
            {
                var message = "Unable to retrieve camunda user tasks for assignee " + assignee;
                Log.Error(ex, message);
                throw new Exception(message);
            }
        }

        private async Task<List<DWExceptionTaskItemDTO>> PopulateDWExceptionTaskDetails(List<TaskGetResource> tasksResourceList)
        {
            try
            {
                using (var client = new HttpClient())
                {
                    var dwTaskItemList = new List<DWExceptionTaskItemDTO>();

                    foreach (var task in tasksResourceList)
                    {
                        var variablesUri = _configSettings.CamundaBaseAddress + "/variable-instance?processInstanceIdIn=" + task.ProcessInstanceId;
                        var variables = client.Send(new HttpRequestMessage(HttpMethod.Get, variablesUri));
                        variables.EnsureSuccessStatusCode();

                        var variablesResultString = variables.Content.ReadAsString();

                        JArray array = JArray.Parse(variablesResultString);
                        var found = false;

                        foreach (JObject content in array.Children<JObject>())
                        {
                            foreach (JProperty prop in content.Properties())
                            {
                                if (prop.Name == "type" && prop.FirstOrDefault().Value<string>() == "Object")
                                {
                                    content.Remove();
                                    found = true;
                                    break;
                                }
                            }

                            if (found)
                            {
                                break;
                            }
                        }

                        var variablesResultStringModified = array.ToString();
                        var variablesResourceList = JsonConvert.DeserializeObject<List<VariableInstanceGetResource>>(variablesResultStringModified);

                        var fctWarehouseExceptionIDVariable = variablesResourceList.FirstOrDefault(i => i.Name == "FctWarehouseExceptionID");
                        long fctWarehouseExceptionID = 0;

                        if (fctWarehouseExceptionIDVariable != null)
                        {
                            fctWarehouseExceptionID = Convert.ToInt64(fctWarehouseExceptionIDVariable.Value);
                        }

                        if (fctWarehouseExceptionID > 0)
                        {
                            var dWException = _dWExceptionService.GetByDWExceptionId(fctWarehouseExceptionID);

                            var dwTaskItem = _mapper.Map<DWExceptionTaskItemDTO>(dWException);
                            dwTaskItem.TaskId = task.Id;
                            dwTaskItem.TaskName = task.Name;
                            dwTaskItem.TaskDefinitionKey = task.TaskDefinitionKey;

                            dwTaskItemList.Add(dwTaskItem);
                        }
                    }

                    return dwTaskItemList;
                }
            }
            catch (Exception ex)
            {
                var message = "Unable to retrieve camunda tasks";
                Log.Error(ex, message);
                throw new Exception(message);
            }
        }

        public void CompleteStakeHolderManagerDWException(string taskId, TaskCompleteDWExceptionDTO taskReview, User user)
        {
            using (var client = new HttpClient())
            {
                var task = _globalHelper.GetTaskAsync(taskId);
                var currentTaskVariables = _globalHelper.GetVariablesAsync(task.ProcessInstanceId);

                var newTaskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                            {
                                {
                                    "variables",
                                    new Dictionary<string, Dictionary<string, string>>{}
                                }
                            };

                var json = JsonConvert.SerializeObject(newTaskVariables);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });
                result.EnsureSuccessStatusCode();


                var fctWarehouseExceptionIDVariable = currentTaskVariables.FirstOrDefault(i => i.Name == "FctWarehouseExceptionID");
                long fctWarehouseExceptionID = 0;

                if (fctWarehouseExceptionIDVariable != null)
                    fctWarehouseExceptionID = Convert.ToInt64(fctWarehouseExceptionIDVariable.Value);

                if (fctWarehouseExceptionID > 0)
                {
                    var updatedDWException = _dWExceptionService.CloseException(fctWarehouseExceptionID, taskReview.Comments);

                    var statusId = "";
                    if (updatedDWException.ExceptionStatus == "Open")
                        statusId = "1";
                    else if (updatedDWException.ExceptionStatus == "Closed")
                        statusId = "2";


                    var updateResource = new DWAExternalPIExceptionUpdateDTO
                    {
                        ColumnsKeyValuePair = new Dictionary<string, string>
                                        {
                                            { "FctWarehouseExceptionID", updatedDWException.FctWarehouseExceptionID.ToString() },
                                            { "DimExceptionStatusID", statusId },
                                            { "UpdateComment", updatedDWException.Comments },
                                            { "CamundaID", task.Id },
                                            { "UpdatedBy", user.FullName },
                                            { "DateUpdated", DateTime.Now.ToString() }
                                        }
                    };

                    var apiResult = UpdateDWExternalAPI(updateResource);
                }
            }
        }

        private int UpdateDWExternalAPI(DWAExternalPIExceptionUpdateDTO updateResouce)
        {
            string requestUrlString = $"Updates";
            string tableName = "API.FctWarehouseExceptionUpdate";

            var restClient = _globalHelper.GetRestClient(_dwBaseApiUrl);

            var request = new RestRequest(requestUrlString, Method.Put);
            request.AddParameter("apiKey", DW_API_KEY, ParameterType.QueryString);
            request.AddParameter("datasetName", _dwDataset, ParameterType.QueryString);
            request.AddParameter("tableName", tableName, ParameterType.QueryString);
            request.AddJsonBody(updateResouce);
            var restResponse = await restClient.ExecuteAsync<int>(request);
            _globalHelper.CheckForAPIRequestError(restResponse);

            return restResponse.Data;
        }

        public void CompleteMemberAutoCloseSHMTask(string taskId, SHMCompleteMemberAutoCloseTaskDTO taskDecision)
        {
            var task = _globalHelper.GetTaskAsync(taskId);
            var variables = _globalHelper.GetVariablesAsync(task.ProcessInstanceId);

            string memberCloseDecision = "";
            if (taskDecision.MemberCloseDecision == MemberAutoCloseSHMTaskOptionsEnum.PostponeCancellation)
            {
                memberCloseDecision = "postpone";
            }
            else if (taskDecision.MemberCloseDecision == MemberAutoCloseSHMTaskOptionsEnum.DontCancelMember)
            {
                memberCloseDecision = "dontCancel";
            }
            else if (taskDecision.MemberCloseDecision == MemberAutoCloseSHMTaskOptionsEnum.CancelMember)
            {
                memberCloseDecision = "cancel";
            }

            if (variables.Count > 0 && !string.IsNullOrEmpty(memberCloseDecision))
            {
                var taskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                        {
                            {
                                "variables",
                                new Dictionary<string, Dictionary<string, string>>
                                {
                                    {
                                        "cancelMember",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", memberCloseDecision },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "postponeDate",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", (taskDecision.PostponeDate != null 
                                                && taskDecision.PostponeDate != DateTime.MinValue)? string.Format("{0:yyyy-MM-dd}", taskDecision.PostponeDate) : null },
                                            { "type", "String" }
                                        }
                                    }
                                }
                            }
                        };

                
                using (var client = new HttpClient())
                {
                    var json = JsonConvert.SerializeObject(taskVariables, new JsonSerializerSettings
                    {
                        Formatting = Formatting.Indented
                    });
                    var content = new StringContent(json, Encoding.UTF8, "application/json");
                    var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                    var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });
                    result.EnsureSuccessStatusCode();
                }
            }
        }

        private void CreateMemberStatusUpdateEventLog(Sacrra.Membership.Database.Models.Member member, MemberStagingChangeLogResource stagingChangeLog, User user)
        {
            var updateDetailsBlob = JsonConvert.SerializeObject(member, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore });

            var stagingDetailsBlob = JsonConvert.SerializeObject(stagingChangeLog);

            if (stagingChangeLog.Changes.Count > 0)
            {
                var userId = 0;
                if (user != null)
                {
                    userId = user.Id;
                }

                Helpers.Helpers.CreateEventLog(_dbContext, userId, "Member Update", member.RegisteredName, updateDetailsBlob, stagingDetailsBlob, member.Id, "Member");
            }
        }

        public void BulkCompleteUserTasks(string emailAddress, string taskName = null, string processDefinitionKey = null)
        {
            try
            {
                var user = _dbContext.Users.FirstOrDefault(i => i.Email == emailAddress);

                if(user == null)
                {
                    return;
                }

                var assignee = user.Id.ToString();

                if (!string.IsNullOrWhiteSpace(assignee))
                {
                    using (var client = new HttpClient())
                    {
                        var definitionKey = (!string.IsNullOrEmpty(processDefinitionKey)) ? "&processDefinitionKey=" + processDefinitionKey : null;
                        var nameLike = (!string.IsNullOrEmpty(taskName)) ? "&name=" + taskName : null;
                        var uri = _configSettings.CamundaBaseAddress + "/task" + "?assignee=" + assignee + definitionKey + nameLike;

                        var result = client.Send(new HttpRequestMessage(HttpMethod.Get, uri));

                        result.EnsureSuccessStatusCode();

                        var resultString = result.Content.ReadAsString();
                        var tasksResourceList = JsonConvert.DeserializeObject<List<TaskGetResource>>(resultString);

                        if (tasksResourceList != null)
                        {
                            var newTaskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                            {
                                {
                                    "variables",
                                    new Dictionary<string, Dictionary<string, string>>{}
                                }
                            };

                            foreach (var task in tasksResourceList)
                            {
                                var json = JsonConvert.SerializeObject(newTaskVariables);
                                var content = new StringContent(json, Encoding.UTF8, "application/json");
                                uri = _configSettings.CamundaBaseAddress + "/task/" + task.Id + "/complete";
                                var completeResult = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                var message = "Unable to retrieve camunda user tasks for email " + emailAddress;
                Log.Error(ex, message);
                throw new Exception(message);
            }
        }

        public MigrationPlanGenerationOutputDTO GenerateProcessDefinitionMigrationPlan(MigrationPlanGenerationDTO migrationPlanGenerationDTO)
        {
            if(migrationPlanGenerationDTO != null)
            {
                using (var client = new HttpClient())
                {
                    var newTaskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                            {
                                {
                                    "variables",
                                    new Dictionary<string, Dictionary<string, string>>()
                                    {
                                        {
                                            "testEndDate",
                                            new Dictionary<string, string>()
                                            {
                                                { "value", "" },
                                                { "type", "String" }
                                            }
                                        }
                                    }
                                }
                            };

                    //migrationPlanGenerationDTO.Variables = null;

                    var json = JsonConvert.SerializeObject(migrationPlanGenerationDTO, Formatting.Indented, new JsonSerializerSettings
                    {
                         Formatting = Formatting.Indented,
                         ContractResolver= new CamelCasePropertyNamesContractResolver()
                    });

                    var content = new StringContent(json, Encoding.UTF8, "application/json");
                    var uri = _configSettings.CamundaBaseAddress + "/migration/generate";
                    var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });

                    var resultString = result.Content.ReadAsString();
                    var migrationPlanOutput = JsonConvert.DeserializeObject<MigrationPlanGenerationOutputDTO>(resultString);

                    return migrationPlanOutput;
                }
            }

            return null;
        }

        public MigrationPlanValidationOutputDTO ValidateProcessDefinitionMigrationPlan(MigrationPlanValidationInputDTO migrationPlanValidationDTO)
        {
            if (migrationPlanValidationDTO != null)
            {
                using (var client = new HttpClient())
                {

                    var json = JsonConvert.SerializeObject(migrationPlanValidationDTO, Formatting.Indented, new JsonSerializerSettings
                    {
                        Formatting = Formatting.Indented,
                        ContractResolver = new CamelCasePropertyNamesContractResolver()
                    });

                    var content = new StringContent(json, Encoding.UTF8, "application/json");
                    var uri = _configSettings.CamundaBaseAddress + "/migration/validate";
                    var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });

                    var resultString = result.Content.ReadAsString();
                    var migrationPlanOutput = JsonConvert.DeserializeObject<MigrationPlanValidationOutputDTO>(resultString);

                    return migrationPlanOutput;
                }
            }

            return null;
        }
        public void ExecuteProcessDefinitionMigrationPlan(MigrationPlanExecutionInputDTO migrationPlanExecutionDTO)
        {
            if (migrationPlanExecutionDTO != null)
            {
                using (var client = new HttpClient())
                {
                    var json = JsonConvert.SerializeObject(migrationPlanExecutionDTO, Formatting.Indented, new JsonSerializerSettings
                    {
                        Formatting = Formatting.Indented,
                        ContractResolver = new CamelCasePropertyNamesContractResolver()
                    });

                    var content = new StringContent(json, Encoding.UTF8, "application/json");
                    var uri = _configSettings.CamundaBaseAddress + "/migration/execute";
                    var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });
                    var resultString = result.Content.ReadAsString();

                    if (!result.IsSuccessStatusCode)
                    {
                        throw new Exception(resultString);
                    }
                }
            }
        }

        public async Task<List<ProcessInstanceInfoResource>> GetProcessInstanceList(string processDefinitionKey)
        {
            try
            {
                using (var client = new HttpClient())
                {
                    var definitionKey = (!string.IsNullOrEmpty(processDefinitionKey)) ? "?processDefinitionKey=" + processDefinitionKey : null;
                    var uri = _configSettings.CamundaBaseAddress + "/process-instance" + definitionKey;

                    var result = client.Send(new HttpRequestMessage(HttpMethod.Get, uri));

                    result.EnsureSuccessStatusCode();

                    var resultString = result.Content.ReadAsString();
                    var tasksResourceList = JsonConvert.DeserializeObject<List<ProcessInstanceInfoResource>>(resultString);

                    return tasksResourceList;
                }
            }
            catch (Exception ex)
            {
                var message = "Unable to retrieve process instances for definition key " + processDefinitionKey;
                Log.Error(ex, message);
                throw new Exception(message);
            }
        }

        public void AddTestEndDateVariableToSRNProcessInstances()
        {
            var definitionKeys = new List<string>
                {
                    "New-SRN-Application",
                    "SRN-Status-Update-To-Test"
                };

            foreach(var definitionKey in definitionKeys)
            {
                var processInstances = GetProcessInstanceList(definitionKey);
                
                foreach (var processInstance in processInstances)
                {
                    var srnData = _dbContext.SRNStatusUpdateHistory.FirstOrDefault(i => i.ProcessInstanceId == processInstance.Id);

                    if (srnData != null)
                    {
                        using (var client = new HttpClient())
                        {
                            var uri = $"{_configSettings.CamundaBaseAddress}/process-instance/{processInstance.Id}/variables/testEndDate";
                            var testEndDate = "";

                            if(srnData.FileType == SRNStatusFileTypes.DailyFile)
                            {
                                testEndDate = string.Format("{0:yyyy-MM-dd}", srnData.DailyFileTestEndDate);
                            }
                            else if (srnData.FileType == SRNStatusFileTypes.MonthlyFile)
                            {
                                testEndDate = string.Format("{0:yyyy-MM-dd}", srnData.MonthlyFileTestEndDate);
                            }

                            var variable = new
                            {
                                value = testEndDate,
                                type = "String"
                            };

                            var json = JsonConvert.SerializeObject(variable, new JsonSerializerSettings
                            {
                                Formatting = Formatting.Indented
                            });
                            var content = new StringContent(json, Encoding.UTF8, "application/json");

                            var result = client.PutAsync(uri, content);

                            result.EnsureSuccessStatusCode();
                        }
                    }
                }
            }
        }

        public async Task<List<SRNHasTaskDTO>> GetSRNTasksBySRNNumber(string srnNumber)
        {
            try
            {
                var sRNHasTaskDTO = new List<SRNHasTaskDTO>();
                var srnData = _dbContext.SRNs.FirstOrDefaultAsync(i => i.SRNNumber == srnNumber) ?? throw new Exception($"SRN Number not found. {srnNumber}");
                using var client = new HttpClient { Timeout = Timeout.InfiniteTimeSpan };
                var variableName = "SRNId";
                var definitionKey = "&varialbleValue=" + srnData.Id;
                var uri = $"{_configSettings.CamundaBaseAddress}/variable-instance?variableName={variableName}{definitionKey}";
                var result = client.Send(new HttpRequestMessage(HttpMethod.Get, uri));

                result.EnsureSuccessStatusCode();

                var resultString = result.Content.ReadAsString();
                var tasksResourceList = JsonConvert.DeserializeObject<List<CamundaTaskDTO>>(resultString)?.Find(x => x.Value == srnData.Id);

                if (tasksResourceList == null)
                {
                    return sRNHasTaskDTO;
                }
                
                var taskUri = $"{_configSettings.CamundaBaseAddress}/task?processInstanceId={tasksResourceList.ProcessInstanceId}";
                var taskRes = client.Send(new HttpRequestMessage(HttpMethod.Get, taskUri));
                taskRes.EnsureSuccessStatusCode();

                var taskResString = taskRes.Content.ReadAsString();
                var taskList = JsonConvert.DeserializeObject<List<SrnHasTask>>(taskResString)
                    .FirstOrDefault(x => x.ProcessInstanceId == tasksResourceList.ProcessInstanceId);

                if (taskList == null)
                {
                    return sRNHasTaskDTO;
                }

                var userUri = $"{_configSettings.CamundaBaseAddress}/user?id={taskList.Assignee}";
                var userRes = client.Send(new HttpRequestMessage(HttpMethod.Get, userUri));
                userRes.EnsureSuccessStatusCode();

                var userResString = userRes.Content.ReadAsString();
                var userList = JsonConvert.DeserializeObject<List<TaskUser>>(userResString)
                    .FirstOrDefault(x => x.Id == taskList.Assignee);

                if (userList == null)
                {
                    return sRNHasTaskDTO;
                }

                var processDefinitionComponents = taskList.ProcessDefinitionId.Split(':');
                var processName = processDefinitionComponents[0];
                var processVersion = processDefinitionComponents[1];
                var processId = processDefinitionComponents[2];

                sRNHasTaskDTO.Add(new SRNHasTaskDTO
                {
                    SRNNumber = tasksResourceList.Value,
                    TaskName = taskList.Name,
                    ProcessInstanceId = taskList.ProcessInstanceId,
                    ProcessDefinitionId = processId,
                    ProcessDefinationName = processName,
                    ProcessDefinationVersion = processVersion,
                    Assignee = $"{userList.FirstName} {userList.LastName}",
                    Created = taskList.Created,
                    Due = taskList.Due
                });

                return sRNHasTaskDTO;
            }
            catch (Exception ex)
            {
                var message = $"Unable to fetch tasks for SRN.";
                throw new Exception(message, ex);
            }
        }

        public async Task<List<ShmReplacementFileTaskOutputDTO>> GetStakeHolderManagerReplacementFileTasks(List<string> processDefinitionKeys, User user)
        {
            var replacementFileTaskList = new List<ShmReplacementFileTaskOutputDTO>();
            ReplacementFileSubmission replacementFileSubmission;

            foreach (var key in processDefinitionKeys)
            {
                var userTasks = _globalHelper.GetUserTasks(user.Id.ToString(), key);
                var mappedTasks = _mapper.Map<List<ShmReplacementFileTaskOutputDTO>>(userTasks);

                replacementFileTaskList.AddRange(mappedTasks);
            }


            foreach (var task in replacementFileTaskList)
            {
                replacementFileSubmission = _dbContext.ReplacementFileSubmissions.Where(x => x.Id == task.FileSubmissionRequestId)
                    .Include(x => x.ReplacementFileSubmissionReason)
                    .Include(x => x.Member)
                    .FirstOrDefault();
                task.ReSubmissionReason = replacementFileSubmission.ReplacementFileSubmissionReason.Name;
                task.NumberOfRecords = replacementFileSubmission.NumberOfRecords;
                task.ProposedSubmissionDate = replacementFileSubmission.PlannedSubmissionDate.ToShortDateString();
                task.FileName = replacementFileSubmission.ReplacementFileName;
                task.Member = replacementFileSubmission.Member.RegisteredName;
            }

            return replacementFileTaskList;
        }

        public void CompleteStakeholderManagerReplacementFileSubmissionRequest(string taskId, StakeholderManagerReplacementFileRequestReviewInputDTO inputDTO)
        {
            using (var transaction = _dbContext.Database.BeginTransaction())
            {
                var task = _globalHelper.GetTask(taskId);
                var existingTaskVariables = _globalHelper.GetVariables(task.ProcessInstanceId);
                var taskVariables = new
                {
                    variables = new
                    {
                        reviewDecision = new { value = inputDTO.ReviewDecision == true ? "approved" : "declined", type = "string" },
                        reasonForDecline = new { value = inputDTO.ReasonForDeclineId, type = "long" },
                        plannedSubmissionDate = new { value = DateTime.Parse(inputDTO.PlannedSubmissionDate).ToString("yyyy-MM-dd"), type = "string" }
                    }
                };

                if (task == null)
                {
                    throw new Exception($"Task ({task.Name}) was not found.");
                }

                try
                {
                    var client = new RestClient();
                    var restRequest = new RestRequest(_configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete")
                        .AddJsonBody(JsonConvert.SerializeObject(taskVariables));
                    var response = client.Post(restRequest);
                    var replacementFileSubmissionId = int.Parse(existingTaskVariables.Find(x => x.Name == "FileSubmissionRequestId").Value);
                    var replacementFileSubmission = _dbContext.ReplacementFileSubmissions
                        .Where(x => x.Id == replacementFileSubmissionId)
                        .FirstOrDefault();
                    var bureauList = _dbContext.Members
                        .Where(x => x.MembershipTypeId == MembershipTypes.Bureau)
                        .ToList();

                    if ((response.StatusCode == HttpStatusCode.NoContent || response.StatusCode == HttpStatusCode.OK) && inputDTO.ReviewDecision == false)
                    {
                        var replacementFileScheduleEntry = new ReplacementFileSchedule()
                        {
                            ReplacementFileName = replacementFileSubmission.ReplacementFileName,
                            ReplacementFileSubmissionId = replacementFileSubmissionId
                        };

                        _dbContext.ReplacementFileSchedule.Add(replacementFileScheduleEntry);
                        _dbContext.SaveChanges();
                    }

                    if ((response.StatusCode == HttpStatusCode.NoContent || response.StatusCode == HttpStatusCode.OK) && inputDTO.ReviewDecision == true)
                    {
                        foreach (var bureau in bureauList)
                        {
                            if (bureau.RegisteredName.ToLower() == "unknown")
                            {
                                continue;
                            }

                            var replacementFileScheduleEntry = new ReplacementFileSchedule()
                            {
                                BureauId = bureau.Id,
                                ReplacementFileBureauStatusId = ReplacementFileBureauStatuses.BureauLoadPending,
                                ReplacementFileName = replacementFileSubmission.ReplacementFileName,
                                ReplacementFileSubmissionId = replacementFileSubmissionId
                            };

                            _dbContext.ReplacementFileSchedule.Add(replacementFileScheduleEntry);
                        }
                    }

                    _dbContext.SaveChanges();
                    transaction.Commit();
                }
                catch (Exception exception)
                {
                    _globalHelper.LogError(_dbContext, exception, "Unable to complete task ({task.Name}). Request failed.");
                    throw new Exception($"Unable to complete task ({task.Name}). Request failed.");
                }
            }
        }

        public RestResponse CancelReplacementFile(CompleteReplacementFileDueInputDTO completeReplacementFileDueInputDTO)
        {
            var client = new RestClient();
            RestRequest restRequest;
            ReplacementFileSubmission replacementFile;
            var taskVariables = new
            {
                variables = new
                {
                    fileSubmittedOnPlannedDate = new { value = "cancelled", type = "string" }
                }
            };

            try
            {
                replacementFile = _dbContext.ReplacementFileSubmissions.Where(x => x.Id == completeReplacementFileDueInputDTO.ReplacementFileSubmissionRequestId).FirstOrDefault();
                replacementFile.ReasonForCancellation = completeReplacementFileDueInputDTO.ReasonForCancellation;
                _dbContext.SaveChanges();

                restRequest = new RestRequest(_configSettings.CamundaBaseAddress + "/task/" + completeReplacementFileDueInputDTO.TaskId + "/complete")
                    .AddJsonBody(JsonConvert.SerializeObject(taskVariables));

                return client.Post(restRequest);
            }
            catch (Exception exception)
            {
                _globalHelper.LogError(_dbContext, exception, $"Unable to complete task to check if replacement file was submitted to DTH ({completeReplacementFileDueInputDTO.TaskId})");
                throw new Exception($"Unable to complete task to check if replacement file was submitted to DTH ({completeReplacementFileDueInputDTO.TaskId})");
            }
        }

        public RestResponse PostponeReplacementFile(CompleteReplacementFileDueInputDTO completeReplacementFileDueInputDTO)
        {
            var client = new RestClient();
            RestRequest restRequest;
            var taskVariables = new
            {
                variables = new
                {
                    fileSubmittedOnPlannedDate = new { value = "submitDatePostponed", type = "string" },
                    plannedSubmissionDate = new { value = completeReplacementFileDueInputDTO.NewPlannedSubmissionDate, type = "string" }
                }
            };

            try
            {
                restRequest = new RestRequest(_configSettings.CamundaBaseAddress + "/task/" + completeReplacementFileDueInputDTO.TaskId + "/complete")
                    .AddJsonBody(JsonConvert.SerializeObject(taskVariables));

                return client.Post(restRequest);
            }
            catch (Exception exception)
            {
                _globalHelper.LogError(_dbContext, exception, $"Unable to complete task to check if replacement file was submitted to DTH ({completeReplacementFileDueInputDTO.TaskId})");
                throw new Exception($"Unable to complete task to check if replacement file was submitted to DTH ({completeReplacementFileDueInputDTO.TaskId})");
            }
        }


        public RestResponse RecheckDthSubmission(CompleteReplacementFileDueInputDTO completeReplacementFileDueInputDTO)
        {
            var client = new RestClient();
            RestRequest restRequest;
            var taskVariables = new
            {
                variables = new
                {
                    recheckDthSubmission = new { value = "true", type = "boolean" }
                }
            };

            try
            {
                restRequest = new RestRequest(_configSettings.CamundaBaseAddress + "/task/" + completeReplacementFileDueInputDTO.TaskId + "/complete")
                    .AddJsonBody(JsonConvert.SerializeObject(taskVariables));

                return client.Post(restRequest);
            }
            catch (Exception exception)
            {
                _globalHelper.LogError(_dbContext, exception, $"Unable to complete task to check if replacement file was submitted to DTH ({completeReplacementFileDueInputDTO.TaskId})");
                throw new Exception($"Unable to complete task to check if replacement file was submitted to DTH ({completeReplacementFileDueInputDTO.TaskId})");
            }
        }

        public RestResponse SHMReviewReplacementFileSubmitted(CompleteReplacementFileDueInputDTO shmReviewReplacementFileSubmittedInputDTO)
        {
            var client = new RestClient();
            RestRequest restRequest;
            var taskVariables = new
            {
                variables = new
                {
                    fileSubmittedOnPlannedDate = new { value = "submitted", type = "string" }
                }
            };

            try
            {
                restRequest = new RestRequest(_configSettings.CamundaBaseAddress + "/task/" + shmReviewReplacementFileSubmittedInputDTO.TaskId + "/complete")
                    .AddJsonBody(JsonConvert.SerializeObject(taskVariables));

                return client.Post(restRequest);
            }
            catch (Exception exception)
            {
                _globalHelper.LogError(_dbContext, exception, $"Unable to complete task to check if replacement file was submitted to DTH ({shmReviewReplacementFileSubmittedInputDTO.TaskId})");
                throw new Exception($"Unable to complete task to check if replacement file was submitted to DTH ({shmReviewReplacementFileSubmittedInputDTO.TaskId})");
            }
        }

        public async Task<List<ShmAdhocFileTaskOutputDTO>> GetStakeHolderManagerAdhocFileTasks(List<string> processDefinitionKeys, User user)
        {
            var adhocFileTaskList = new List<ShmAdhocFileTaskOutputDTO>();
            AdhocFileSubmission adhocFileSubmission;

            foreach (var key in processDefinitionKeys)
            {
                var userTasks = _globalHelper.GetUserTasks(user.Id.ToString(), key);
                var mappedTasks = _mapper.Map<List<ShmAdhocFileTaskOutputDTO>>(userTasks);

                adhocFileTaskList.AddRange(mappedTasks);
            }


            foreach (var task in adhocFileTaskList)
            {
                adhocFileSubmission = _dbContext.AdhocFileSubmissions
                    .Where(x => x.Id == task.FileSubmissionRequestId)
                    .Include(x => x.FileSubmissionReason)
                    .Include(x => x.Member)
                    .FirstOrDefault();

                if (adhocFileSubmission != null)
                {
                    task.AdhocReasonId = adhocFileSubmission.FileSubmissionReason.Id;
                    task.AdhocReason = adhocFileSubmission.FileSubmissionReason.Name;
                    task.NumberOfRecords = adhocFileSubmission.NumberOfRecords;
                    task.ProposedSubmissionDate = adhocFileSubmission.PlannedSubmissionDate.ToShortDateString();
                    task.FileName = adhocFileSubmission.FileName;
                    task.Member = adhocFileSubmission.Member.RegisteredName;
                    task.Comments = adhocFileSubmission.Comments;
                }
            }

            return adhocFileTaskList;
        }

        public void CompleteStakeholderManagerAdHocFileSubmissionRequest(string taskId, StakeholderManagerAdhocFileRequestReviewInputDTO inputDTO)
        {
            using (var transaction = _dbContext.Database.BeginTransaction())
            {
                var task = _globalHelper.GetTask(taskId);
                var existingTaskVariables = _globalHelper.GetVariables(task.ProcessInstanceId);
                var taskVariables = new
                {
                    variables = new
                    {
                        reviewDecision = new { value = inputDTO.ReviewDecision == true ? "approved" : "declined", type = "string" },
                        reasonForDecline = new { value = inputDTO.ReasonForDeclineId, type = "long" },
                        plannedSubmissionDate = new { value = DateTime.Parse(inputDTO.PlannedSubmissionDate).ToString("yyyy-MM-dd"), type = "string" }
                    }
                };

                if (task == null)
                {
                    throw new Exception($"Task ({task.Name}) was not found.");
                }

                try
                {
                    var client = new RestClient();
                    var restRequest = new RestRequest(_configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete")
                        .AddJsonBody(JsonConvert.SerializeObject(taskVariables));
                    var response = client.Post(restRequest);
                    var adhocFileSubmissionId = int.Parse(existingTaskVariables.Find(x => x.Name == "FileSubmissionRequestId").Value);
                    var adhocFileSubmission = _dbContext.AdhocFileSubmissions
                        .Where(x => x.Id == adhocFileSubmissionId)
                        .Where(x => x.FileName == inputDTO.FileName)
                        .FirstOrDefault();
                    var bureauList = _dbContext.Members
                        .Where(x => x.MembershipTypeId == MembershipTypes.Bureau)
                        .ToList();

                    if ((response.StatusCode == HttpStatusCode.NoContent || response.StatusCode == HttpStatusCode.OK) && inputDTO.ReviewDecision == false)
                    {
                        var adhocFileScheduleEntry = new AdhocFileSchedule()
                        {
                            BureauId = 0,
                            AdhocFileName = adhocFileSubmission.FileName,
                            AdhocFileBureauStatusId = ReplacementFileBureauStatuses.BureauAdHocFileDeclined,
                            AdhocFileSubmissionId = adhocFileSubmissionId
                        };

                        adhocFileSubmission.Comments = inputDTO.Comments;
                        adhocFileSubmission.AdhocFileSubmissionReasonId = inputDTO.AdhocReasonId;
                        _dbContext.AdhocFileSubmissions.Update(adhocFileSubmission);
                        _dbContext.AdhocFileSchedules.Add(adhocFileScheduleEntry);
                        _dbContext.SaveChanges();
                    }

                    if ((response.StatusCode == HttpStatusCode.NoContent || response.StatusCode == HttpStatusCode.OK) && inputDTO.ReviewDecision == true)
                    {
                        foreach (var bureau in bureauList)
                        {
                            if (bureau.RegisteredName.ToLower() == "unknown")
                            {
                                continue;
                            }

                            var adhocFileSchedule = new AdhocFileSchedule()
                            {
                                BureauId = bureau.Id,
                                AdhocFileBureauStatusId = ReplacementFileBureauStatuses.BureauLoadPending,
                                AdhocFileName = adhocFileSubmission.FileName,
                                AdhocFileSubmission = adhocFileSubmission
                            };

                            _dbContext.AdhocFileSchedules.Add(adhocFileSchedule);
                        }

                        adhocFileSubmission.Comments = inputDTO.Comments;
                        adhocFileSubmission.AdhocFileSubmissionReasonId = inputDTO.AdhocReasonId;
                        _dbContext.AdhocFileSubmissions.Update(adhocFileSubmission);
                    }

                    _dbContext.SaveChanges();
                    transaction.Commit();
                }
                catch (Exception exception)
                {
                    // Don't pass the same DbContext instance to avoid concurrent access issues
                    _globalHelper.LogError(null, exception, "Unable to complete task ({task.Name}). Request failed.");
                    throw new Exception($"Unable to complete task ({task.Name}). Request failed.");
                }
            }
        }

        public RestResponse CancelAdhocFile(CompleteAdhocFileDueInputDTO completeAdhocFileDueInputDTO)
        {
            var client = new RestClient();
            RestRequest restRequest;
            AdhocFileSubmission adhocFileSubmission;
            var taskVariables = new
            {
                variables = new
                {
                    fileSubmittedOnPlannedDate = new { value = "cancelled", type = "string" }
                }
            };

            try
            {
                adhocFileSubmission = _dbContext.AdhocFileSubmissions.Where(x => x.Id == completeAdhocFileDueInputDTO.AdhocFileSubmissionRequestId).FirstOrDefault();
                adhocFileSubmission.ReasonForDeletion = completeAdhocFileDueInputDTO.ReasonForCancellation;
                _dbContext.SaveChanges();

                restRequest = new RestRequest(_configSettings.CamundaBaseAddress + "/task/" + completeAdhocFileDueInputDTO.TaskId + "/complete")
                    .AddJsonBody(JsonConvert.SerializeObject(taskVariables));

                return client.Post(restRequest);
            }
            catch (Exception exception)
            {
                _globalHelper.LogError(_dbContext, exception, $"Unable to complete task to check if adhoc file was submitted to DTH ({completeAdhocFileDueInputDTO.TaskId})");
                throw new Exception($"Unable to complete task to check if adhoc file was submitted to DTH ({completeAdhocFileDueInputDTO.TaskId})");
            }
        }

        public RestResponse PostponeAdhocFile(CompleteAdhocFileDueInputDTO completeAdhocFileDueInputDTO)
        {
            var client = new RestClient();
            RestRequest restRequest;
            var taskVariables = new
            {
                variables = new
                {
                    fileSubmittedOnPlannedDate = new { value = "submitDatePostponed", type = "string" },
                    plannedSubmissionDate = new { value = completeAdhocFileDueInputDTO.NewPlannedSubmissionDate, type = "string" }
                }
            };

            try
            {
                restRequest = new RestRequest(_configSettings.CamundaBaseAddress + "/task/" + completeAdhocFileDueInputDTO.TaskId + "/complete")
                    .AddJsonBody(JsonConvert.SerializeObject(taskVariables));

                return client.Post(restRequest);
            }
            catch (Exception exception)
            {
                _globalHelper.LogError(_dbContext, exception, $"Unable to complete task to check if adhoc file was submitted to DTH ({completeAdhocFileDueInputDTO.TaskId})");
                throw new Exception($"Unable to complete task to check if adhoc file was submitted to DTH ({completeAdhocFileDueInputDTO.TaskId})");
            }
        }

        public RestResponse RecheckAdhocDthSubmission(CompleteAdhocFileDueInputDTO completeAdhocFileDueInputDTO)
        {
            var client = new RestClient();
            RestRequest restRequest;
            var taskVariables = new
            {
                variables = new
                {
                    recheckDthSubmission = new { value = "true", type = "boolean" }
                }
            };

            try
            {
                restRequest = new RestRequest(_configSettings.CamundaBaseAddress + "/task/" + completeAdhocFileDueInputDTO.TaskId + "/complete")
                    .AddJsonBody(JsonConvert.SerializeObject(taskVariables));

                return client.Post(restRequest);
            }
            catch (Exception exception)
            {
                _globalHelper.LogError(_dbContext, exception, $"Unable to complete task to check if adhoc file was submitted to DTH ({completeAdhocFileDueInputDTO.TaskId})");
                throw new Exception($"Unable to complete task to check if adhoc file was submitted to DTH ({completeAdhocFileDueInputDTO.TaskId})");
            }
        }

        public RestResponse SHMReviewAdhocFileSubmitted(CompleteAdhocFileDueInputDTO shmReviewAdhocFileSubmittedInputDTO)
        {
            var client = new RestClient();
            RestRequest restRequest;
            var taskVariables = new
            {
                variables = new
                {
                    fileSubmittedOnPlannedDate = new { value = "submitted", type = "string" }
                }
            };

            try
            {
                restRequest = new RestRequest(_configSettings.CamundaBaseAddress + "/task/" + shmReviewAdhocFileSubmittedInputDTO.TaskId + "/complete")
                    .AddJsonBody(JsonConvert.SerializeObject(taskVariables));

                return client.Post(restRequest);
            }
            catch (Exception exception)
            {
                _globalHelper.LogError(_dbContext, exception, $"Unable to complete task to check if replacement file was submitted to DTH ({shmReviewAdhocFileSubmittedInputDTO.TaskId})");
                throw new Exception($"Unable to complete task to check if replacement file was submitted to DTH ({shmReviewAdhocFileSubmittedInputDTO.TaskId})");
            }
        }
    }
}
