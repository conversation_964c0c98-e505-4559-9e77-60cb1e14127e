using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using RestSharp;
using Sacrra.Membership.Business.Resources.Camunda.Task;
using Sacrra.Membership.Business.Resources.IdValuePair;
using Sacrra.Membership.Business.Resources.MemberChangeRequest;
using Sacrra.Membership.Business.Resources.SRN;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;
using Serilog;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Reflection;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;

namespace Sacrra.Membership.Business.Helpers
{
    public static class Helpers
    {
        public static void CreateHashAndSalt(string password, out byte[] passwordHash, out byte[] passwordSalt)
        {
            using (var hmac = new System.Security.Cryptography.HMACSHA256())
            {
                passwordSalt = hmac.Key;
                passwordHash = hmac.ComputeHash(System.Text.Encoding.UTF8.GetBytes(password));
            }
        }

        public static bool VerifyHashAndSaft(string password, byte[] passwordHash, byte[] passwordSalt)
        {
            using (var hmac = new System.Security.Cryptography.HMACSHA256(passwordSalt))
            {
                var computedHash = hmac.ComputeHash(System.Text.Encoding.UTF8.GetBytes(password));

                for (var i = 0; i < computedHash.Length; i++)
                {
                    if (computedHash[i] != passwordHash[i])
                        return false;
                }
            }

            return true;
        }
        public static void PrepareMemberForUpdate (AppDbContext _context, Member entity)
        {
            // Sometimes when you want to update a detached entity, before attempting to attach it (by setting the .State property),
            // you first need to make sure the entity isn't already attached and being tracked. If this is the case, the existing entity
            // needs to be detached, and the updated entity, attached.
            var attachedEntity = _context.ChangeTracker.Entries<Member>().FirstOrDefault(e => e.Entity.Id == entity.Id);
            if (attachedEntity != null)
            {
                // the entity you want to update is already attached, we need to detach it and attach the updated entity instead
                _context.Entry(attachedEntity.Entity).State = EntityState.Detached;
            }

            _context.Entry(entity).State = EntityState.Modified; // Attach entity, and set State to Modified.
            _context.Entry(entity).Property(o => o.ApplicationStatusId).IsModified = true;
        }
        public static void PrepareSRNForUpdate(AppDbContext _context, SRN entity)
        {
            var attachedEntity = _context.ChangeTracker.Entries<SRN>().FirstOrDefault(e => e.Entity.Id == entity.Id);
            if (attachedEntity != null)
            {
                _context.Entry(attachedEntity.Entity).State = EntityState.Detached;
            }

            _context.Entry(entity).State = EntityState.Modified;
            _context.Entry(entity).Property(o => o.MemberId).IsModified = true;
        }
        public static void PrepareSRNSaleRequestForUpdate(AppDbContext _context, SRNSaleRequest entity)
        {
            var attachedEntity = _context.ChangeTracker.Entries<SRNSaleRequest>().FirstOrDefault(e => e.Entity.Id == entity.Id);
            if (attachedEntity != null)
            {
                _context.Entry(attachedEntity.Entity).State = EntityState.Detached;
            }

            _context.Entry(entity).State = EntityState.Modified;
            _context.Entry(entity).Property(o => o.Status).IsModified = true;
        }
        public static void PrepareSRNSplitRequestForUpdate(AppDbContext _context, SRN entity)
        {
            var attachedEntity = _context.ChangeTracker.Entries<SRN>().FirstOrDefault(e => e.Entity.Id == entity.Id);
            if (attachedEntity != null)
            {
                _context.Entry(attachedEntity.Entity).State = EntityState.Detached;
            }

            _context.Entry(entity).State = EntityState.Modified;
            _context.Entry(entity).Property(o => o.SRNStatusId).IsModified = true;
        }
        public static void PrepareUserForUpdate(AppDbContext _context, User entity)
        {
            var attachedEntity = _context.ChangeTracker.Entries<User>().FirstOrDefault(e => e.Entity.Id == entity.Id);
            if (attachedEntity != null)
            {
                _context.Entry(attachedEntity.Entity).State = EntityState.Detached;
            }

            _context.Entry(entity).State = EntityState.Modified;
            _context.Entry(entity).Property(o => o.RoleId).IsModified = true;
        }
        public static void PrepareChangeRequestForUpdate(AppDbContext _context, ChangeRequestStaging entity)
        {
            var attachedEntity = _context.ChangeTracker.Entries<ChangeRequestStaging>().FirstOrDefault(e => e.Entity.Id == entity.Id);
            if (attachedEntity != null)
            {
                _context.Entry(attachedEntity.Entity).State = EntityState.Detached;
            }

            _context.Entry(entity).State = EntityState.Deleted;
        }
        public static void PrepareBranchLocationForUpdate(AppDbContext _context, BranchLocation entity)
        {
            var attachedEntity = _context.ChangeTracker.Entries<BranchLocation>().FirstOrDefault(e => e.Entity.Id == entity.Id);
            if (attachedEntity != null)
            {
                _context.Entry(attachedEntity.Entity).State = EntityState.Detached;
            }

            _context.Entry(entity).State = EntityState.Deleted;
        }
        public static void PrepareSRNContactForUpdate(AppDbContext _context, SRNContact entity)
        {
            var attachedEntity = _context.ChangeTracker.Entries<SRNContact>().FirstOrDefault(e => e.Entity.Id == entity.Id);
            if (attachedEntity != null)
            {
                _context.Entry(attachedEntity.Entity).State = EntityState.Detached;
            }
        }
        public static string GetPropertyDisplayName(PropertyInfo prop)
        {
            var displayName = prop.Name;
            var attributes = prop.GetCustomAttributes(typeof(DisplayAttribute),
                false);

            if (attributes != null)
            {
                if (attributes.Length > 0)
                {
                    displayName = attributes.Cast<DisplayAttribute>().Single().Name;
                }
            }

            return displayName;
        }
        public static bool IsPropertyForeignKey(PropertyInfo prop)
        {
            bool isForeignKey = false;
            var groupName = "";
            var attributes = prop.GetCustomAttributes(typeof(DisplayAttribute),
                false);

            if (attributes != null)
            {
                if (attributes.Length > 0)
                {
                    groupName = attributes.Cast<DisplayAttribute>().Single().GroupName;
                    if (groupName == "ForeignKey")
                        isForeignKey = true;
                }
            }

            return isForeignKey;
        }

        public async static Task<string> GetPropertyVaueById (AppDbContext context, int id, string propertyName)
        {
            string propertyValue = string.Empty;

            switch (propertyName)
            {
                case "LoanManagementSystemVendorId":
                    var entity = context.LoanManagementSystemVendors.FirstOrDefaultAsync(i => i.Id == id);
                    if (entity != null)
                        propertyValue = entity.Name;
                    break;

                case "SoftwareVendorId":
                    var vendor = context.SoftwareVendors.FirstOrDefaultAsync(i => i.Id == id);
                    if (vendor != null)
                        propertyValue = vendor.Name;
                    break;

                case "AccountTypeId":
                    var type = context.AccountTypes.FirstOrDefaultAsync(i => i.Id == id);
                    if (type != null)
                        propertyValue = type.Name;
                    break;

                case "NCRReportingAccountTypeClassificationId":
                    var typeClassification = context.NCRReportingAccountTypeClassifications.FirstOrDefaultAsync(i => i.Id == id);
                    if (typeClassification != null)
                        propertyValue = typeClassification.Name;
                    break;

                case "SPGroupId":
                    var spGroup = context.SPGroups.FirstOrDefaultAsync(i => i.Id == id);
                    if (spGroup != null)
                        propertyValue = spGroup.SPNumber;
                    break;
                case "SRNStatusId":
                    var srnStatus = context.SRNStatuses.FirstOrDefaultAsync(i => i.Id == id);
                    if (srnStatus != null)
                        propertyValue = srnStatus.Name;
                    break;
            }

            return propertyValue;
        }
        public static string GetEnumValue(string propertyName, int id)
        {
            string propertyValue = string.Empty;

            switch (propertyName)
            {
                case "MembershipTypeId":
                    var entity = EnumHelper.GetEnumIdValuePair<MembershipTypes>(id);
                    if (entity != null)
                        propertyValue = entity.Value;
                    break;

                case "PrincipleDebtRangeId":
                    var vendor = EnumHelper.GetEnumIdValuePair<PrincipleDebtRanges>(id);
                    if (vendor != null)
                        propertyValue = vendor.Value;
                    break;

                case "IndustryClassificationId":
                    var type = EnumHelper.GetEnumIdValuePair<IndustryClassifications>(id);
                    if (type != null)
                        propertyValue = type.Value;
                    break;

                case "NcrReportingPrimaryBusinessClassificationId":
                    var typeClassification = EnumHelper.GetEnumIdValuePair<NcrReportingPrimaryBusinessClassifications>(id);
                    if (typeClassification != null)
                        propertyValue = typeClassification.Value;
                    break;
            }

            return propertyValue;
        }
        public static void CreateEventLog(AppDbContext _dbContext, int userId, string changeType, string entityName, string entityBlob, string changeBlob)
        {
            var user = _dbContext.Users.FirstOrDefaultAsync(i => i.Id == userId);
            var userFullName = "";
            if (user != null)
                userFullName = user.FirstName + " " + user.LastName;

            var eventLog = new EventLog
            {
                User = userFullName,
                Date = DateTime.Now,
                ChangeType = changeType,
                EntityName = entityName,
                EntityBlob = entityBlob,
                ChangeBlob = changeBlob
            };

            _dbContext.Add(eventLog);
            _dbContext.SaveChanges();
        }
        public static void CreateEventLog(AppDbContext _dbContext, int userId, string changeType, string entityName, string entityBlob, string changeBlob, int entityId, string entityTypeName)
        {
            var userFullName = "";
            if (userId > 0)
            {
                var user = _dbContext.Users.FirstOrDefaultAsync(i => i.Id == userId);
                if (user != null)
                    userFullName = user.FirstName + " " + user.LastName;
            }
            else
            {
                userFullName = "Internal System";
            }
            

            var entityTypeId = GetEntityTypeId(_dbContext, entityTypeName);

            if(entityTypeId > 0)
            {
                var eventLog = new EventLog
                {
                    User = userFullName,
                    Date = DateTime.Now,
                    ChangeType = changeType,
                    EntityName = entityName,
                    EntityBlob = entityBlob,
                    ChangeBlob = changeBlob,
                    EntityId = entityId,
                    EntityTypeId = entityTypeId
                };

                _dbContext.Add(eventLog);
                _dbContext.SaveChanges();
            }
        }
        
        public static void CreateEventLogNonAsync(AppDbContext _dbContext, int userId, string changeType, string entityName, string entityBlob, string changeBlob, int entityId, string entityTypeName)
        {
            var userFullName = "";
            if (userId > 0)
            {
                var user = _dbContext.Users.FirstOrDefault(i => i.Id == userId);
                if (user != null)
                    userFullName = user.FirstName + " " + user.LastName;
            }
            else
            {
                userFullName = "Internal System";
            }
            

            var entityTypeId = GetEntityTypeIdNonAsync(_dbContext, entityTypeName);

            if(entityTypeId > 0)
            {
                var eventLog = new EventLog
                {
                    User = userFullName,
                    Date = DateTime.Now,
                    ChangeType = changeType,
                    EntityName = entityName,
                    EntityBlob = entityBlob,
                    ChangeBlob = changeBlob,
                    EntityId = entityId,
                    EntityTypeId = entityTypeId
                };

                _dbContext.Add(eventLog);
                _dbContext.SaveChanges();
            }
        }
        
        public static void CreateEventLogSystemTask(AppDbContext _dbContext, string userId, string changeType, string entityName, string entityBlob, string changeBlob, int entityId, string entityTypeName)
        {
            var entityTypeId = GetEntityTypeId(_dbContext, entityTypeName);

            if(entityTypeId > 0)
            {
                var eventLog = new EventLog
                {
                    User = userId,
                    Date = DateTime.Now,
                    ChangeType = changeType,
                    EntityName = entityName,
                    EntityBlob = entityBlob,
                    ChangeBlob = changeBlob,
                    EntityId = entityId,
                    EntityTypeId = entityTypeId
                };

                _dbContext.Add(eventLog);
                _dbContext.SaveChanges();
            }
        }
        
        private static int GetEntityTypeId(AppDbContext _dbContext, string name)
        {
            if (_dbContext == null || string.IsNullOrEmpty(name))
            {
                return 0;
            }
            else
            {
                var type = _dbContext.EntityTypes.FirstOrDefaultAsync(i => i.Name == name.Trim());
                if (type == null)
                    return 0;
                else
                    return type.Id;
            }
        }
        
        private static int GetEntityTypeIdNonAsync(AppDbContext _dbContext, string name)
        {
            if (_dbContext == null || string.IsNullOrEmpty(name))
            {
                return 0;
            }
            else
            {
                var type = _dbContext.EntityTypes.FirstOrDefault(i => i.Name == name.Trim());
                if (type == null)
                    return 0;
                else
                    return type.Id;
            }
        }
        
        public async static Task<string> GetAuth0APITokenAsync(Auth0APIManagement auth0APIManagementSettings)
        {
            if (auth0APIManagementSettings != null)
            {
                using (var client = new HttpClient())
                {
                    var auth0Object = new
                    {
                        grant_type = "client_credentials",
                        client_id = auth0APIManagementSettings.ClientID,
                        client_secret = auth0APIManagementSettings.ClientSecret,
                        audience = auth0APIManagementSettings.Audience
                    };
                    var json = JsonConvert.SerializeObject(auth0Object);
                    var content = new StringContent(json, Encoding.UTF8, "application/json");
                    var uri = "https://" + auth0APIManagementSettings.Domain + "/oauth/token";
                    var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });
                    result.EnsureSuccessStatusCode();

                    var token = JObject.Parse(result.Content.ReadAsString())["access_token"].ToString();

                    return token;
                }
            }

            return null;
        }

        public static string GetAuth0APIToken(Auth0APIManagement auth0APIManagementSettings)
        {
            if (auth0APIManagementSettings != null)
            {
                using var client = new HttpClient();
                var auth0Object = new
                {
                    grant_type = "client_credentials",
                    client_id = auth0APIManagementSettings.ClientID,
                    client_secret = auth0APIManagementSettings.ClientSecret,
                    audience = auth0APIManagementSettings.Audience
                };

                var json = JsonConvert.SerializeObject(auth0Object);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var uri = "https://" + auth0APIManagementSettings.Domain + "/oauth/token";
                var restClient = new RestClient(uri);
                var request = new RestRequest();
                request.AddJsonBody(json);

                var response = restClient.Post(request);

                if (response.StatusCode != HttpStatusCode.OK)
                {
                    throw new Exception("Unable to authenticate with Auth0 Management API." + response.Content.ToString());
                }

                var token = JObject.Parse(response.Content)["access_token"].ToString();

                return token;
            }

            return null;
        }

        public static void LogError(AppDbContext _dbContext, Exception ex, string message = null, int statusCode = 0)
        {
            if(ex != null)
            {
                Log.Error(ex, message);
            }
        }

        public async static Task<User> GetLoggedOnUser(AppDbContext _dbContext, ClaimsPrincipal currentUser)
        {
            if(currentUser != null)
            {
                if(currentUser.Identity != null)
                {
                    if (currentUser.Identity.IsAuthenticated)
                    {
                        var user = await _dbContext.Users
                            .AsNoTracking()
                            .FirstOrDefaultAsync(i => i.Auth0Id == currentUser.Identity.Name);

                        return user;
                    }
                }
            }
            return null;

        }

        public  static User GetLoggedOnUserNonAsync(AppDbContext _dbContext, ClaimsPrincipal currentUser)
        {
            if (currentUser != null)
            {
                if (currentUser.Identity != null)
                {
                    if (currentUser.Identity.IsAuthenticated)
                    {
                        var user =  _dbContext.Users
                            .AsNoTracking()
                            .FirstOrDefault(i => i.Auth0Id == currentUser.Identity.Name);

                        return user;
                    }
                }
            }
            return null;
        }

        public async static Task<User> GetUserByAuth0Id(AppDbContext _dbContext, bool isSystemUser = false)
        {
            if (isSystemUser)
            {
                return null;
            }
            if (OnlineUser.Instance == null)
                throw new Exception();

            if (string.IsNullOrEmpty(OnlineUser.Instance.Auth0Id))
                throw new Exception();

            if (string.IsNullOrEmpty(OnlineUser.Instance.Auth0Id))
                return null;

            var user = await _dbContext.Users
                .Where(u => u.Auth0Id == OnlineUser.Instance.Auth0Id)
                .AsNoTracking()
                .FirstOrDefaultAsync();

            if (user == null)
                return null;

            return user;
        }
        
        public static User GetUserByAuth0IdNonAsync(AppDbContext _dbContext, bool isSystemUser = false)
        {
            if (isSystemUser)
            {
                return null;
            }
            if (OnlineUser.Instance == null)
                throw new Exception();

            if (string.IsNullOrEmpty(OnlineUser.Instance.Auth0Id))
                throw new Exception();

            if (string.IsNullOrEmpty(OnlineUser.Instance.Auth0Id))
                return null;

            var user = _dbContext.Users
                .Where(u => u.Auth0Id == OnlineUser.Instance.Auth0Id)
                .AsNoTracking()
                .FirstOrDefault();

            if (user == null)
                return null;

            return user;
        }
        
        public static User GetUserByAuth0Id_V2(AppDbContext _dbContext)
        {
            if (OnlineUser.Instance == null)
                throw new Exception();

            if (string.IsNullOrEmpty(OnlineUser.Instance.Auth0Id))
                throw new Exception();

            if (string.IsNullOrEmpty(OnlineUser.Instance.Auth0Id))
                return null;

            var user = _dbContext.Users
                .Where(u => u.Auth0Id == OnlineUser.Instance.Auth0Id)
                .AsNoTracking()
                .FirstOrDefault();

            if (user == null)
                return null;

            return user;
        }
        public static bool IsInternalSACRRAUser(User user)
        {
            if(user.RoleId == UserRoles.FinancialAdministrator ||
                user.RoleId == UserRoles.SACRRAAdministrator ||
                user.RoleId == UserRoles.StakeHolderAdministrator ||
                user.RoleId == UserRoles.StakeHolderManager)
            {
                return true;
            }

            return false;
        }

        public async static Task<MemberContact> GetMemberMainContact(AppDbContext _dbContext, int memberId)
        {
            if(memberId > 0)
            {
                var mainContactType = await _dbContext.ContactTypes
                    .FirstOrDefaultAsync(x => x.Name == "Main Contact Details");

                if(mainContactType != null)
                {
                    var mainContact = await _dbContext.MemberContacts
                        .FirstOrDefaultAsync(x => x.ContactTypeId == mainContactType.Id && x.MemberId == memberId);

                    return mainContact;
                }
            }
            return null;
        }
        public async static Task<MemberContact> GetMemberDataContact(AppDbContext _dbContext, int memberId)
        {
            if (memberId > 0)
            {
                var mainContactType = await _dbContext.ContactTypes
                    .FirstOrDefaultAsync(x => x.Name == "Data Contact Details");

                if (mainContactType != null)
                {
                    var mainContact = await _dbContext.MemberContacts
                        .FirstOrDefaultAsync(x => x.ContactTypeId == mainContactType.Id && x.MemberId == memberId);

                    return mainContact;
                }
            }
            return null;
        }

        public  static async Task CreateSRNStatusEventLog(AppDbContext _dbContext, IMapper _mapper, string oldStatus, string newStatus, string changeType, SRN srn, User user)
        {
            var srnUpdateResource = _mapper.Map<SRNUpdateResource>(srn);
            var entityBlob = JsonConvert.SerializeObject(srnUpdateResource);

            var stagingChangeLog = new MemberStagingChangeLogResource();

            var stagingChange = new StagingChange
            {
                Name = "SRN Status",
                OldValue = oldStatus,
                NewValue = newStatus
            };

            stagingChangeLog.Changes.Add(stagingChange);

            var changeBlob = JsonConvert.SerializeObject(stagingChangeLog);

            var userId = 0;
            if (user != null)
                userId = user.Id;

            CreateEventLog(_dbContext, userId, changeType, srn.TradingName, entityBlob, changeBlob, srn.Id, "SRN");
        }

        public static bool IsInThePast(string dateInput)
        {
            DateTime result;

            if(DateTime.TryParse(dateInput, out result))
            {
                if (result == DateTime.MinValue)
                    return false;

                if (result.Date <= DateTime.Now.Date)
                    return true;
            }

            return false;
        }

        public static DateTime? ConvertStringToDate(string dateInput)
        {
            DateTime result;

            if (string.IsNullOrEmpty(dateInput))
                return null;

            if (DateTime.TryParse(dateInput, out result))
            {
                return result;
            }

            return null;
        }

        public static RestClient GetRestClient(string baseApiUrl)
        {
            RestClientOptions options = new($"{baseApiUrl}/api");
            // disable SSL errors
            options.RemoteCertificateValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true;
            var restClient = new RestClient(options);
            return restClient;
        }

        public static void CheckForAPIRequestError(RestResponse restRequest)
        {
            if (restRequest.StatusCode != System.Net.HttpStatusCode.OK)
            {
                // If the API throws an exception or we cannot access the API
                if (restRequest.ErrorException != null)
                    throw restRequest.ErrorException;

                // Otherwise return the error we get back from the API
                var message = restRequest.Content;
                var ex = new Exception(message);
                throw ex;
            }
        }
    }
    public static class DefaultValueHelper<T>
    {
        public static void GetDefaultValue(T objEntity)
        {
            var type = objEntity.GetType();
            IList<PropertyInfo> properties = new List<PropertyInfo>(type.GetProperties());
            foreach(var prop in properties)
            {
                var propValue = prop.GetValue(objEntity, null);

                if(propValue == null)
                {
                    if (prop.PropertyType.IsClass && prop.PropertyType != typeof(string) && prop.PropertyType != typeof(List<IdValuePairResource>))
                    {
                        var subClass = CreateInstance(prop);

                        foreach (var subProp in subClass.GetType().GetProperties())
                        {
                            if (subProp.PropertyType.IsPrimitive)
                            {
                                subProp.SetValue(subClass, 0);
                            }
                            else if (subProp.PropertyType == (typeof(string)))
                            {
                                subProp.SetValue(subClass, "N/A");
                            }
                        }

                        prop.SetValue(objEntity, subClass);
                    }
                    else if (prop.PropertyType.IsPrimitive || prop.PropertyType.IsEnum)
                    {
                        prop.SetValue(objEntity, 0);
                    }
                    else if (prop.PropertyType == (typeof(string)))
                    {
                        prop.SetValue(objEntity, "N/A");
                    }
                    else if(prop.PropertyType == typeof(int?))
                    {
                        prop.SetValue(objEntity, 0);
                    }
                    else if (prop.PropertyType == typeof(bool?))
                    {
                        prop.SetValue(objEntity, false);
                    }
                    else if (prop.PropertyType == typeof(List<IdValuePairResource>))
                    {
                        prop.SetValue(objEntity, new List<IdValuePairResource>());
                    }
                    else if (prop.PropertyType == typeof(DateTime?))
                    {
                        prop.SetValue(objEntity, DateTime.MinValue);
                    }
                    else if (prop.PropertyType == typeof(IdValuePairResource))
                    {
                        prop.SetValue(objEntity, new IdValuePairResource { Id = 0, Value = "N/A" });
                    }
                }
            }
        }

        private static object CreateInstance(PropertyInfo entity)
        {
            return Activator.CreateInstance(entity.PropertyType);
        }
    }
    
}

