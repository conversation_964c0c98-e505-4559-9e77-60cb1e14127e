using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sacrra.Membership.Freshdesk.DTOs;
using Sacrra.Membership.Freshdesk.DTOs.Ticket;
using Sacrra.Membership.Freshdesk.Services;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Sacrra.Membership.Api.ControllersV2
{
    [ApiController]
    [ApiVersion("2")]
    [Route("api/v{v:apiVersion}/[controller]/[action]")]
    public class FreshdeskController : Controller
    {
        private readonly CompanyService _companyService;
        private readonly TicketsService _ticketsService;
        public FreshdeskController(CompanyService companyService, TicketsService ticketsService)
        {
            _companyService = companyService;
            _ticketsService = ticketsService;
        }

        [Authorize(Roles = "SACRRA Administrator,Stakeholder Manager,Bureau")]
        [HttpPost]
        [ProducesResponseType(type: typeof(TicketSummaryDTO), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult GetTickets([FromBody] TicketFilterInputDTO filter, int page = 1)
        {
            var tickets = _ticketsService.GetTicketsSummary(filter, page);
            return Ok(tickets);
        }

        [Authorize(Roles = "SACRRA Administrator,Stakeholder Manager,Bureau")]
        [HttpGet("{ticketId}")]
        [ProducesResponseType(type: typeof(TicketRequesterDetailDTO), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult GetRequester(string ticketId)
        {
            var ticket = _ticketsService.GetRequester(ticketId);
            return Ok(ticket);
        }

        [Authorize(Roles = "SACRRA Administrator,Stakeholder Manager,Bureau")]
        [HttpGet("{ticketId}")]
        [ProducesResponseType(type: typeof(ConversationsSummaryDTO), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult GetAllTicketConversations(string ticketId, int page = 1)
        {
            var ticket = _ticketsService.GetAllTicketConversations(ticketId, page);
            return Ok(ticket);
        }

        [Authorize(Roles = "SACRRA Administrator,Stakeholder Manager,Bureau")]
        [HttpGet]
        [ProducesResponseType(type: typeof(IEnumerable<IdValuePairDTO>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult GetAgents()
        {
            var agents = _ticketsService.GetAgents();
            return Ok(agents);
        }

        [Authorize(Roles = "SACRRA Administrator,Stakeholder Manager,Bureau")]
        [HttpGet]
        [ProducesResponseType(type: typeof(IEnumerable<IdValuePairDTO>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult GetTicketTypes()
        {
            long id = 72000086391; //TODO: This is the field Id for ticket type. Hardcoded value for now
            var choices = _ticketsService.GetTicketField(id);
            return Ok(choices);
        }

        [Authorize(Roles = "SACRRA Administrator,Stakeholder Manager,Bureau")]
        [HttpGet]
        [ProducesResponseType(type: typeof(TicketFilterGetAllDTO), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult GetAllTicketFilters()
        {
            long id = 72000086391; //TODO: This is the field Id for ticket type. Hardcoded value for now
            var choices = _ticketsService.GetAllTicketFilters(id);
            return Ok(choices);
        }

        [HttpPut]
        [ProducesResponseType(type: typeof(SearchTicketItemDTO), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult AutoPopulateImpactedMemberField([FromBody] AutoPopulateImpactedMemberInputDTO inputDTO)
        {
            if (!HttpContext.Request.Headers.TryGetValue("APIkey", out
                var extractedApiKey))
            {
                return BadRequest("Invalid request. API key is required.");
            }

            if (string.IsNullOrEmpty(extractedApiKey))
            {
                return BadRequest("Invalid request. API key is required.");
            }

            if (!_ticketsService.IsValidWebhookApiKey(extractedApiKey))
            {
                return BadRequest("Invalid API key");
            }

            var data = _ticketsService.AutoPopulateImpactedMemberField(extractedApiKey, inputDTO);
            
            return Ok(data);
        }

        [HttpPut]
        [ProducesResponseType(type: typeof(void), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult BulkPopulateImpactedMemberField([FromBody] TicketFilterInputDTO filter, int page = 1)
        {
            _ticketsService.BulkPopulateImpactedMemberField(filter, page);

            return Ok();
        }
    }
}

