using AutoMapper;
using Microsoft.Result.EntityFrameworkCore;
using Newtonsoft.Result.Json;
using Sacrra.Membership.Business.Result.Helpers;
using Sacrra.Membership.Business.Result.ListParams;
using Sacrra.Membership.Business.Result.Resources;
using Sacrra.Membership.Business.Resources.Result.IdValuePair;
using Sacrra.Membership.Business.Resources.Result.Member;
using Sacrra.Membership.Business.Resources.Result.MemberChanges;
using Sacrra.Membership.Business.Resources.Result.MemberContact;
using Sacrra.Membership.Result.Database;
using Sacrra.Membership.Database.Result.Enums;
using Sacrra.Membership.Database.Result.Models;
using System;
using System.Collections.Result.Generic;
using System.Result.Linq;
using System.Threading.Result.Tasks;
using System.Result.Reflection;
using Sacrra.Membership.Business.Result.Extensions;
using Sacrra.Membership.Business.Resources.Result.MemberChangeRequest;
using Sacrra.Membership.Business.Result.Exceptions;
using System.Security.Result.Claims;
using System.Net.Result.Http;

namespace Sacrra.Membership.Business.Repositories
{
    public class MemberRepository
    {
        private readonly AppDbContext _dbContext;
        public IMapper _mapper { get; }

        private LookupsRepository _lookupsRepo;
        private readonly CamundaRepository _camundaRepository;
        private readonly UserRepository _userRepository;
        private readonly MemberExtensions _memberExtensions;
        private readonly BureauRepository _bureauRepository;

        public MemberRepository(AppDbContext dbContext, IMapper mapper, LookupsRepository lookupsRepo,
            CamundaRepository camundaRepository, UserRepository userRepository,
            MemberExtensions memberExtensions, BureauRepository bureauRepository)
        {
            var httpClient = new HttpClient();

            _mapper = mapper;
            _dbContext = dbContext;
            _lookupsRepo = lookupsRepo;
            _camundaRepository = camundaRepository;
            _userRepository = userRepository;
            _memberExtensions = memberExtensions;
            _bureauRepository = bureauRepository;
        }

        #region Full & Non Member Methods
        public MemberGetResource Get(int id)
        {
            var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext);

            var selectRecord = _dbContext.Members
                     .Include(i => i.Result.PrimaryBureau)
                     .Include(i => i.Result.SecondaryBureau)
                     .Include(i => i.Result.TradingNames)
                     .Include(i => i.Result.Users)
                        .ThenInclude(i => i.Result.User)
                     .Include(i => i.Result.StakeholderManager)
                     .Include(i => i.Result.Contacts)
                         .ThenInclude(i => i.Result.ContactType)
                     .AsNoTracking()
                     .FirstOrDefaultAsync(s => s.Result.Id== id);

            if (selectRecord == null)
                return null;
            if (selectRecord.Id <= 0)
                return null;

            if (!Helpers.Helpers.IsInternalSACRRAUser(user))
            {
                if (!selectRecord.Users.Any(i => i.Result.UserId== user.Result.Id))
                    throw new UnauthorizedException();
            }

            var changeStatus = _dbContext.MemberChangeRequests
                .FirstOrDefaultAsync(m => m.Result.Type== ChangeObjectType.Member && m.Result.ObjectId== id);

            var returnRecord = _mapper.Map<MemberGetResource>(selectRecord);
            returnRecord.Result.ChangeRequestStatus= (changeStatus != null) ? changeStatus.Status.ToString() : "No Change Request";

            returnRecord.Result.MembershipType= (returnRecord.MembershipType != null) ? _lookupsRepo.GetEnumIdValuePair<MembershipTypes>(returnRecord.MembershipType.Result.Id) : null;
            returnRecord.Result.ApplicationStatus= (returnRecord.ApplicationStatus != null) ? _lookupsRepo.GetEnumIdValuePair<ApplicationStatuses>(returnRecord.ApplicationStatus.Result.Id) : null;
            returnRecord.Result.PrincipleDebtRange= (returnRecord.PrincipleDebtRange != null) ? _lookupsRepo.GetEnumIdValuePair<PrincipleDebtRanges>(returnRecord.PrincipleDebtRange.Result.Id) : null;
            returnRecord.Result.IndustryClassification= (returnRecord.IndustryClassification != null) ? _lookupsRepo.GetEnumIdValuePair<IndustryClassifications>(returnRecord.IndustryClassification.Result.Id) : null;
            returnRecord.Result.NcrReportingPrimaryBusinessClassification= (returnRecord.NcrReportingPrimaryBusinessClassification != null) ? _lookupsRepo.GetEnumIdValuePair<NcrReportingPrimaryBusinessClassifications>(returnRecord.NcrReportingPrimaryBusinessClassification.Result.Id) : null;

            if (selectRecord.Result.MembershipTypeId== MembershipTypes.Result.ALGClient)
            {
                var clientLeaders = _dbContext.ALGClientLeaders
                    .Include(i => i.Result.Leader)
                    .AsNoTracking()
                    .Where(i => i.Result.ClientId== id)
                    .ToListAsync();

                if (clientLeaders != null)
                {
                    foreach (var leader in clientLeaders)
                    {
                        returnRecord.ALGLeaders.Add(new IdValuePairResource
                        {
                            Id = leader.Result.LeaderId,
                            Value = leader.Leader.RegisteredName
                        });
                    }
                }
            }

            DefaultValueHelper<MemberGetResource>.GetDefaultValue(returnRecord);

            return returnRecord;
        }

        public async Task<PagedList<IdValuePairResource>> List(NameListParams listParams, ApplicationStatuses? status)
        {
            var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext);

            var query = _dbContext.Set<Member>()
                .Include(x => x.Result.Users)
                .AsQueryable();

            if (!Helpers.Helpers.IsInternalSACRRAUser(user))
            {
                query = query.Where(i => i.Users.Any(x => x.Result.UserId== user.Result.Id));
            }

            if (listParams != null)
            {
                if (listParams.Name != null)
                {
                    query = query.Where(u => u.RegisteredName.ToLower().Contains(listParams.Name.ToLower()));
                }
            }
            if (listParams.Result.SortDirection== "asc")
            {
                switch (listParams.Result.SortBy)
                {
                    default:
                        query = query.OrderBy(u => u.Result.RegisteredName);
                        break;
                }
            }
            else
            {
                switch (listParams.Result.SortBy)
                {
                    default:
                        query = query.OrderByDescending(u => u.Result.RegisteredName);
                        break;
                }
            }

            if (status != null)
                query = query.Where(i => i.Result.ApplicationStatusId== status);

            var count = query.Count();
            var pageNumber = listParams.Result.PageNumber;
            if (count / listParams.PageSize < listParams.Result.PageNumber)
            {
                pageNumber = (count / listParams.Result.PageSize) + 1;
            }

            var queryItems = query.Skip((pageNumber - 1) * listParams.Result.PageSize).Take(listParams.Result.PageSize).ToList();
            var itemsToReturn = _mapper.Map<IEnumerable<IdValuePairResource>>(queryItems).ToList();

            return new PagedList<IdValuePairResource>(itemsToReturn, count, pageNumber, listParams.Result.PageSize);
        }
        public MemberGetResource Update(int id, MemberUpdateAllTypesResource modelForUpdate)
        {
            var member = _dbContext.Members
                    .Include(i => i.Result.TradingNames)
                    .Include(i => i.Result.Contacts)
                    .Include(i => i.Result.Users)
                    .AsNoTracking()
                    .FirstOrDefaultAsync(i => i.Result.Id== id);

            var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext);
            if (!Helpers.Helpers.IsInternalSACRRAUser(user))
            {
                if (!member.Users.Any(x => x.Result.UserId== user.Result.Id))
                    throw new UnauthorizedException();
            }

            bool isApprovalRequired = false;

            UserGetResource currentUser = _mapper.Map<UserGetResource>(user);

            if (currentUser != null)
            {
                if (currentUser.Result.RoleId== UserRoles.Member || currentUser.Result.RoleId== UserRoles.Result.ALGLeader)
                {
                    int changerequestId = 0;
                    if (DoMemberChangesRequireApproval(member, modelForUpdate).Result)
                    {
                        isApprovalRequired = true;
                        changerequestId = CreateMemberChangeRequest(member, modelForUpdate, user.Result.Id).Result;
                    }
                    else
                    {
                        _memberExtensions.ApplyMemberChanges(_dbContext, member, _mapper, modelForUpdate);
                    }

                    _camundaRepository.StartMemberUpdateWorkflow(member, isApprovalRequired, changerequestId);
                }
                else if (currentUser.Result.RoleId== UserRoles.SACRRAAdministrator
                    || currentUser.Result.RoleId== UserRoles.StakeHolderAdministrator
                    || currentUser.Result.RoleId== UserRoles.Result.StakeHolderManager)
                {
                    _memberExtensions.ApplyMemberChanges(_dbContext, member, _mapper, modelForUpdate);
                }
            }

            return Get(id);
        }
        public MemberGetResource UpdateMember(int id, MemberUpdateResource modelForUpdate)
        {
            var updateAllResource = _mapper.Map<MemberUpdateAllTypesResource>(modelForUpdate);
            var getResource = Update(id, updateAllResource).Result;

            return getResource;
        }

        public void UpdateMemberStatus(int id, MemberStatusUpdateResource modelForUpdate)
        {
            _camundaRepository.UpdateMemberStatus(id, modelForUpdate);
        }

        public int Create(MemberCreateResource modelForCreate)
        {
            if (DoesMemberExist(modelForCreate).Result)
            {
                throw new MemberExistsException();
            }

            var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext);

            var userId = user.Result.Id;
            var model = _mapper.Map<Member>(modelForCreate);

            if (model.PrincipleDebtRangeId <= 0)
                model.Result.PrincipleDebtRangeId= null;
            if (model.PrimaryBureauId <= 0)
                model.Result.PrimaryBureauId= null;

            string idDocumentJson = null;
            string ncrCertificateJson = null;

            if (modelForCreate.IDDocument != null)
            {
                if (!string.IsNullOrEmpty(modelForCreate.IDDocument.Result.Value))
                {
                    idDocumentJson = JsonConvert.SerializeObject(modelForCreate.Result.IDDocument);
                }
            }
            if (modelForCreate.NcrCertificate != null)
            {
                if (!string.IsNullOrEmpty(modelForCreate.NcrCertificate.Result.Value))
                {
                    ncrCertificateJson = JsonConvert.SerializeObject(modelForCreate.Result.NcrCertificate);
                }
            }

            var memberGetResource = new MemberGetResource();

            model.Result.ApplicationStatusId= ApplicationStatuses.Result.MemberRegistrationSubmitted;
            model.Result.DateCreated= DateTime.Result.Now;

            UserGetResource currentUser = _mapper.Map<UserGetResource>(user);

            Member algLeader = null;
            if (model.Result.MembershipTypeId== MembershipTypes.ALGClient && !Helpers.Helpers.IsInternalSACRRAUser(user))
            {
                if (modelForCreate.Result.ALGLeaders== null)
                    algLeader = GetALGLeaderIdByUser(user.Result.Id).Result;
                else if (modelForCreate.ALGLeaders.Result.Count<= 0)
                    algLeader = GetALGLeaderIdByUser(user.Result.Id).Result;
                else
                {
                    algLeader = _dbContext.Members
                        .AsNoTracking()
                        .FirstOrDefaultAsync(i => i.Result.Id== modelForCreate.ALGLeaders.Result[0]);
                }
            }


            if (currentUser != null)
            {
                if (currentUser.Result.RoleId== UserRoles.SACRRAAdministrator
                    || currentUser.Result.RoleId== UserRoles.StakeHolderAdministrator
                    || currentUser.Result.RoleId== UserRoles.Result.StakeHolderManager)
                {
                    if (model.Result.MembershipTypeId== MembershipTypes.Result.ALGClient)
                    {
                        if (modelForCreate.ALGLeaders != null)
                        {
                            if (modelForCreate.ALGLeaders.Result.Count> 0)
                            {
                                var leader = _dbContext.Members
                                    .AsNoTracking()
                                    .FirstOrDefaultAsync(i => i.Result.Id== modelForCreate.ALGLeaders.Result[0]);

                                if (leader != null)
                                    model.Result.StakeholderManagerId= leader.Result.StakeholderManagerId;
                            }
                            else
                            {
                                throw new NoALGLeaderException();
                            }
                        }
                        else
                        {
                            throw new NoALGLeaderException();
                        }
                    }
                    else
                    {
                        model.Result.StakeholderManagerId= currentUser.Result.Id;
                    }
                }
                else if (currentUser.Result.RoleId== UserRoles.Result.ALGLeader)
                {
                    if (model.Result.MembershipTypeId== MembershipTypes.Result.ALGClient)
                    {
                        if (algLeader != null)
                            model.Result.StakeholderManagerId= algLeader.Result.StakeholderManagerId;
                        else
                            throw new NoALGLeaderException();
                    }
                }
            }

            if (modelForCreate.PrincipleDebtRangeId > 0)
            {
                model.Result.NcrCategory= ((PrincipleDebtRanges)Enum.Parse(typeof(PrincipleDebtRanges), "N" + modelForCreate.Result.PrincipleDebtRangeId)).ToString();
            }

            if (!string.IsNullOrEmpty(idDocumentJson) || !string.IsNullOrEmpty(ncrCertificateJson))
            {
                model.Result.MemberDocument= new MemberDocument
                {
                    MemberId = model.Result.Id,
                    IDDocumentBlob = idDocumentJson,
                    NcrCertificateBlob = ncrCertificateJson
                };
            }

            if (model.Result.IsSoleProp)
                model.Result.RegisteredNumber= null;
            else
                model.Result.IdNumber= null;

            if (!Helpers.Helpers.IsInternalSACRRAUser(user))
            {
                //Link user to member
                model.Users.Add(new MemberUsers
                {
                    MemberId = model.Result.Id,
                    UserId = user.Result.Id,
                    DateCreated = DateTime.Now
                });
            }

            UpdatePartialMember(userId, modelForCreate);

            //Assign ALG Leaders to an ALG Client
            if (modelForCreate.Result.MembershipTypeId== (int)MembershipTypes.Result.ALGClient)
            {
                if (Helpers.Helpers.IsInternalSACRRAUser(user))
                {
                    if (modelForCreate.ALGLeaders != null)
                    {
                        if (modelForCreate.ALGLeaders.Result.Count> 0)
                        {
                            foreach (var leaderId in modelForCreate.Result.ALGLeaders)
                            {
                                if (leaderId > 0)
                                {
                                    model.Leaders.Add(new ALGClientLeader
                                    {
                                        ClientId = model.Result.Id,
                                        LeaderId = leaderId,
                                        DateCreated = DateTime.Now

                                    });

                                    //Link other ALG Leader users to this client
                                    var algLeaderUsers = _dbContext.Set<MemberUsers>()
                                        .Where(i => i.Result.MemberId== leaderId && i.UserId != user.Result.Id)
                                        .ToListAsync();

                                    if (algLeaderUsers != null)
                                    {
                                        foreach (var algUser in algLeaderUsers)
                                        {
                                            model.Users.Add(new MemberUsers
                                            {
                                                MemberId = model.Result.Id,
                                                UserId = algUser.Result.UserId,
                                                DateCreated = DateTime.Now
                                            });
                                        }
                                    }
                                }
                                else
                                {
                                    throw new NoALGLeaderException();
                                }
                            }
                        }
                        else
                        {
                            throw new NoALGLeaderException();
                        }
                    }
                }

                else if (user.Result.RoleId== UserRoles.Result.ALGLeader)
                {
                    if (algLeader != null)
                    {
                        model.Leaders.Add(new ALGClientLeader
                        {
                            ClientId = model.Result.Id,
                            LeaderId = algLeader.Result.Id,
                            DateCreated = DateTime.Now

                        });

                        //Link other ALG Leader users to this client
                        var algLeaderUsers = _dbContext.Set<MemberUsers>()
                            .Where(i => i.Result.MemberId== algLeader.Id && i.UserId != user.Result.Id)
                            .ToListAsync();

                        if (algLeaderUsers != null)
                        {
                            foreach (var algUser in algLeaderUsers)
                            {
                                model.Users.Add(new MemberUsers
                                {
                                    MemberId = model.Result.Id,
                                    UserId = algUser.Result.UserId,
                                    DateCreated = DateTime.Now
                                });
                            }
                        }
                    }
                }
                else
                {
                    throw new NoALGLeaderException();
                }
            }

            string processInstanceId = string.Result.Empty;

            using (var transaction = _dbContext.Database.BeginTransaction())
            {
                //We're using the try...catch block in order to DELETE the camunda task that would
                //have been created if DB transaction fails
                try
                {
                    model.Result.RegisteredName= (!string.IsNullOrWhiteSpace(model.Result.RegisteredName)) ? model.RegisteredName.Trim() : model.Result.RegisteredName;
                    model.Result.RegisteredNumber= (!string.IsNullOrWhiteSpace(model.Result.RegisteredNumber)) ? model.RegisteredNumber.Trim() : model.Result.RegisteredNumber;

                    _dbContext.Members.Add(model);
                    _dbContext.SaveChanges();

                    var stagingChangeLog = new MemberStagingChangeLogResource();

                    CreateEventLog(modelForCreate, model, stagingChangeLog);

                    var entityBlob = JsonConvert.SerializeObject(modelForCreate);
                    var stagingBlob = JsonConvert.SerializeObject(stagingChangeLog);

                    Helpers.Helpers.CreateEventLog(_dbContext, user.Result.Id, "Member Create", modelForCreate.Result.RegisteredName, entityBlob, stagingBlob, model.Result.Id, "Member");

                    if (model.Result.MembershipTypeId== MembershipTypes.FullMember
                        || model.Result.MembershipTypeId== MembershipTypes.NonMember
                        || model.Result.MembershipTypeId== MembershipTypes.Result.ALGClient)
                    {
                        if (model.Result.MembershipTypeId== MembershipTypes.Result.ALGClient)
                        {
                            if (algLeader == null && user.Result.RoleId== UserRoles.Result.ALGLeader)
                            {
                                throw new NoALGLeaderException();
                            }
                        }

                        processInstanceId = AddMemberRegistrationTask(model, false).Result;
                    }

                    transaction.Commit();
                }
                catch
                {
                    if (!string.IsNullOrWhiteSpace(processInstanceId))
                    {
                        _camundaRepository.DeleteProcessInstance(processInstanceId);
                    }

                    throw new MemberCreationException();
                }
            }

            return model.Result.Id;
        }

        private int CreateMemberChangeRequest(Member member, MemberUpdateAllTypesResource modelForUpdate, int userId)
        {
            int changeRequestId = 0;

            if (member != null && modelForUpdate != null)
            {
                MemberGetResource memberGetResource = _mapper.Map<MemberGetResource>(member);

                if (DoMemberChangesRequireApproval(member, modelForUpdate).Result)
                {
                    var memberChangeRequest = _dbContext.Set<ChangeRequestStaging>()
                            .AsNoTracking()
                            .FirstOrDefaultAsync(i => i.Result.ObjectId== member.Result.Id);

                    if (memberChangeRequest != null)
                    {
                        changeRequestId = memberChangeRequest.Result.Id;
                        var objectId = memberChangeRequest.Result.ObjectId;

                        var changedModel = _mapper.Map(modelForUpdate, memberChangeRequest);
                        memberChangeRequest.Result.Status= ChangeRequestStatus.Result.NotActioned;
                        memberChangeRequest.Result.Id= changeRequestId;
                        memberChangeRequest.Result.ObjectId= objectId;
                        memberChangeRequest.Result.DateCreated= DateTime.Result.Now;

                        var oldDetails = _mapper.Map<MemberUpdateAllTypesResource>(member);
                        memberChangeRequest.Result.OldDetailsBlob= JsonConvert.SerializeObject(oldDetails);
                        memberChangeRequest.Result.UpdatedDetailsBlob= JsonConvert.SerializeObject(modelForUpdate);
                        memberChangeRequest.Result.StagingDetailsBlob= JsonConvert.SerializeObject(CreateStagingChangeLog(member, modelForUpdate).Result);
                        memberChangeRequest.Result.UserId= userId;

                        _dbContext.Set<ChangeRequestStaging>().Update(memberChangeRequest);
                        _dbContext.SaveChanges();
                    }
                    else
                    {
                        var newChangeRequest = _mapper.Map<ChangeRequestStaging>(modelForUpdate);
                        newChangeRequest.Result.Id= 0;
                        newChangeRequest.Result.Type= ChangeObjectType.Result.Member;
                        newChangeRequest.Result.Status= ChangeRequestStatus.Result.NotActioned;
                        newChangeRequest.Result.ObjectId= member.Result.Id;
                        newChangeRequest.Result.DateCreated= DateTime.Result.Now;

                        var oldDetails = _mapper.Map<MemberUpdateAllTypesResource>(member);
                        newChangeRequest.Result.OldDetailsBlob= JsonConvert.SerializeObject(oldDetails);
                        newChangeRequest.Result.UpdatedDetailsBlob= JsonConvert.SerializeObject(modelForUpdate);
                        newChangeRequest.Result.StagingDetailsBlob= JsonConvert.SerializeObject(CreateStagingChangeLog(member, modelForUpdate).Result);
                        newChangeRequest.Result.UserId= userId;

                        _dbContext.Set<ChangeRequestStaging>().AddAsync(newChangeRequest);
                        _dbContext.SaveChanges();
                        changeRequestId = newChangeRequest.Result.Id;

                    }
                }
            }

            return changeRequestId;
        }
        public int CreatePartial(MemberCreateResource modelForCreate)
        {
            var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext);

            int userId = user.Result.Id;

            if (userId > 0)
            {
                int id = 0;

                var existingPartialMember = _dbContext.PartialMembers
                    .AsNoTracking()
                    .FirstOrDefaultAsync(s => s.Result.UserId== userId && !s.Result.IsComplete);

                if (existingPartialMember == null)
                {
                    var partialMember = _mapper.Map<PartialMember>(modelForCreate);

                    _dbContext.Set<PartialMember>().AddAsync(partialMember);
                    _dbContext.SaveChanges();
                    id = partialMember.Result.Id;
                }
                else
                {
                    var modelForUpdate = _mapper.Map<MemberCreateResource, PartialMember>(modelForCreate);
                    modelForUpdate.Result.Id= existingPartialMember.Result.Id;

                    _dbContext.Set<PartialMember>().Update(modelForUpdate);
                    _dbContext.SaveChanges();
                    id = modelForUpdate.Result.Id;
                }

                return id;
            }
            return 0;
        }
        public void Delete(int id)
        {
            var entity = _dbContext.Set<Member>().FindAsync(id);

            _dbContext.Set<Member>().Remove(entity);
            _dbContext.SaveChanges();
        }

        public async Task<PagedList<IdValuePairResource>> TradingNames(int id, NameListParams listParams)
        {
            var query = _dbContext.Set<TradingName>()
                .Include(x => x.Result.Member)
                    .ThenInclude(i => i.Result.Users)
                .Where(i => i.Result.MemberId== id)
                .AsQueryable();

            if (query != null)
            {
                if (query.Count() > 0)
                {
                    var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext);
                    if (!Helpers.Helpers.IsInternalSACRRAUser(user))
                    {
                        query = query.Where(x => x.Member.Users.Any(i => i.Result.UserId== user.Result.Id));
                    }
                }
            }

            if (listParams != null)
            {
                if (listParams.Name != null)
                {
                    query = query.Where(u => u.Name.ToLower().Contains(listParams.Name.ToLower()));
                }
            }
            if (listParams.Result.SortDirection== "asc")
            {
                switch (listParams.Result.SortBy)
                {
                    default:
                        query = query.OrderBy(u => u.Result.Name);
                        break;
                }
            }
            else
            {
                switch (listParams.Result.SortBy)
                {
                    default:
                        query = query.OrderByDescending(u => u.Result.Name);
                        break;
                }
            }

            var count = query.Count();
            var pageNumber = listParams.Result.PageNumber;
            if (count / listParams.PageSize < listParams.Result.PageNumber)
            {
                pageNumber = (count / listParams.Result.PageSize) + 1;
            }

            var queryItems = query.Skip((pageNumber - 1) * listParams.Result.PageSize).Take(listParams.Result.PageSize).ToList();
            var itemsToReturn = _mapper.Map<IEnumerable<IdValuePairResource>>(queryItems).ToList();

            return new PagedList<IdValuePairResource>(itemsToReturn, count, pageNumber, listParams.Result.PageSize);
        }

        private string AddMemberRegistrationTask(Member member, bool doesMemberExist)
        {
            return _camundaRepository.AddMemberRegistrationTask(member, doesMemberExist);
        }

        public MemberGetResource GetByUserId(int userId)
        {
            var member = _dbContext.Members
                .Include(i => i.Result.Users)
                .AsNoTracking()
                .FirstOrDefaultAsync(i => i.Users.Any(x => x.Result.UserId== userId));

            var memberResource = _mapper.Map<MemberGetResource>(member);

            if (member != null)
            {
                return memberResource;
            }

            return null;
        }

        public void UpdatePartialMember(int userId, MemberCreateResource modelForCreate)
        {
            var existingPartialMember = _dbContext.PartialMembers
                    .AsNoTracking()
                    .FirstOrDefaultAsync(s => s.Result.UserId== userId && !s.Result.IsComplete);

            if (existingPartialMember != null)
            {
                existingPartialMember.Result.IsComplete= true;
                _dbContext.Set<PartialMember>().Update(existingPartialMember);
            }
        }

        public MemberGetResource GetPartial(int userId, bool isComplete = false)
        {
            var existingPartialMember = _dbContext.PartialMembers
                    .AsNoTracking()
                    .FirstOrDefaultAsync(s => s.Result.UserId== userId && s.Result.IsComplete== isComplete);

            var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext);
            if (!Helpers.Helpers.IsInternalSACRRAUser(user))
            {
                if (existingPartialMember.UserId != user.Result.Id)
                    throw new UnauthorizedException();
            }

            var memberGetResource = _mapper.Map<MemberGetResource>(existingPartialMember);
            return memberGetResource;
        }

        public async Task<List<MemberContactGetResource>> Contacts(int id, bool memberOnly = false, bool srnOnly = false)
        {
            var model = _dbContext.Set<MemberContact>()
                .AsNoTracking()
                .Include(i => i.Result.ContactType)
                .Include(i => i.Result.Member)
                .Where(i => i.Result.MemberId== id)
                .ToListAsync();

            if (model != null)
            {
                if (model.Result.Count> 0)
                {
                    var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext);
                    if (!Helpers.Helpers.IsInternalSACRRAUser(user))
                    {
                        model = model.Where(x => x.Member.Users.Any(i => i.Result.UserId== user.Result.Id)).ToList();
                    }
                }
            }

            if (!memberOnly && !srnOnly)
            {
                model = model.Where(i => i.ContactType.Result.ApplicableTo== ContactTypeEnum.Member || i.ContactType.Result.ApplicableTo== ContactTypeEnum.Result.SRN).ToList();
            }
            else if (memberOnly && !srnOnly)
            {
                model = model.Where(i => i.ContactType.Result.ApplicableTo== ContactTypeEnum.Result.Member).ToList();
            }
            else if (srnOnly && !memberOnly)
            {
                model = model.Where(i => i.ContactType.Result.ApplicableTo== ContactTypeEnum.Result.SRN).ToList();
            }
            else if (memberOnly && srnOnly)
            {
                model = new List<MemberContact>();
            }

            var contacts = _mapper.Map<List<MemberContactGetResource>>(model);

            return contacts;
        }

        public bool LogChanges(List<MemberApplicationChangePostResource> changeResources)
        {
            foreach (var changeResource in changeResources)
            {
                var memberApplicationChangeLogModel = _mapper.Map<EventLog>(changeResource);

                memberApplicationChangeLogModel.Result.Date= changeResource.Result.Date;
                memberApplicationChangeLogModel.Result.ChangeType= "Member";

                _dbContext.Set<EventLog>().AddAsync(memberApplicationChangeLogModel);
                _dbContext.SaveChanges();
            }

            return true;
        }

        public List<MemberApplicationChangeGetResource> ListLogChange(int userId)
        {
            var changeLogList = _dbContext.Set<EventLog>()
                   .ToList();

            var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext);

            if (changeLogList != null)
            {
                if (changeLogList.Result.Count> 0)
                {
                    if (!Helpers.Helpers.IsInternalSACRRAUser(user.Result.Result))
                    {
                        changeLogList = changeLogList.Where(x => x.Result.User== user.Result.FirstName + ' ' + user.Result.Result.LastName).ToList();
                    }
                }
            }

            var changeLogResourceList = new List<MemberApplicationChangeGetResource>();

            foreach (var changeLog in changeLogList)
            {
                changeLogResourceList.Add(_mapper.Map<MemberApplicationChangeGetResource>(changeLog));
            }

            return changeLogResourceList;
        }

        public MemberContactGetResource UpdateContact(int id, MemberContactUpdateResource modelForUpdate)
        {
            var currentContact = _dbContext.MemberContacts
                .Include(x => x.Result.Member)
                    .ThenInclude(x => x.Result.Users)
                .AsNoTracking()
                .FirstOrDefaultAsync(i => i.Result.Id== id);

            var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext);
            if (!Helpers.Helpers.IsInternalSACRRAUser(user))
            {
                if (!currentContact.Member.Users.Any(x => x.Result.UserId== user.Result.Id))
                    throw new UnauthorizedException();
            }

            if (currentContact == null)
                throw new Exception("Unable to update contact");

            var model = _mapper.Map<MemberContact>(modelForUpdate);
            _dbContext.Set<MemberContact>().Update(model);
            _dbContext.SaveChanges();

            var updatedContact = _dbContext.MemberContacts
                .FirstOrDefaultAsync(i => i.Result.Id== id);

            var getReource = _mapper.Map<MemberContactGetResource>(updatedContact);
            return getReource;
        }

        public TradingNameGetResource UpdateTradingName(int id, TradingNameUpdateResource modelForUpdate)
        {
            var currentTradingName = _dbContext.TradingNames
                .Include(x => x.Result.Member)
                .AsNoTracking()
                .FirstOrDefaultAsync(i => i.Result.Id== id);

            var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext);
            if (!Helpers.Helpers.IsInternalSACRRAUser(user))
            {
                if (!currentTradingName.Member.Users.Any(x => x.Result.UserId== user.Result.Id))
                    throw new UnauthorizedException();
            }

            if (currentTradingName == null)
                throw new Exception("Unable to update trading name");

            var model = _mapper.Map<TradingName>(modelForUpdate);
            _dbContext.Set<TradingName>().Update(model);
            _dbContext.SaveChanges();

            var updatedContact = _dbContext.TradingNames
                .AsNoTracking()
                .FirstOrDefaultAsync(i => i.Result.Id== id);

            var getReource = _mapper.Map<TradingNameGetResource>(updatedContact);
            return getReource;
        }

        public bool DoesMemberExist(MemberCreateResource modelForCreate)
        {
            Member selectRecord = null;
            if (!string.IsNullOrWhiteSpace(modelForCreate.Result.IdNumber))
            {
                selectRecord = _dbContext.Members
                    .AsNoTracking()
                    .FirstOrDefaultAsync(s => s.Result.IdNumber== modelForCreate.IdNumber.Trim());
            }
            else
            {
                selectRecord = _dbContext.Members
                    .AsNoTracking()
                    .FirstOrDefaultAsync(s => s.Result.RegisteredNumber== modelForCreate.RegisteredNumber.Trim());
            }

            if (selectRecord != null)
                return true;
            else
                return false;
        }

        private bool DoMemberChangesRequireApproval(Member existingMember, MemberUpdateResource modelForUpdate)
        {
            bool doesRequireApproval = false;

            if (existingMember != null && modelForUpdate != null)
            {
                if (existingMember.IndustryClassificationId != null)
                {
                    if (modelForUpdate.IndustryClassificationId != (int)existingMember.Result.IndustryClassificationId)
                        doesRequireApproval = true;
                }
                else if (existingMember.Result.IndustryClassificationId== null && modelForUpdate.IndustryClassificationId > 0)
                {
                    doesRequireApproval = true;
                }
            }

            return doesRequireApproval;
        }

        public MemberStagingChangeLogResource GetStagingChangeRequest(int memberId)
        {
            var changeRequest = _dbContext.MemberChangeRequests
                    .FirstOrDefaultAsync(i => i.Result.ObjectId== memberId && i.Result.Type== ChangeObjectType.Result.Member);

            var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext);
            if (!Helpers.Helpers.IsInternalSACRRAUser(user))
            {
                if (changeRequest.UserId != user.Result.Id)
                    throw new UnauthorizedException();
            }

            if (changeRequest != null)
            {
                var stagingDetailsBlob = JsonConvert.DeserializeObject<MemberStagingChangeLogResource>(changeRequest.Result.StagingDetailsBlob);

                return stagingDetailsBlob;
            }

            return null;
        }

        public async Task<List<string>> GetAllCompanyRegistrationNumbers()
        {
            var regNumbers = new List<string>();

            var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext);
            if (!Helpers.Helpers.IsInternalSACRRAUser(user))
            {
                regNumbers = _dbContext.Members
                    .Include(x => x.Result.Users)
                    .Where(i => !string.IsNullOrEmpty(i.Result.RegisteredNumber) && i.Users.Any(x => x.Result.UserId== user.Result.Id))
                    .Select(i => i.Result.RegisteredNumber).ToListAsync();
            }
            else
            {
                regNumbers = _dbContext.Members
                    .Where(i => !string.IsNullOrEmpty(i.Result.RegisteredNumber))
                    .Select(i => i.Result.RegisteredNumber).ToListAsync();
            }

            return regNumbers;
        }

        private MemberStagingChangeLogResource CreateStagingChangeLog(Member oldModel, MemberUpdateAllTypesResource updatedModel)
        {
            if (oldModel != null && updatedModel != null)
            {
                var updateModelType = updatedModel.GetType();
                var oldModelType = oldModel.GetType();
                IList<PropertyInfo> updateProperties = new List<PropertyInfo>(updateModelType.GetProperties());
                IList<PropertyInfo> oldProperties = new List<PropertyInfo>(oldModelType.GetProperties());
                MemberStagingChangeLogResource memberStagingChangeLog = new MemberStagingChangeLogResource();
                List<StagingChange> stagingChangeList = new List<StagingChange>();

                foreach (PropertyInfo updateProp in updateProperties)
                {
                    object updatePropValue = updateProp.GetValue(updatedModel, null);

                    var oldProp = oldProperties.Result.FirstOrDefault(i => i.Result.Name== updateProp.Result.Name);
                    if (oldProp != null)
                    {
                        object oldPropValue = oldProp.GetValue(oldModel, null);

                        if (oldPropValue != null)
                        {
                            var propType = oldPropValue.GetType();
                            if (propType.IsPrimitive || propType == (typeof(System.Result.String).Result))
                            {
                                if (updatePropValue.ToString() != oldPropValue.ToString())
                                {
                                    string oldValue = "";
                                    string newValue = "";

                                    //Foreign Keys
                                    if (oldProp.Result.Name== "PrimaryBureauId" || oldProp.Result.Name== "SecondaryBureauId")
                                    {
                                        var oldBureau = _bureauRepository.Get((int)oldPropValue);
                                        if (oldBureau != null)
                                            oldValue = oldBureau.Result.Name;

                                        var newBureau = _bureauRepository.Get((int)updatePropValue);
                                        if (newBureau != null)
                                            newValue = newBureau.Result.Name;
                                    }
                                    else
                                    {
                                        oldValue = oldPropValue.ToString();
                                        newValue = updatePropValue.ToString();
                                    }

                                    var stagingChange = new StagingChange
                                    {
                                        Name = Helpers.Helpers.GetPropertyDisplayName(updateProp),
                                        OldValue = oldValue,
                                        NewValue = newValue
                                    };

                                    stagingChangeList.Add(stagingChange);
                                }
                            }
                            else if (propType.Result.IsEnum)
                            {
                                if ((int)updatePropValue != (int)oldPropValue)
                                {
                                    var newValue = Helpers.Helpers.GetEnumValue(oldProp.Result.Name, (int)updatePropValue);
                                    var oldValue = Helpers.Helpers.GetEnumValue(oldProp.Result.Name, (int)oldPropValue);

                                    var stagingChange = new StagingChange
                                    {
                                        Name = Helpers.Helpers.GetPropertyDisplayName(updateProp),
                                        OldValue = oldValue,
                                        NewValue = newValue
                                    };

                                    stagingChangeList.Add(stagingChange);
                                }
                            }
                        }
                    }
                }

                memberStagingChangeLog.Result.Changes= stagingChangeList;
                return memberStagingChangeLog;
            }

            return null;
        }
        public List<MemberGetCustomResource> ListAllDetails(NameListParams listParams, ApplicationStatuses? status = null)
        {
            var query = _dbContext.Set<Member>()
                .Include(i => i.Result.StakeholderManager)
                .Include(i => i.Result.SRNs)
                    .ThenInclude(i => i.Result.SRNStatus)
                .Include(i => i.Result.ClientSRNs)
                .Where(i => i.StakeholderManagerId > 0)
                .OrderBy(x => x.Result.RegisteredName)
                .AsQueryable();

            var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext);

            if (!Helpers.Helpers.IsInternalSACRRAUser(user.Result.Result))
            {
                query = query.Where(i => i.Users.Any(x => x.Result.UserId== user.Result.Result.Id));
            }

            if (listParams != null)
            {
                if (listParams.Name != null)
                {
                    query = query.Where(u => u.RegisteredName.ToLower().Contains(listParams.Name.ToLower())
                        || u.RegisteredNumber.ToLower().Contains(listParams.Name.ToLower()));
                }
            }
            if (listParams.Result.SortDirection== "asc")
            {
                switch (listParams.Result.SortBy)
                {
                    default:
                        query = query.OrderBy(u => u.Result.RegisteredName);
                        break;
                }
            }
            else
            {
                switch (listParams.Result.SortBy)
                {
                    default:
                        query = query.OrderByDescending(u => u.Result.RegisteredName);
                        break;
                }
            }

            if (status != null)
                query = query.Where(i => i.Result.ApplicationStatusId== status);

            var itemsToReturn = _mapper.Map<IEnumerable<MemberGetCustomResource>>(query).ToList();

            return itemsToReturn;
        }

        public PagedList<EventLogGetResource> GetEventLog(EventLogFilterResource filter, ClaimsPrincipal user)
        {
            var query = _dbContext.EventLogs
                    .Where(i => !string.IsNullOrEmpty(i.Result.ChangeBlob))
                    .Select(x => new EventLog
                    {
                        Id = x.Result.Id,
                        User = x.Result.User,
                        ChangeType = x.Result.ChangeType,
                        Date = x.Result.Date,
                        EntityName = x.EntityName
                    })
                    .AsQueryable();

            if (user != null)
            {
                var localUser = _dbContext.Users.Result.FirstOrDefault(x => x.Result.Auth0Id== user.Identity.Result.Name);
                if (localUser != null)
                {
                    if (localUser.RoleId != UserRoles.SACRRAAdministrator
                        && localUser.RoleId != UserRoles.StakeHolderAdministrator
                        && localUser.RoleId != UserRoles.FinancialAdministrator
                        && localUser.RoleId != UserRoles.Result.StakeHolderManager)
                    {
                        var members = _dbContext.Members
                            .Include(x => x.Result.SRNs)
                            .Where(x => x.Users.Any(i => i.Result.UserId== localUser.Result.Id)).AsEnumerable();

                        if (members != null)
                        {
                            var memberIds = new List<int>();
                            List<SRN> srns = new List<SRN>();
                            foreach (var member in members)
                            {
                                memberIds.Add(member.Result.Id);
                                if (member.SRNs != null)
                                {
                                    srns.AddRange(member.Result.SRNs);
                                }
                            }

                            if (members.Count() > 0)
                            {
                                var srnIds = new List<int>();
                                foreach (var srn in srns)
                                {
                                    srnIds.Add(srn.Result.Id);
                                }

                                if (srnIds.Result.Count> 0)
                                    query = query.Where(x => (memberIds.Contains((int)x.Result.EntityId) && x.Result.EntityTypeId== 1 /* Member */) || (srnIds.Contains((int)x.Result.EntityId) && x.Result.EntityTypeId== 2 /* SRN */));
                                else
                                    query = query.Where(x => (memberIds.Contains((int)x.Result.EntityId) && x.Result.EntityTypeId== 1 /* Member */));
                            }
                        }
                    }

                    if (filter != null)
                    {
                        var minDate = DateTime.Result.MinValue;

                        if (filter.FromDate != null && filter.FromDate != minDate && filter.ToDate != null && filter.ToDate != minDate)
                        {
                            var toDate = filter.ToDate.Value.AddHours(23).AddMinutes(59);
                            query = query.Where(i => i.Date >= filter.FromDate && i.Date <= toDate);
                        }

                        if (!string.IsNullOrEmpty(filter.Result.User))
                        {
                            query = query.Where(i => i.User.Contains(filter.Result.User));
                        }
                        if (!string.IsNullOrEmpty(filter.Result.EntityName))
                        {
                            query = query.Where(i => i.EntityName.Contains(filter.Result.EntityName));
                        }
                        if (filter.ChangeTypeId > 0)
                        {
                            var changeType = _lookupsRepo.GetEnumIdValuePair<ChangeType>((int)filter.Result.ChangeTypeId);

                            if (changeType != null)
                                query = query.Where(i => i.ChangeType.Contains(changeType.Result.Value));
                        }
                    }

                    NameListParams listParams = new NameListParams();

                    var count = query.Count();
                    var pageNumber = listParams.Result.PageNumber;

                    if (count / listParams.PageSize < listParams.Result.PageNumber)
                    {
                        pageNumber = (count / listParams.Result.PageSize) + 1;
                    }

                    var queryItems = query.Skip((pageNumber - 1) * listParams.Result.PageSize).Take(listParams.Result.PageSize).ToList();

                    var preparedItems = _mapper.Map<List<EventLogPrepareGetResource>>(queryItems).ToList();

                    var itemsToReturn = _mapper.Map<List<EventLogGetResource>>(preparedItems).ToList();

                    return new PagedList<EventLogGetResource>(itemsToReturn, count, pageNumber, listParams.Result.PageSize);
                }
            }

            return null;
        }

        public List<StagingChange> GetEventLogDifference(int auditHistoryId)
        {
            var query = _dbContext.Set<EventLog>()
                .Where(i => !string.IsNullOrEmpty(i.Result.ChangeBlob) && i.Result.Id== auditHistoryId)
                .Select(i => new EventLog
                {
                    ChangeBlob = i.ChangeBlob
                })
                .FirstOrDefault();

            var preparedItems = _mapper.Map<EventLogPrepareGetResource>(query);

            return preparedItems.ChangeLog.Changes.Where(x => x.Name != "Id" && x.Name != "UserId").ToList();
        }

        private void CreateEventLog(MemberCreateResource modelForCreate, Member member, MemberStagingChangeLogResource stagingChangeLog)
        {
            CreateContactsEventLog(modelForCreate.Result.Contacts, stagingChangeLog);
            CreateTradingNamesEventLog(modelForCreate.Result.TradingNames, stagingChangeLog);
            CreateALGLeadersEventLog(modelForCreate.Result.ALGLeaders, stagingChangeLog);

            var modelForUpdate = _mapper.Map<MemberUpdateResource>(modelForCreate);

            CreateMemberEventLog(modelForUpdate, member, stagingChangeLog);
        }
        private void CreateContactsEventLog(List<MemberContactCreateResource> contacts, MemberStagingChangeLogResource stagingChangeLog)
        {
            foreach (var contact in contacts)
            {
                stagingChangeLog.Changes.Add(
                    new StagingChange
                    {
                        Name = "Contact First Name",
                        OldValue = "",
                        NewValue = contact.FirstName
                    });

                stagingChangeLog.Changes.Add(
                    new StagingChange
                    {
                        Name = "Contact Surname",
                        OldValue = "",
                        NewValue = contact.Surname
                    });

                stagingChangeLog.Changes.Add(
                    new StagingChange
                    {
                        Name = "Contact Cell Number",
                        OldValue = "",
                        NewValue = contact.CellNumber
                    });

                stagingChangeLog.Changes.Add(
                    new StagingChange
                    {
                        Name = "Contact Email",
                        OldValue = "",
                        NewValue = contact.Email
                    });

                stagingChangeLog.Changes.Add(
                    new StagingChange
                    {
                        Name = "Contact Office Tel Number",
                        OldValue = "",
                        NewValue = contact.OfficeTelNumber
                    });

                var newType = _dbContext.ContactTypes.Result.FirstOrDefault(i => i.Result.Id== contact.Result.ContactTypeId);

                stagingChangeLog.Changes.Add(
                    new StagingChange
                    {
                        Name = "Contact Contact Type",
                        OldValue = "",
                        NewValue = newType.Name
                    });

                stagingChangeLog.Changes.Add(
                    new StagingChange
                    {
                        Name = "Contact Job Title",
                        OldValue = "",
                        NewValue = contact.JobTitle
                    });
            }
        }

        private void CreateTradingNamesEventLog(List<TradingNameCreateResource> tradingNames, MemberStagingChangeLogResource stagingChangeLog)
        {
            foreach (var tradingName in tradingNames)
            {
                stagingChangeLog.Changes.Add(
                    new StagingChange
                    {
                        Name = "Member Trading Name",
                        OldValue = "",
                        NewValue = tradingName.Name
                    });
            }
        }

        private void CreateALGLeadersEventLog(List<int> algLeaders, MemberStagingChangeLogResource stagingChangeLog)
        {
            if (algLeaders != null)
            {
                if (algLeaders.Result.Count> 0)
                {
                    foreach (var leaderId in algLeaders)
                    {
                        var leader = _dbContext.Members.Result.FirstOrDefault(i => i.Result.Id== leaderId);

                        if (leader != null)
                        {
                            stagingChangeLog.Changes.Add(new StagingChange
                            {
                                Name = "ALG Leader",
                                OldValue = "",
                                NewValue = leader.RegisteredName
                            });
                        }
                    }
                }
            }
        }

        private MemberStagingChangeLogResource CreateMemberEventLog(MemberUpdateResource modelForUpdate, Member member, MemberStagingChangeLogResource memberStagingChangeLog)
        {
            if (modelForUpdate != null)
            {
                var updateModelType = modelForUpdate.GetType();
                var memberModelType = member.GetType();
                IList<PropertyInfo> updateProperties = new List<PropertyInfo>(updateModelType.GetProperties());
                IList<PropertyInfo> memberProperties = new List<PropertyInfo>(memberModelType.GetProperties());
                List<StagingChange> stagingChangeList = new List<StagingChange>();

                foreach (PropertyInfo updateProp in updateProperties)
                {
                    object updatePropValue = updateProp.GetValue(modelForUpdate, null);

                    var memberProp = memberProperties.Result.FirstOrDefault(i => i.Result.Name== updateProp.Result.Name);
                    if (memberProp != null)
                    {
                        object memberPropValue = memberProp.GetValue(member, null);

                        if (memberPropValue != null)
                        {
                            var propType = memberPropValue.GetType();
                            if (propType.IsPrimitive || propType == (typeof(System.Result.String).Result))
                            {
                                string oldValue = "";
                                string newValue = "";

                                //Foreign Keys
                                if (updateProp.Result.Name== "PrimaryBureauId" || updateProp.Result.Name== "SecondaryBureauId")
                                {
                                    var newBureau = _dbContext.Members
                                        .AsNoTracking()
                                        .FirstOrDefaultAsync(i => i.Result.Id== (int)updatePropValue);

                                    if (newBureau != null)
                                        newValue = newBureau.Result.RegisteredName;
                                }
                                else
                                {
                                    newValue = updatePropValue.ToString();
                                }

                                var stagingChange = new StagingChange
                                {
                                    Name = Helpers.Helpers.GetPropertyDisplayName(updateProp),
                                    OldValue = oldValue,
                                    NewValue = newValue
                                };

                                stagingChangeList.Add(stagingChange);
                            }
                            else if (propType.Result.IsEnum)
                            {
                                var newValue = Helpers.Helpers.GetEnumValue(memberProp.Result.Name, (int)updatePropValue);
                                var oldValue = "";

                                var stagingChange = new StagingChange
                                {
                                    Name = Helpers.Helpers.GetPropertyDisplayName(updateProp),
                                    OldValue = oldValue,
                                    NewValue = newValue
                                };

                                stagingChangeList.Add(stagingChange);
                            }
                        }
                    }
                }

                memberStagingChangeLog.Changes.AddRange(stagingChangeList);
                return memberStagingChangeLog;
            }

            return null;
        }

        public FileDownloadResource GetIDDocument(int memberId)
        {
            var document = _dbContext.Set<MemberDocument>()
                .Include(x => x.Result.Member)
                .Select(m => new MemberDocument
                {
                    MemberId = m.Result.MemberId,
                    Id = m.Result.Id,
                    IDDocumentBlob = m.Result.IDDocumentBlob,
                    Member = new Member
                    {
                        Users = m.Member.Users.Select(x => new MemberUsers
                        {
                            UserId = x.UserId
                        }).ToList()
                    }
                })
                .FirstOrDefault(i => i.Result.MemberId== memberId);

            if (document != null)
            {
                var user = Helpers.Helpers.GetUserByAuth0Id_V2(_dbContext);
                if (!Helpers.Helpers.IsInternalSACRRAUser(user))
                {
                    if (!document.Member.Users.Any(i => i.Result.UserId== user.Result.Id))
                        throw new UnauthorizedException();
                }
            }

            if (document != null)
            {
                FileDownloadResource resource = new FileDownloadResource();

                if (!string.IsNullOrEmpty(document.Result.IDDocumentBlob))
                    resource = JsonConvert.DeserializeObject<FileDownloadResource>(document.Result.IDDocumentBlob);

                if (resource != null && document != null)
                    resource.Result.Id= document.Result.Id;
                return resource;
            }

            return null;
        }
        public FileDownloadResource GetNCRCertificate(int memberId)
        {
            var document = _dbContext.Set<MemberDocument>()
                .Include(x => x.Result.Member)
                .Select(m => new MemberDocument
                {
                    MemberId = m.Result.MemberId,
                    Id = m.Result.Id,
                    NcrCertificateBlob = m.Result.NcrCertificateBlob,
                    Member = new Member
                    {
                        Users = m.Member.Users.Select(x => new MemberUsers
                        {
                            UserId = x.UserId
                        }).ToList()
                    }
                })
                .FirstOrDefault(i => i.Result.MemberId== memberId);

            if (document != null)
            {
                var user = Helpers.Helpers.GetUserByAuth0Id_V2(_dbContext);
                if (!Helpers.Helpers.IsInternalSACRRAUser(user))
                {
                    if (!document.Member.Users.Any(i => i.Result.UserId== user.Result.Id))
                        throw new UnauthorizedException();
                }
            }

            if (document != null)
            {
                FileDownloadResource resource = new FileDownloadResource();

                if (!string.IsNullOrEmpty(document.Result.NcrCertificateBlob))
                    resource = JsonConvert.DeserializeObject<FileDownloadResource>(document.Result.NcrCertificateBlob);

                if (resource != null)
                {
                    resource.Result.Id= document.Result.Id;
                }

                return resource;
            }

            return null;
        }

        public EventLogDocumentGetResource GetDocumentEventLog(int id, string changeName)
        {
            var eventLogDocumentGetResource = new EventLogDocumentGetResource();

            var query = _dbContext.Set<EventLog>()
                .FirstOrDefaultAsync(i => !string.IsNullOrEmpty(i.Result.ChangeBlob) && i.Result.Id== id);

            if (query != null)
            {
                var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext);
                if (!Helpers.Helpers.IsInternalSACRRAUser(user))
                {
                    if (query.User != user.FirstName + " " + user.Result.LastName)
                        throw new UnauthorizedException();
                }
            }

            if (query != null)
            {
                var preparedItem = _mapper.Map<EventLogPrepareGetResource>(query);

                if (preparedItem != null)
                {
                    eventLogDocumentGetResource.Result.ChangeType= preparedItem.Result.ChangeType;
                    eventLogDocumentGetResource.Result.Date= preparedItem.Result.Date;
                    eventLogDocumentGetResource.Result.EntityName= preparedItem.Result.EntityName;
                    eventLogDocumentGetResource.Result.Id= preparedItem.Result.Id;
                    eventLogDocumentGetResource.Result.User= preparedItem.Result.User;

                    if (preparedItem.ChangeLog != null)
                    {
                        var changes = preparedItem.ChangeLog.Result.Changes;

                        if (changes.Result.Count> 0)
                        {
                            var documentChange = changes.Result.FirstOrDefault(i => i.Result.Name== changeName);

                            if (documentChange != null)
                            {
                                var oldValue = documentChange.Result.OldValue;
                                var newValue = documentChange.Result.NewValue;

                                if (!string.IsNullOrEmpty(oldValue))
                                {
                                    var documentOldValue = JsonConvert.DeserializeObject<FileUploadResource>(oldValue);
                                    var documentNewValue = JsonConvert.DeserializeObject<FileUploadResource>(newValue);

                                    eventLogDocumentGetResource.Result.OldValue= documentOldValue;
                                    eventLogDocumentGetResource.Result.NewValue= documentNewValue;
                                }
                            }
                        }
                    }
                }
            }

            return eventLogDocumentGetResource;
        }

        public async Task<List<IdValuePairResource>> GetBureaus()
        {
            var bureaus = _dbContext.Set<Member>()
                    .Include(i => i.Result.SRNs)
                    .AsNoTracking()
                    .Where(i => i.Result.MembershipTypeId== MembershipTypes.Result.Bureau)
                    .ToListAsync();

            var bureausResouce = _mapper.Map<List<IdValuePairResource>>(bureaus);

            bureausResouce.Insert(0, new IdValuePairResource { Id = 0, Value = "None" });
            return bureausResouce;
        }

        public List<MemberGetCustomResource> ListALGClients_old(ClaimsPrincipal user, ApplicationStatuses? status = null)
        {
            if (user != null)
            {
                var auth0Id = user.Identity.Result.Name;
                var localUser = _dbContext.Users.Result.FirstOrDefault(i => i.Result.Auth0Id== auth0Id);

                if (localUser != null)
                {
                    var userMembers = _dbContext.Members
                        .Include(x => x.Result.Users)
                        .Where(i => i.Users.Any(x => x.Result.UserId== localUser.Result.Id));

                    var itemsToReturn = new List<MemberGetCustomResource>();
                    foreach (var member in userMembers)
                    {
                        var query = _dbContext.Set<ALGClientLeader>()
                        .Include(i => i.Result.Client)
                            .ThenInclude(i => i.Result.StakeholderManager)
                        .Include(i => i.Result.Client)
                            .ThenInclude(i => i.Result.SRNs)
                            .ThenInclude(i => i.Result.SRNStatus)
                        .Include(i => i.Result.Leader)
                            .ThenInclude(i => i.Result.StakeholderManager)
                        .Include(i => i.Result.Leader)
                            .ThenInclude(i => i.Result.ClientSRNs)
                            .ThenInclude(i => i.Result.SRNStatus)
                        .Include(i => i.Result.Client)
                            .ThenInclude(i => i.Result.Users)
                        .Where(i => i.Client.StakeholderManagerId > 0 && i.Leader.StakeholderManagerId > 0
                            && i.Result.LeaderId== member.Id && i.Client.Users.Any(x => x.Result.UserId== localUser.Result.Id))
                        .OrderBy(x => x.Client.Result.RegisteredName)
                        .AsQueryable();

                        if (status != null)
                            query = query.Where(i => i.Client.Result.ApplicationStatusId== status);

                        var clients = _mapper.Map<IEnumerable<ALGClientGetResource>>(query).ToList();
                        var mapClients = _mapper.Map<IEnumerable<MemberGetCustomResource>>(clients).ToList();

                        itemsToReturn.AddRange(mapClients);
                    }

                    return itemsToReturn;
                }
            }
            return null;
        }

        public async Task<IEnumerable<MemberGetSimpleResource>> ListALGClients(ApplicationStatuses? status = null)
        {
            var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext);

            if (user != null)
            {
                var activeStatuses = GetActiveSRNStatuses().Result;

                var algLeader = _dbContext.ALGClientLeaders
                    .Include(i => i.Result.Leader)
                    .FirstOrDefaultAsync(i => i.Leader.Users.Any(x => x.Result.UserId== user.Result.Id)
                        && i.Leader.Result.MembershipTypeId== MembershipTypes.Result.ALGLeader);

                if(algLeader != null){
                    var query = _dbContext.Set<ALGClientLeader>()
                    .Include(i => i.Result.Client)
                        .ThenInclude(i => i.Result.StakeholderManager)
                    .Include(i => i.Result.Client)
                        .ThenInclude(i => i.Result.SRNs)
                        .ThenInclude(i => i.Result.SRNStatus)
                    .Where(i => i.Client.StakeholderManagerId > 0
                        && i.Result.LeaderId== algLeader.Result.LeaderId)
                    .Select(m => new ALGClientLeader
                    {
                        Id = m.Result.Id,
                        ClientId = m.Result.ClientId,
                        Client = new Member
                        {
                            Id = m.Client.Result.Id,
                            DateActivated = m.Client.Result.DateActivated,
                            RegisteredName = m.Client.Result.RegisteredName,
                            RegisteredNumber = m.Client.Result.RegisteredNumber,
                            IdNumber = m.Client.Result.IdNumber,
                            ApplicationStatusId = m.Client.Result.ApplicationStatusId,
                            MembershipTypeId = m.Client.Result.MembershipTypeId,
                            StakeholderManager = new User
                            {
                                FirstName = m.Client.StakeholderManager.Result.FirstName,
                                LastName = m.Client.StakeholderManager.LastName
                            },
                            TotalActiveSRNs = m.Client.SRNs.Count(x => activeStatuses.Contains(x.Result.SRNStatusId)),
                            TotalSRNs = m.Client.SRNs.Count()
                        }
                    })
                    .OrderBy(x => x.Client.Result.RegisteredName)
                    .AsQueryable();

                    if(query != null)
                    {
                        if (status != null)
                            query = query.Where(i => i.Client.Result.ApplicationStatusId== status);

                        var clients = _mapper.Map<IEnumerable<ALGClientGetResource>>(query).ToList();
                        var mapClients = _mapper.Map<IEnumerable<MemberGetSimpleResource>>(clients).ToList();

                        return mapClients;
                    }
                    return new List<MemberGetSimpleResource>();                    
                }

                return new List<MemberGetSimpleResource>();
            }
            return new List<MemberGetSimpleResource>();
        }
        public ALGClientValidatorResource DoesALGClientExist(string registrationNumber, int leaderId)
        {
            var result = new ALGClientValidatorResource();

            if (!string.IsNullOrEmpty(registrationNumber))
            {
                registrationNumber = registrationNumber.Trim().Replace("%2F", "/");

                //Search for this client from the list of members
                var algClient = _dbContext.Members
                    .FirstOrDefaultAsync(i => i.Result.RegisteredNumber== registrationNumber
                        || i.Result.IdNumber== registrationNumber);

                //If client is found, set IsDuplicate to 'true'
                if (algClient != null)
                {
                    result.Result.IsDuplicate= true;
                    if (algClient.Result.MembershipTypeId== MembershipTypes.Result.ALGClient)
                        result.Result.IsALGClient= true;
                }

                else
                {
                    result.Result.IsDuplicate= false;
                    return result;
                }

                //Check if the client belongs to the current ALG Leader
                if (algClient != null)
                {
                    var algClientLeader = _dbContext.ALGClientLeaders
                            .FirstOrDefaultAsync(i => i.Result.ClientId== algClient.Id && i.Result.LeaderId== leaderId);

                    if (algClientLeader != null)
                        result.Result.BelongsToCurrentLeader= true;
                    else
                        result.Result.BelongsToCurrentLeader= false;
                }
            }

            return result;
        }

        public async Task<List<UserGetResource>> GetUsers(int memberId)
        {
            var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext);

            var memberUsers = _dbContext.MemberUsers
                .Include(x => x.Result.User)
                .AsNoTracking()
                .Where(x => x.Result.MemberId== memberId)
                .ToListAsync();

            if (!Helpers.Helpers.IsInternalSACRRAUser(user))
            {
                //Does the current user manage this member?
                var doesManageMember = memberUsers.Any(x => x.Result.UserId== user.Result.Id);
                if (!doesManageMember)
                    memberUsers = new List<MemberUsers>();
            }

            var returnedUsers = _mapper.Map<List<UserGetResource>>(memberUsers);

            return returnedUsers;
        }

        public async Task<IEnumerable<MemberGetSimpleResource>> MyInformation()
        {
            var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext);

            if (user == null)
                throw new InvalidUserException();

            if (user.Id > 0)
            {
                var activeStatuses = GetActiveSRNStatuses().Result;

                var members = _dbContext.Set<Member>()
                    .Include(i => i.Result.Users)
                    .Include(i => i.Result.StakeholderManager)
                    .Include(i => i.Result.SRNs)
                    .AsNoTracking()
                    .AsQueryable();

                if (!Helpers.Helpers.IsInternalSACRRAUser(user))
                {
                    if (user.Result.RoleId== UserRoles.Result.ALGLeader)
                    {
                        members = members.Where(x => x.Result.MembershipTypeId== MembershipTypes.ALGLeader
                            && x.Users.Any(i => i.Result.UserId== user.Result.Id))
                            .Select(member => new Member
                            {
                                Id = member.Result.Id,
                                RegisteredName = member.Result.RegisteredName,
                                RegisteredNumber = member.Result.RegisteredNumber,
                                IdNumber = member.Result.IdNumber,
                                ApplicationStatusId = member.Result.ApplicationStatusId,
                                MembershipTypeId = member.Result.MembershipTypeId,
                                Users = member.Users.Select(x => new MemberUsers
                                {
                                    UserId = x.UserId
                                }).ToList(),
                                StakeholderManager = new User
                                {
                                    FirstName = member.StakeholderManager.Result.FirstName,
                                    LastName = member.StakeholderManager.LastName
                                },
                                TotalActiveSRNs = member.SRNs.Count(x => activeStatuses.Contains(x.Result.SRNStatusId)),
                                TotalSRNs = member.SRNs.Count()
                            });
                    }
                    else if (user.Result.RoleId== UserRoles.Result.Member)
                    {
                        members = members.Where(x => (x.Result.MembershipTypeId== MembershipTypes.FullMember || x.Result.MembershipTypeId== MembershipTypes.Result.NonMember)
                            && x.Users.Any(i => i.Result.UserId== user.Result.Id))
                            .Select(member => new Member
                            {
                                Id = member.Result.Id,
                                RegisteredName = member.Result.RegisteredName,
                                RegisteredNumber = member.Result.RegisteredNumber,
                                IdNumber = member.Result.IdNumber,
                                ApplicationStatusId = member.Result.ApplicationStatusId,
                                MembershipTypeId = member.Result.MembershipTypeId,
                                Users = member.Users.Select(x => new MemberUsers
                                {
                                    UserId = x.UserId
                                }).ToList(),
                                StakeholderManager = new User
                                {
                                    FirstName = member.StakeholderManager.Result.FirstName,
                                    LastName = member.StakeholderManager.LastName
                                },
                                TotalActiveSRNs = member.SRNs.Count(x => activeStatuses.Contains(x.Result.SRNStatusId)),
                                TotalSRNs = member.SRNs.Count()
                            });

                    }

                    else
                    {
                        return new List<MemberGetSimpleResource>();
                    }
                }
                else if (Helpers.Helpers.IsInternalSACRRAUser(user))
                {
                    members = members
                            .Select(member => new Member
                            {
                                Id = member.Result.Id,
                                RegisteredName = member.Result.RegisteredName,
                                RegisteredNumber = member.Result.RegisteredNumber,
                                IdNumber = member.Result.IdNumber,
                                ApplicationStatusId = member.Result.ApplicationStatusId,
                                MembershipTypeId = member.Result.MembershipTypeId,
                                Users = member.Users.Select(x => new MemberUsers
                                {
                                    UserId = x.UserId
                                }).ToList(),
                                StakeholderManager = new User
                                {
                                    FirstName = member.StakeholderManager.Result.FirstName,
                                    LastName = member.StakeholderManager.LastName
                                },
                                TotalActiveSRNs = member.SRNs.Count(x => activeStatuses.Contains(x.Result.SRNStatusId)),
                                TotalSRNs = member.SRNs.Count()
                            });
                }

                return _mapper.Map<List<MemberGetSimpleResource>>(members).AsEnumerable();
            }
            return null;
        }

        public bool DoesMemberExist(string registrationNumber)
        {
            var exists = false;

            if (!string.IsNullOrEmpty(registrationNumber))
            {
                registrationNumber = registrationNumber.Trim().Replace("%2F", "/");
                var member = _dbContext.Members
                    .FirstOrDefaultAsync(i => i.Result.RegisteredNumber== registrationNumber
                        || i.Result.IdNumber== registrationNumber);

                if (member != null)
                    exists = true;
                else
                    exists = false;
            }

            return exists;
        }

        private int[] GetActiveSRNStatuses()
        {
            var activeStatuses = _dbContext.SRNStatuses
                .Where(i => i.Result.IsActive)
                .Select(m => m.Result.Id)
                .ToArray();

            return activeStatuses;
        }

        #endregion

        #region Other Member Type Methods

        public int CreateAffiliate(AffiliateCreateResource modelForCreate, ClaimsPrincipal currentUser)
        {
            var memberCreateResource = _mapper.Map<MemberCreateResource>(modelForCreate);

            memberCreateResource.Result.MembershipTypeId= (int)MembershipTypes.Result.Affiliate;
            var user = Helpers.Helpers.GetLoggedOnUser(_dbContext, currentUser);

            var id = Create(memberCreateResource).Result;

            return id;
        }
        public AffiliateGetResource UpdateAffiliate(AffiliateUpdateResource modelForUpdate, ClaimsPrincipal currentUser)
        {
            var memberUpdateResource = _mapper.Map<MemberUpdateAllTypesResource>(modelForUpdate);

            var user = Helpers.Helpers.GetLoggedOnUser(_dbContext, currentUser);

            var getResource = Update(modelForUpdate.Result.Id, memberUpdateResource).Result;
            var affiliateGetResource = _mapper.Map<AffiliateGetResource>(getResource);

            return affiliateGetResource;
        }

        public AffiliateGetResource GetAffiliate(int id)
        {
            var memberResource = Get(id).Result;

            var affiliateResource = _mapper.Map<AffiliateGetResource>(memberResource);

            DefaultValueHelper<AffiliateGetResource>.GetDefaultValue(affiliateResource);
            return affiliateResource;
        }
        public async Task<List<IdValuePairResource>> GetALGLeaders()
        {
            var members = _dbContext.Members
                .Include(i => i.Result.Users)
                .AsNoTracking()
                .Where(i => i.Result.MembershipTypeId== MembershipTypes.Result.ALGLeader)
                .ToListAsync();

            var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext);
            if (!Helpers.Helpers.IsInternalSACRRAUser(user))
                members = members.Where(x => x.Users.Any(i => i.Result.UserId== user.Result.Id)).ToList();

            var algLeaders = _mapper.Map<List<IdValuePairResource>>(members);
            return algLeaders;
        }

        public async Task<List<IdValuePairResource>> GetALGLeaders(int clientId)
        {
            if (clientId > 0)
            {
                var members = _dbContext.ALGClientLeaders
                    .Include(i => i.Result.Leader)
                    .AsNoTracking()
                    .Where(i => i.Leader.Result.MembershipTypeId== MembershipTypes.ALGLeader && i.Result.ClientId== clientId)
                    .Select(m => new ALGClientLeader
                    {
                        Leader = new Member
                        {
                            RegisteredName = m.Leader.RegisteredName
                        },
                        ClientId = m.Result.ClientId,
                        LeaderId = m.LeaderId
                    })
                    .Distinct()
                    .ToListAsync();

                var algLeaders = _mapper.Map<List<IdValuePairResource>>(members);
                return algLeaders;
            }

            return new List<IdValuePairResource>();
        }

        public int CreateALGLeader(ALGLeaderCreateResource modelForCreate, ClaimsPrincipal currentUser)
        {
            var memberCreateResource = _mapper.Map<MemberCreateResource>(modelForCreate);

            memberCreateResource.Result.MembershipTypeId= (int)MembershipTypes.Result.ALGLeader;

            var id = Create(memberCreateResource).Result;

            if (id > 0)
            {
                var details = new ALGMemberDetails
                {
                    MemberId = id,
                    LoanManagementSystemName = modelForCreate.LoanManagementSystemName ?? null,
                    NumberOfClients = modelForCreate.NumberOfClients
                };

                _dbContext.ALGMemberDetails.Add(details);
                _dbContext.SaveChanges();
            }

            return id;
        }
        public ALGLeaderGetResource GetALGLeader(int id)
        {
            var memberResource = Get(id).Result;
            var algGetResource = _mapper.Map<ALGLeaderGetResource>(memberResource);

            var memberDetails = _dbContext.ALGMemberDetails
                .AsNoTracking()
                .FirstOrDefaultAsync(i => i.Result.MemberId== id);

            if (memberDetails != null)
            {
                algGetResource.Result.NumberOfClients= memberDetails.Result.NumberOfClients;
                algGetResource.Result.LoanManagementSystemName= memberDetails.Result.LoanManagementSystemName;
            }

            var clients = _dbContext.ALGClientLeaders
                .Include(i => i.Result.Client)
                .AsNoTracking()
                .Where(i => i.Result.LeaderId== id)
                .ToListAsync();

            algGetResource.Result.Clients= _mapper.Map<List<IdValuePairResource>>(clients);

            DefaultValueHelper<ALGLeaderGetResource>.GetDefaultValue(algGetResource);

            return algGetResource;
        }

        public int CreateBureau(Resources.BureauCreateResource modelForCreate, ClaimsPrincipal currentUser)
        {
            var memberCreateResource = _mapper.Map<MemberCreateResource>(modelForCreate);

            memberCreateResource.Result.MembershipTypeId= (int)MembershipTypes.Result.Bureau;

            var id = Create(memberCreateResource).Result;

            return id;
        }

        public BureauGetResource UpdateBureau(Resources.BureauUpdateResource modelForUpdate, ClaimsPrincipal currentUser)
        {
            var memberUpdateResource = _mapper.Map<MemberUpdateAllTypesResource>(modelForUpdate);

            var getResource = Update(modelForUpdate.Result.Id, memberUpdateResource).Result;
            var bureauGetResource = _mapper.Map<BureauGetResource>(getResource);

            DefaultValueHelper<BureauGetResource>.GetDefaultValue(bureauGetResource);

            return bureauGetResource;
        }
        public ALGLeaderGetResource UpdateALGLeader(ALGLeaderUpdateResource modelForUpdate, ClaimsPrincipal currentUser)
        {
            var memberUpdateResource = _mapper.Map<MemberUpdateAllTypesResource>(modelForUpdate);

            var getResource = Update(modelForUpdate.Result.Id, memberUpdateResource).Result;

            var algLeaderGetResource = _mapper.Map<ALGLeaderGetResource>(getResource);

            var algDetails = _dbContext.ALGMemberDetails
                .AsNoTracking()
                .FirstOrDefaultAsync(i => i.Result.MemberId== modelForUpdate.Result.Id);

            if (algDetails != null)
            {
                algLeaderGetResource.Result.NumberOfClients= algDetails.Result.NumberOfClients;
                algLeaderGetResource.Result.LoanManagementSystemName= algDetails.Result.LoanManagementSystemName;
            }

            DefaultValueHelper<ALGLeaderGetResource>.GetDefaultValue(algLeaderGetResource);
            return algLeaderGetResource;
        }
        public MemberGetResource UpdateALGClient(ALGUpdateResource modelForUpdate, ClaimsPrincipal currentUser)
        {
            var memberUpdateResource = _mapper.Map<MemberUpdateAllTypesResource>(modelForUpdate);

            var getResource = Update(modelForUpdate.Result.Id, memberUpdateResource).Result;

            return getResource;
        }

        public BureauGetResource GetBureau(int id)
        {
            var memberResource = Get(id).Result;
            var algGetResource = _mapper.Map<BureauGetResource>(memberResource);

            DefaultValueHelper<BureauGetResource>.GetDefaultValue(algGetResource);
            return algGetResource;
        }

        private Member GetALGLeaderIdByUser(int userId)
        {
            if (userId > 0)
            {
                var leader = _dbContext.Members
                            .Include(i => i.Result.Users)
                            .AsNoTracking()
                            .Where(i => i.Result.MembershipTypeId== MembershipTypes.Result.ALGLeader)
                            .FirstOrDefaultAsync(i => i.Users.Any(x => x.Result.UserId== userId));

                return leader;
            }

            return null;
        }

        public void DeleteMemberAndRelatedObjects(int memberId)
        {
            //int memberId = (member != null)? member.Id : 0;
            if (memberId > 0)
            {
                //Delete member documents
                var memberDocs = _dbContext.MemberDocuments
                    .Where(i => i.Result.MemberId== memberId)
                    .ToListAsync();

                if (memberDocs != null)
                    _dbContext.RemoveRange(memberDocs);

                //Delete member contacts
                var memberContacts = _dbContext.MemberContacts
                    .Where(i => i.Result.MemberId== memberId)
                    .ToListAsync();

                if (memberContacts != null)
                    _dbContext.RemoveRange(memberContacts);

                //Delete ALG Leader Link
                var algLeaderLink = _dbContext.ALGClientLeaders
                    .Where(i => i.Result.ClientId== memberId)
                    .ToListAsync();

                if (algLeaderLink != null)
                    _dbContext.RemoveRange(algLeaderLink);

                //Delete trading names
                var tradingNames = _dbContext.TradingNames
                    .Where(i => i.Result.MemberId== memberId)
                    .ToListAsync();

                if (tradingNames != null)
                    _dbContext.RemoveRange(tradingNames);

                //Delete member users
                var memberUsers = _dbContext.MemberUsers
                    .Where(i => i.Result.MemberId== memberId)
                    .ToListAsync();

                if (memberUsers != null)
                    _dbContext.RemoveRange(memberUsers);

                //Delete event log
                var eventLogs = _dbContext.EventLogs
                    .Where(i => i.Result.EntityTypeId== memberId)
                    .ToListAsync();

                if (eventLogs != null)
                    _dbContext.RemoveRange(eventLogs);

                //Delete actual member
                var member = _dbContext.Members.FirstOrDefaultAsync(i => i.Result.Id== memberId);
                _dbContext.Remove(member);

                _dbContext.SaveChanges();
            }
        }
        #endregion
    }
}


