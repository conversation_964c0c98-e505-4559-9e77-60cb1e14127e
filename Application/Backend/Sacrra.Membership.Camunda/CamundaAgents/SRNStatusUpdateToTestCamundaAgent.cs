using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Camunda.Api.Client;
using Camunda.Api.Client.ExternalTask;
using Microsoft.Extensions.DependencyInjection;
using Sacrra.Membership.Business.Services;

namespace Sacrra.Membership.Camunda.CamundaAgents;

public class SrnStatusUpdateToTestCamundaAgent: BaseCamundaAgent
{
    public SrnStatusUpdateToTestCamundaAgent(CamundaClient camundaClient) : base(camundaClient)
    {
    }

    public override async Task Process(string topicName, ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        switch (topicName)
        {
            case "email-bureaus-on-srn-go-live":
                EmailBureausOnSrnGoLive(task, completeExternalTask, serviceScope);
                break;
            
            case "email-bureaus-after-srn-testing-srn-status-update":
                EmailBureausAfterSrnTestingSrnStatusUpdate(task, completeExternalTask, serviceScope);
                break;
            
            case "update-srn-and-file-statuses":
                UpdateSrnAndFileStatuses(task, completeExternalTask, serviceScope);
                break;
            
            case "update-srn-status-to-test":
                UpdateSrnStatusToTest(task, completeExternalTask, serviceScope);
                break;
        }

        task.Complete(completeExternalTask);
    }

    private void UpdateSrnStatusToTest(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var isLiveFileSubmissionsSuspended = GetGenericVariable(task, "isLiveFileSubmissionsSuspended", serviceScope);

        if (isLiveFileSubmissionsSuspended == "true")
        {
            var srnId = GetGenericVariable(task, "SRNId", serviceScope);
            var srnStatusName = GetGenericVariable(task, "SRNStatusName", serviceScope);
            var updatedByUserIdVariable = GetGenericVariable(task, "UpdatedByUserId", serviceScope);

            if (!string.IsNullOrEmpty(srnId) && !string.IsNullOrEmpty(srnStatusName))
            {
                var updatedByUserId = (!string.IsNullOrEmpty(updatedByUserIdVariable)) ? Convert.ToInt32(updatedByUserIdVariable) : 0;
                var taskInfo = task.Get();
                var processInstanceId = taskInfo.ProcessInstanceId;

                GetSrnStatusUpdateToTestCamundaService(serviceScope).UpdateSRNStatus(Convert.ToInt32(srnId), srnStatusName, updatedByUserId, processInstanceId);
                GetSrnStatusUpdateToTestCamundaService(serviceScope).UpdateRolloutStatus("Test", processInstanceId);
            }
        }
    }

    private void UpdateSrnAndFileStatuses(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var srnId = GetIntegerVariable(task, "SRNId", serviceScope);
        var taskInfo = task.Get();
        var processInstanceId = string.Empty;
        var updatedByUserIdVariable = GetIntegerVariable(task, "UpdatedByUserId", serviceScope);
        
        if (taskInfo != null)
        {
            processInstanceId = taskInfo.ProcessInstanceId;
        }

        GetSrnStatusUpdateToTestCamundaService(serviceScope).UpdateSrnAndFileStatuses(srnId, processInstanceId, updatedByUserIdVariable, task);
        GetSrnStatusUpdateToTestCamundaService(serviceScope).UpdateRolloutStatus("Test", processInstanceId);
    }

    private void EmailBureausAfterSrnTestingSrnStatusUpdate(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var srnId = GetIntegerVariable(task, "SRNId", serviceScope);

        GetSrnStatusUpdateToTestCamundaService(serviceScope).EmailBureausAfterSRNTesting(srnId, "SRN Status Update");
    }

    private void EmailBureausOnSrnGoLive(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var srnId = GetIntegerVariable(task, "SRNId", serviceScope);
        var randomUser = GetRandomUser("SACRRAAdministrator");
        
        GetSrnStatusUpdateToTestCamundaService(serviceScope).EmailBureausOnSRNGoLive(srnId);
        completeExternalTask.Variables = new Dictionary<string, VariableValue>
        {
            ["SACRRAAdminAssignee"] = VariableValue.FromObject(randomUser.Id)
        };
    }
    
    private static SrnStatusUpdateToTestCamundaService GetSrnStatusUpdateToTestCamundaService(IServiceScope serviceScope)
    {
        return serviceScope.ServiceProvider.GetRequiredService<SrnStatusUpdateToTestCamundaService>();
    }
}
