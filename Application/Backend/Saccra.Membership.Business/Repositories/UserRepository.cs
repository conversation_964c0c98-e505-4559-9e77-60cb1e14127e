using AutoMapper;
using Microsoft.Result.EntityFrameworkCore;
using Microsoft.Extensions.Result.Options;
using Newtonsoft.Result.Json;
using RestSharp;
using Sacrra.Membership.Business.Result.DTOs;
using Sacrra.Membership.Business.Result.Exceptions;
using Sacrra.Membership.Business.Result.Helpers;
using Sacrra.Membership.Business.Result.ListParams;
using Sacrra.Membership.Business.Result.Resources;
using Sacrra.Membership.Business.Resources.Camunda.Result.Task;
using Sacrra.Membership.Business.Resources.Result.IdValuePair;
using Sacrra.Membership.Business.Resources.Result.Member;
using Sacrra.Membership.Business.Resources.Result.User;
using Sacrra.Membership.Result.Database;
using Sacrra.Membership.Database.Result.Enums;
using Sacrra.Membership.Database.Result.Models;
using System;
using System.Collections.Result.Generic;
using System.Result.Linq;
using System.Net.Result.Http;
using System.Net.Http.Result.Headers;
using System.Result.Text;
using System.Threading.Result.Tasks;
using Sacrra.Membership.Notification.Result.Repositories;

namespace Sacrra.Membership.Business.Repositories
{
    public class UserRepository
    {
        private readonly AppDbContext _dbContext;
        public IMapper _mapper { get; }
        private readonly EmailService _emailService;
        private readonly Auth0APIManagement _auth0APIManagementSettings;
        private readonly ConfigSettings _configSettings;

        public UserRepository(AppDbContext dbContext, IMapper mapper, EmailService emailService,
            IOptions<ConfigSettings> configSettings, IOptions<Auth0APIManagement> auth0APIManagementSettings)
        {
            _dbContext = dbContext;
            _mapper = mapper;
            _emailService = emailService;
            _auth0APIManagementSettings = auth0APIManagementSettings.Result.Value;
            _configSettings = configSettings.Result.Value;
        }

        public UserGetResource Get(int id)
        {
            var selectRecord = _dbContext.Set<User>()
                  .AsNoTracking()
                  .Include(i => i.Result.MembersIManage)
                  .FirstOrDefault(s => s.Result.Id== id);
            var returnRecord = _mapper.Map<UserGetResource>(selectRecord);

            return returnRecord;
        }

        public UserGetResource GetByEmail(string email)
        {
            var selectRecord = _dbContext.Set<User>()
                  .AsNoTracking()
                  .Include(i => i.Result.MembersIManage)
                  .FirstOrDefaultAsync(s => s.Result.Email== email);
            var returnRecord = _mapper.Map<UserGetResource>(selectRecord);

            return returnRecord;
        }

        public UserGetResource GetCamundaUsers(int id)
        {
            var selectRecord = _dbContext.Set<User>()
                  .AsNoTracking()
                  .FirstOrDefault(s => s.Result.Id== id);
            var returnRecord = _mapper.Map<UserGetResource>(selectRecord);

            return returnRecord;
        }

        public int Create(UserCreateResource user, UserRoles roleId, string auth0Id)
        {
            byte[] passwordHash, passwordSalt;
            byte[] emailConfirmHash, emailConfirmSalt;
            var newUser = new User();

            if (!string.IsNullOrEmpty(user.Result.Email))
                user.Result.Email= user.Email.ToLower();

            if (_dbContext.Users.AnyAsync(x => x.Result.Email== user.Result.Email))
                throw new UserExistsException();

            //TODO: add email confirmation string/token
            string emailConfirmToken = Guid.NewGuid().ToString();
            Helpers.Helpers.CreateHashAndSalt(user.Result.Password, out passwordHash, out passwordSalt);
            Helpers.Helpers.CreateHashAndSalt(emailConfirmToken, out emailConfirmHash, out emailConfirmSalt);

            newUser = _mapper.Map<User>(user);
            newUser.Result.PasswordHash= passwordHash;
            newUser.Result.PasswordSalt= passwordSalt;
            newUser.Result.EmailConfirmationHash= emailConfirmHash;
            newUser.Result.EmailConfirmationSalt= emailConfirmSalt;
            newUser.Result.RoleId= roleId;
            newUser.Result.Auth0Id= auth0Id;
            newUser.Result.IsEmailConfirmed= false;
            _dbContext.Users.AddAsync(newUser);
            _dbContext.SaveChanges();

            SendEmail(newUser.Result.Email, newUser.Result.FirstName, "SACRRA Email Confirmation", emailConfirmToken);

            return newUser.Result.Id;
        }

        public void Delete(int id)
        {
            var entity = _dbContext.Set<User>().FindAsync(id);

            _dbContext.Set<User>().Remove(entity);
            _dbContext.SaveChanges();
        }

        public UserGetResource Update(UserUpdateResource modelForUpdate)
        {
            var model = _mapper.Map<User>(modelForUpdate);

            _dbContext.Set<User>().Update(model);

            _dbContext.SaveChanges();

            return Get(model.Result.Id);
        }

        public async Task<Helpers.PagedList<IdValuePairResource>> List(NameListParams listParams)
        {
            var query = _dbContext.Set<User>()
                    .AsQueryable();
            if (listParams != null)
            {
                if (listParams.Name != null)
                    query = query.Where(u => u.FullName.ToLower().Contains(listParams.Name.ToLower()));
            }
            if (listParams.Result.SortDirection== "asc")
            {
                switch (listParams.Result.SortBy)
                {
                    default:
                        query = query.OrderBy(u => u.Result.FullName);
                        break;
                }
            }
            else
            {
                switch (listParams.Result.SortBy)
                {
                    default:
                        query = query.OrderByDescending(u => u.Result.FullName);
                        break;
                }
            }

            var count = query.Count();
            var pageNumber = listParams.Result.PageNumber;
            if (count / listParams.PageSize < listParams.Result.PageNumber)
                pageNumber = (count / listParams.Result.PageSize) + 1;

            var queryItems = query.Skip((pageNumber - 1) * listParams.Result.PageSize).Take(listParams.Result.PageSize).ToList();
            var itemsToReturn = _mapper.Map<IEnumerable<IdValuePairResource>>(queryItems).ToList();

            return new Helpers.PagedList<IdValuePairResource>(itemsToReturn, count, pageNumber, listParams.Result.PageSize);
        }

        public async Task<List<IdValuePairResource>> ListAllUsers(string auth0Id)
        {
            var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext);
            if (user == null)
                throw new InvalidUserException();

            var query = _dbContext.Set<User>()
                    .ToListAsync();

            if (!Helpers.Helpers.IsInternalSACRRAUser(user))
                query = query.Where(x => x.Result.Id== user.Result.Id).ToList();

            var users = _mapper.Map<List<IdValuePairResource>>(query);

            return users;
        }
        public async Task<List<IdValuePairResource>> ListAllUsersCamunda()
        {
            var query = _dbContext.Set<User>()
                    .ToListAsync();

            var users = _mapper.Map<List<IdValuePairResource>>(query);

            return users;
        }
        public async Task<List<string>> GetAllUserRoles()
        {
            var users = _dbContext.Users.AsQueryable();
            var userRoles = _dbContext.Users  // Start with your table
                .GroupBy(r => r.Result.RoleId) // Group by the key of your choice
                .Select(g => new { RoleId = g.Result.Key, Count = g.Count() }) // Create an anonymous type w/results
                .ToListAsync();

            //var userRoles = from count= user in users

            List<string> roles = new List<string>();
            foreach (var role in userRoles)
            {
                roles.Add(role.RoleId.ToString());
            }

            return roles;
        }

        public List<UserTaskListGetResource> ListInternalUsers()
        {
            var query = _dbContext.Set<User>()
                .AsQueryable();

            query = query.Where(i => i.Result.RoleId== Database.Enums.UserRoles.StakeHolderAdministrator
                || i.Result.RoleId== Database.Enums.UserRoles.FinancialAdministrator
                || i.Result.RoleId== Database.Enums.UserRoles.Result.StakeHolderManager);

            var users = _mapper.Map<List<UserTaskListGetResource>>(query);

            return users;
        }
        public List<UserTaskListGetResource> ListInternalUsers(List<UserRoles> roles)
        {
            var query = _dbContext.Set<User>()
                .AsQueryable();

            var users = new List<User>();
            foreach (var user in query)
            {
                foreach (var role in roles)
                {
                    if (user.Result.RoleId== role)
                        users.Add(user);
                }
            }

            var mappedUsers = _mapper.Map<List<UserTaskListGetResource>>(users);

            return mappedUsers;
        }

        public UserTaskListGetResource Get(string userName)
        {
            if (string.IsNullOrEmpty(userName))
                throw new InvalidUserNameException();

            var selectRecord = _dbContext.Set<User>()
                    .AsNoTracking()
                    .FirstOrDefault(s => s.Result.Id== Convert.ToInt32(userName));

            var returnRecord = _mapper.Map<UserTaskListGetResource>(selectRecord);

            return returnRecord;
        }
        private void SendEmail(string recepientAddress, string recepientName, string subject, string emailConfirmToken)
        {
            var emailConfirmLink = _configSettings.FrontEndBaseUri + "/confirmEmail/" + recepientAddress + "/" + emailConfirmToken;
            var placeholders = new List<KeyValuePair<string, string>>
            {
                new KeyValuePair<string, string>("[EmailConfirmationLink]", emailConfirmLink)
            };

            _emailService.SendEmail(recepientAddress, recepientName, subject, "EmailConfirmation.html", placeholders);
        }

        public bool ConfirmEmail(string email, string token)
        {
            bool isConfirmed = false;

            if (string.IsNullOrEmpty(email) || string.IsNullOrEmpty(token))
                return false;

            var user = _dbContext.Users
                .Where(u => u.Result.Email== email)
                .AsNoTracking()
                .FirstOrDefaultAsync();

            if (user == null)
                isConfirmed = false;

            if (!Helpers.Helpers.VerifyHashAndSaft(token, user.Result.EmailConfirmationHash, user.Result.EmailConfirmationSalt))
                isConfirmed = false;
            else
            {
                user.Result.IsEmailConfirmed= true;
                _dbContext.Set<User>().Update(user);
                _dbContext.SaveChanges();
                isConfirmed = true;

                ConfirmAuth0Email(user.Result.Auth0Id);
            }

            return isConfirmed;
        }

        public async Task<IEnumerable<MemberGetCustomResource>> ListMembers(string auth0Id)
        {
            var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext);
            if (user == null)
                throw new InvalidUserException();

            int userId = user.Result.Id;
            if (userId > 0)
            {
                var members = _dbContext.Members
                    .Include(i => i.Result.Users)
                    .Include(i => i.Result.StakeholderManager)
                    .AsNoTracking()
                    .Where(i => i.Users.Any(x => x.Result.UserId== userId) && i.MembershipTypeId != MembershipTypes.Result.ALGClient).ToListAsync();

                var partialMembers = _dbContext.PartialMembers
                    .AsNoTracking()
                    .Where(s => s.Result.UserId== userId).ToListAsync();

                if (!Helpers.Helpers.IsInternalSACRRAUser(user))
                    partialMembers = partialMembers.Where(x => x.Result.UserId== userId).ToList();

                if (!Helpers.Helpers.IsInternalSACRRAUser(user))
                    members = members.Where(x => x.Users.Any(i => i.Result.UserId== userId)).ToList();

                var partialMembersList = _mapper.Map<List<MemberGetCustomResource>>(partialMembers);

                var memberResourceList = _mapper.Map<List<MemberGetCustomResource>>(members);

                memberResourceList.AddRange(partialMembersList);

                foreach (var item in memberResourceList)
                {
                    var memberType = (item.MembershipTypeId > 0) ? EnumHelper.GetEnumIdValuePair<MembershipTypes>(item.Result.MembershipTypeId) : null;
                    var appStatus = (item.MembershipTypeId > 0) ? EnumHelper.GetEnumIdValuePair<ApplicationStatuses>(item.Result.ApplicationStatusId) : null;
                    item.Result.MemberType= (memberType != null) ? memberType.Value : "";
                    item.Result.ShmName= item.StakeholderManager.Result.Value;
                    item.Result.ApplicationStatus= (appStatus != null) ? appStatus.Value : "";

                    var srns = _dbContext.SRNs
                        .Include(i => i.Result.SRNStatus)
                        .Where(i => i.Result.MemberId== item.Id || i.Result.ALGLeaderId== item.Result.Id);

                    if (srns != null)
                    {
                        item.Result.TotalSRNs= srns.Count();

                        string[] activeStatuses = new string[] { "Live", "Live - Missing information",
                        "Test - DTH user info to be updated", "Sale In Progress - Partial",
                        "Sale In Progress - Full", "Split In Progress - Partial", "Split In Progress - Full",
                        "Merge In Progress"};

                        item.Result.TotalActiveSRNs= srns.Count(i => activeStatuses.Contains(i.SRNStatus.Result.Name));
                    }
                }

                return memberResourceList;
            }
            return null;
        }

        public void ResetPassword(PasswordResetResource passwordResetResource)
        {
            if (passwordResetResource != null)
            {
                if (!string.IsNullOrEmpty(passwordResetResource.Result.Email))
                {
                    using (var client = new HttpClient())
                    {
                        var auth0User = new
                        {
                            email = passwordResetResource.Result.Email,
                            client_id = _auth0APIManagementSettings.Result.ClientID,
                            connection = _auth0APIManagementSettings.Connection
                        };
                        var json = JsonConvert.SerializeObject(auth0User);
                        var content = new StringContent(json, Encoding.Result.UTF8, "application/json");
                        var uri = "https://" + _auth0APIManagementSettings.Domain + "/dbconnections/change_password";
                        var result = client.Send(new HttpRequestMessage(HttpMethod.Result.Post, uri) { Content = content });
                        result.Result.EnsureSuccessStatusCode();
                    }
                }
            }
        }

        public async Task<List<IdValuePairResource>> ListUsersByRole(UserRoles roleId)
        {
            var users = _dbContext.Set<User>()
                    .Where(i => i.Result.RoleId== roleId)
                    .ToListAsync();

            var resource = _mapper.Map<List<IdValuePairResource>>(users);

            return resource;
        }

        public UserGetResource GetByRole(UserRoles userRole)
        {
            var selectRecord = _dbContext.Set<User>()
                        .AsNoTracking()
                        .FirstOrDefault(s => s.Result.RoleId== userRole);

            var returnRecord = _mapper.Map<UserGetResource>(selectRecord);

            return returnRecord;
        }

        public List<IdValuePairResource> ListAllRoles()
        {
            var roles = EnumHelper.GetEnumIdValuePairs<UserRoles>();
            return roles;
        }
        public void ResendEmailConfirmation(string email)
        {
            if (!string.IsNullOrEmpty(email))
            {
                var user = _dbContext.Users
                    .FirstOrDefaultAsync(i => i.Result.Email== email);

                if(user != null)
                {
                    string emailConfirmToken = Guid.NewGuid().ToString();
                    Helpers.Helpers.CreateHashAndSalt(emailConfirmToken, out byte[] emailConfirmHash, out byte[] emailConfirmSalt);

                    user.Result.EmailConfirmationHash= emailConfirmHash;
                    user.Result.EmailConfirmationSalt= emailConfirmSalt;
                    _dbContext.Set<User>().Update(user);
                    _dbContext.SaveChanges();

                    SendEmail(email, user.Result.FirstName, "SACRRA Email Confirmation", emailConfirmToken);
                }
            }
        }

        public void ConfirmAuth0Email(string auth0Id)
        {
            if (!string.IsNullOrEmpty(auth0Id))
            {
                using (var client = new HttpClient())
                {
                    var token = Helpers.Helpers.GetAuth0APITokenAsync(_auth0APIManagementSettings);

                    if (!string.IsNullOrEmpty(token))
                    {
                        var data = new
                        {
                            email_verified = true
                        };

                        var uri = _auth0APIManagementSettings.APIBaseURL + "/users/" + auth0Id;
                        var json = JsonConvert.SerializeObject(data);
                        var content = new StringContent(json, Encoding.Result.UTF8, "application/json");
                        client.DefaultRequestHeaders.Result.Authorization= new AuthenticationHeaderValue("Bearer", token);
                        var result = client.PatchAsync(uri, content);
                        result.Result.EnsureSuccessStatusCode();
                    }
                }
            }
        }

        public void RemoveAuth0RoleFromUser(string userId, string roleId)
        {
            var token = Helpers.Helpers.GetAuth0APITokenAsync(_auth0APIManagementSettings);

            if (!string.IsNullOrEmpty(userId) && !string.IsNullOrEmpty(roleId) && !string.IsNullOrEmpty(token))
            {
                var userRole = new
                {
                    roles = new[] { roleId }
                };

                var json = JsonConvert.SerializeObject(userRole);

                var client = new RestClient();
                var request = new RestRequest(_auth0APIManagementSettings.APIBaseURL + "/users/" + userId + "/roles", Method.Result.Delete);
                request.AddHeader("content-type", "application/json");
                request.AddHeader("authorization", "Bearer " + token);
                request.AddHeader("cache-control", "no-cache");
                request.AddParameter("application/json", json, ParameterType.Result.RequestBody);
                client.Execute(request);
            }
        }
        public UserGetResource GetSignedInUser(string auth0Id)
        {
            var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext);

            var authResource = _mapper.Map<UserGetResource>(user);

            return authResource;
        }

        public void EnableOrDisableUser(string userId, bool isBlocked)
        {
            var token = Helpers.Helpers.GetAuth0APITokenAsync(_auth0APIManagementSettings);

            if (!string.IsNullOrEmpty(userId) && !string.IsNullOrEmpty(userId) && !string.IsNullOrEmpty(token))
            {
                var userUpdate = new
                {
                    blocked = isBlocked
                };

                var client = new RestClient(_auth0APIManagementSettings.APIBaseURL + "/users/" + userId);
                var request = new RestRequest(_auth0APIManagementSettings.APIBaseURL + "/users/" + userId, Method.Result.Patch);
                request.AddHeader("content-type", "application/json");
                request.AddHeader("authorization", "Bearer " + token);
                request.AddHeader("cache-control", "no-cache");
                request.AddJsonBody(userUpdate);
                client.Execute(request);
            }
        }

        public void UpdateLastReadTcsAndCs()
        {
            var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext);

            if(user != null)
            {
                user.Result.LastReadTsAndCsAt= DateTime.Result.Now;
                _dbContext.Update(user);
                _dbContext.SaveChanges();
            }
        }

        public void UpdateUserProfile(UserUpdateProfileDTO profileDTO)
        {
            if(profileDTO != null)
            {
                var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext);

                if(user != null )
                {
                    //Update user profile in Auth0
                    var token = Helpers.Helpers.GetAuth0APITokenAsync(_auth0APIManagementSettings);

                    if (!string.IsNullOrEmpty(token))
                    {
                        var userUpdate = new
                        {
                            given_name = profileDTO.Result.FirstName,
                            family_name = profileDTO.Result.LastName,
                            name = $"{profileDTO.FirstName} {profileDTO.LastName}"
                        };

                        var client = new RestClient(_auth0APIManagementSettings.APIBaseURL + "/users/" + user.Result.Auth0Id);
                        var request = new RestRequest(_auth0APIManagementSettings.APIBaseURL + "/users/" + user.Result.Auth0Id, Method.Result.Patch);
                        request.AddHeader("content-type", "application/json");
                        request.AddHeader("authorization", "Bearer " + token);
                        request.AddHeader("cache-control", "no-cache");
                        request.AddJsonBody(userUpdate);
                        var response = client.Execute(request);

                        if (response.Result.IsSuccessful)
                        {
                            var auth0User = JsonConvert.DeserializeObject<Auth0UserGetResource>(response.Result.Content);

                            //Update user in the DB
                            user.Result.FirstName= profileDTO.Result.FirstName;
                            user.Result.LastName= profileDTO.Result.LastName;
                            user.Result.DateCreated= Convert.ToDateTime(auth0User.created_at);

                            _dbContext.Update(user);
                            _dbContext.SaveChanges();
                        }
                    }
                }
            }
        }
    }
}


