using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Camunda.Api.Client;
using Camunda.Api.Client.ExternalTask;
using Microsoft.Extensions.DependencyInjection;
using Sacrra.Membership.Business.Services;
using Sacrra.Membership.Database.Models;

namespace Sacrra.Membership.Camunda.CamundaAgents;

public class MemberUpdateDetailsCamundaAgent: BaseCamundaAgent
{
    public MemberUpdateDetailsCamundaAgent(CamundaClient camundaClient) : base(camundaClient) { }

    public override async Task Process(string topicName, ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        switch (topicName)
        {
            case "notify-member-of-details-change-decline":
                NotifyMemberOfDetailsChangeDecline(task, completeExternalTask, serviceScope);
                break;
            
            case "assign-member-details-update-to-shm":
                AssignMemberDetailsUpdateToShm(task, completeExternalTask, serviceScope);
                break;
            
            case "notify-shm-of-member-update":
                NotifyShmOfMemberUpdate(task, completeExternalTask, serviceScope);
                break;
            
            case "apply-member-details-update":
                ApplyMemberDetailsUpdate(task, completeExternalTask, serviceScope);
                break;
        }
            
        task.Complete(completeExternalTask);
    }

    private void ApplyMemberDetailsUpdate(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        throw new NotImplementedException();
    }

    private void NotifyShmOfMemberUpdate(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var requestId = GetMemberVariable(task, "ChangeRequestId", serviceScope);
        var changeRequest = GetCamundaRepository(serviceScope).GetMemberChangeRequest(Convert.ToInt32(requestId));

        if (changeRequest != null)
        {
            var memberId = changeRequest.ObjectId;

            GetMemberUpdateDetailsCamundaService(serviceScope).NotifySHMOfMemberUpdate(memberId);
        }
    }

    private void AssignMemberDetailsUpdateToShm(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var requestId = GetMemberVariable(task, "ChangeRequestId", serviceScope);
        var changeRequest = GetCamundaRepository(serviceScope).GetMemberChangeRequest(requestId);

        if (changeRequest != null)
        {
            if (changeRequest.Type == ChangeObjectType.Member)
            {
                var member = GetCamundaRepository(serviceScope).GetMember(changeRequest.ObjectId);

                if (member != null)
                {
                    if (member.StakeholderManager != null)
                    {
                        completeExternalTask.Variables = new Dictionary<string, VariableValue>
                        {
                            ["stakeHolderManagerAssignee"] = VariableValue.FromObject(member.StakeholderManager.Id.ToString())
                        };
                    }
                }
            }
        }
    }

    private void NotifyMemberOfDetailsChangeDecline(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        //var memberId = GetMemberVariable(task, "MemberId");
        
        //getCamundaRepository(serviceScope).NotifyApplicantOfMemberUpdateDecline(memberId);
        throw new NotImplementedException();
    }
    
    private static MemberUpdateDetailsCamundaService GetMemberUpdateDetailsCamundaService(IServiceScope serviceScope)
    {
        return serviceScope.ServiceProvider.GetRequiredService<MemberUpdateDetailsCamundaService>();
    }
}
