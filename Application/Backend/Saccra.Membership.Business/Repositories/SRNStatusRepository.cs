using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.ListParams;
using Sacrra.Membership.Business.Resources.IdValuePair;
using Sacrra.Membership.Business.Resources.SRNStatus;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Models;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Sacrra.Membership.Business.Repositories
{
    public class SRNStatusRepository
    {
        private readonly AppDbContext _dbContext;
        public IMapper _mapper { get; }

        public SRNStatusRepository(AppDbContext dbContext, IMapper mapper)
        {
            this._mapper = mapper;
            this._dbContext = dbContext;
        }

        public SRNStatusGetResource Get(int id)
        {
            var selectRecord = await _dbContext.Set<SRNStatus>()
                    .AsNoTracking()
                .FirstOrDefaultAsync(s => s.Id == id);

            var returnRecord = _mapper.Map<SRNStatusGetResource>(selectRecord);

            return returnRecord;
        }

        public async Task<PagedList<IdValuePairResource>> List(NameListParams listParams, bool? isActivityAllowedWhileInProcess = null)
        {
            var query = _dbContext.Set<SRNStatus>()
                    .AsQueryable();
            if (listParams != null)
            {
                if (listParams.Name != null)
                    query = query.Where(u => u.Name.ToLower().Contains(listParams.Name.ToLower()));
            }
            if (listParams.SortDirection == "asc")
            {
                switch (listParams.SortBy)
                {
                    default:
                        query = query.OrderBy(u => u.Name);
                        break;
                }
            }
            else
            {
                switch (listParams.SortBy)
                {
                    default:
                        query = query.OrderByDescending(u => u.Name);
                        break;
                }
            }

            if(isActivityAllowedWhileInProcess != null)
            {
                if((bool)isActivityAllowedWhileInProcess)
                    query = query.Where(u => u.IsActivityAllowedWhileInProcess);
                else
                    query = query.Where(u => !u.IsActivityAllowedWhileInProcess);
            }

            var count = query.Count();
            var pageNumber = listParams.PageNumber;
            if (count / listParams.PageSize < listParams.PageNumber)
                pageNumber = (count / listParams.PageSize) + 1;

            var queryItems = query.Skip((pageNumber - 1) * listParams.PageSize).Take(listParams.PageSize).ToList();
            var itemsToReturn = _mapper.Map<IEnumerable<IdValuePairResource>>(queryItems).ToList();

            return new PagedList<IdValuePairResource>(itemsToReturn, count, pageNumber, listParams.PageSize);
        }
        public SRNStatusGetResource Update(SRNStatusUpdateResource modelForUpdate)
        {
            var model = _mapper.Map<SRNStatus>(modelForUpdate);

            _dbContext.Set<SRNStatus>().Update(model);

            _dbContext.SaveChanges();

            return Get(model.Id);
        }

        public int Create(SRNStatusCreateResource modelForCreate)
        {
            var model = _mapper.Map<SRNStatus>(modelForCreate);

            await _dbContext.Set<SRNStatus>().AddAsync(model);

            _dbContext.SaveChanges();

            return model.Id;
        }
        public void Delete(int id)
        {
            var entity = await _dbContext.Set<SRNStatus>().FindAsync(id);

            _dbContext.Set<SRNStatus>().Remove(entity);
            _dbContext.SaveChanges();
        }
        public SRNStatusGetResource GetByName(string name)
        {
            var selectRecord = await _dbContext.Set<SRNStatus>()
                    .AsNoTracking()
                .FirstOrDefaultAsync(s => s.Name.ToLower() == name.Trim().ToLower());

            var returnRecord = _mapper.Map<SRNStatusGetResource>(selectRecord);

            return returnRecord;
        }
        public SRNStatusGetResource GetByCode(string code)
        {
            var selectRecord = await _dbContext.Set<SRNStatus>()
                    .AsNoTracking()
                .FirstOrDefaultAsync(s => s.Code.ToLower() == code.Trim().ToLower());

            var returnRecord = _mapper.Map<SRNStatusGetResource>(selectRecord);

            return returnRecord;
        }
    }
}

