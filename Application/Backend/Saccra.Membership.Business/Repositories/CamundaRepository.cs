using AutoMapper;
using Camunda.Api.Client;
using Camunda.Api.Client.ProcessDefinition;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Microsoft.VisualBasic.FileIO;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;
using RestSharp;
using Sacrra.Membership.Business.Extensions;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.Resources;
using Sacrra.Membership.Business.Resources.Camunda.Task;
using Sacrra.Membership.Business.Resources.Member;
using Sacrra.Membership.Business.Resources.MemberChangeRequest;
using Sacrra.Membership.Business.Resources.SRN;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;
using Sacrra.Membership.Notification.Repositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace Sacrra.Membership.Business.Repositories
{
    public class CamundaRepository
    {
        private readonly UserRepository _userRepository;
        private readonly IMapper _mapper;
        private readonly CamundaClient _camundaClient;
        private readonly ConfigSettings _configSettings;
        private readonly AppDbContext _dbContext;
        private readonly SRNRepository _srnRepository;
        private readonly EmailService _emailService;
        private readonly MemberExtensions _memberExtensions;
        private readonly DWExceptionRepository _dWExceptionRepository;
        private readonly string _dwBaseApiUrl;
        private readonly string _dwApiKey;
        private readonly string _dwDataset;
        private readonly ReportingAPISettings _reportingApiSettings;

        public CamundaRepository(UserRepository userRepository, IMapper mapper,
            IOptions<ConfigSettings> configSettings, AppDbContext dbContext,
            SRNRepository srnRepository, EmailService emailService,
            MemberExtensions memberExtensions, DWExceptionRepository dWExceptionRepository,
            IOptions<ReportingAPISettings> reportingApiSettings)
        {
            _userRepository = userRepository;
            _mapper = mapper;
            _configSettings = configSettings.Value;
            HttpClient httpClient = new HttpClient
            {
                BaseAddress = new Uri(_configSettings.CamundaBaseAddress)
            };
            _camundaClient = CamundaClient.Create(httpClient);
            _dbContext = dbContext;
            _srnRepository = srnRepository;
            _emailService = emailService;
            _memberExtensions = memberExtensions;
            _dWExceptionRepository = dWExceptionRepository;
            _reportingApiSettings = reportingApiSettings.Value;
            _dwBaseApiUrl =
                _reportingApiSettings.BaseApiUrl; //configuration.GetSection("ReportingAPISettings")["BaseApiUrl"];
            _dwApiKey = _reportingApiSettings.ApiKey; //configuration.GetSection("ReportingAPISettings")["ApiKey"];
            _dwDataset = _reportingApiSettings.Dataset; //configuration.GetSection("ReportingAPISettings")["Dataset"];
        }

        public string AddMemberRegistrationTask(Member member, bool doesMemberExist)
        {
            string membershipType;
            switch (member.MembershipTypeId)
            {
                case MembershipTypes.FullMember:
                    membershipType = "fullMember";
                    break;
                case MembershipTypes.NonMember:
                    membershipType = "nonMember";
                    break;
                case MembershipTypes.ALGClient:
                    membershipType = "algClient";
                    break;
                default:
                    membershipType = "fullMember";
                    break;
            }

            var financialAdmin = _userRepository.GetByRole(UserRoles.FinancialAdministrator);

            if (member.MembershipTypeId == MembershipTypes.ALGClient)
            {
                var leader = _dbContext.ALGClientLeaders
                    .AsNoTracking()
                    .Include(i => i.Leader)
                    .FirstOrDefault(i => i.ClientId == member.Id);

                var processInstance = _camundaClient.ProcessDefinitions.ByKey("New-Member-Takeon").StartProcessInstance(
                    new StartProcessInstance()
                    {
                        Variables = new Dictionary<string, VariableValue>()
                        {
                            { "OrganisationID", VariableValue.FromObject(member.Id) },
                            { "OrganisationName", VariableValue.FromObject(member.RegisteredName) },
                            { "membershipType", VariableValue.FromObject(membershipType) },
                            {
                                "stakeHolderManagerAssignee",
                                VariableValue.FromObject(leader.Leader.StakeholderManagerId.ToString())
                            },
                            { "FinancialAdministratorAssignee", VariableValue.FromObject(financialAdmin.Id.ToString()) }
                        }
                    }).Result;

                if (processInstance != null)
                {
                    if (!string.IsNullOrEmpty(processInstance.Id))
                        return processInstance.Id;
                }
            }
            else
            {
                if (financialAdmin != null)
                {
                    var processInstance = _camundaClient.ProcessDefinitions.ByKey("New-Member-Takeon")
                        .StartProcessInstance(new StartProcessInstance()
                        {
                            Variables = new Dictionary<string, VariableValue>()
                            {
                                { "OrganisationID", VariableValue.FromObject(member.Id) },
                                { "OrganisationName", VariableValue.FromObject(member.RegisteredName) },
                                {
                                    "FinancialAdministratorAssignee",
                                    VariableValue.FromObject(financialAdmin.Id.ToString())
                                },
                                { "membershipType", VariableValue.FromObject(membershipType) }
                            }
                        }).Result;

                    if (processInstance != null)
                    {
                        if (!string.IsNullOrEmpty(processInstance.Id))
                            return processInstance.Id;
                    }
                }
            }

            return null;
        }

        public void UpdateMemberStatus(int id, MemberStatusUpdateResource modelForUpdate)
        {
            _memberExtensions.UpdateMemberStatus(_camundaClient, _dbContext, id, modelForUpdate);
        }

        private async Task<List<TaskListResource>> GetUserTasks(string assignee, string processDefinitionKey = null)
        {
            try
            {
                using (var client = new HttpClient())
                {
                    string definitionKey = (!string.IsNullOrEmpty(processDefinitionKey))
                        ? "&processDefinitionKey=" + processDefinitionKey
                        : null;

                    var uri = _configSettings.CamundaBaseAddress + "/task?assignee=" + assignee + definitionKey;
                    var result = client.Send(new HttpRequestMessage(HttpMethod.Get, uri));

                    result.EnsureSuccessStatusCode();

                    var resultString = result.Content.ReadAsString();
                    var tasksResourceList = JsonConvert.DeserializeObject<List<TaskGetResource>>(resultString);
                    //To get member IDs for each task
                    PopulateMemberDetails(tasksResourceList);

                    var mappedTasks = _mapper.Map<List<TaskListResource>>(tasksResourceList);

                    return mappedTasks;
                }
            }
            catch (Exception ex)
            {
                var message = "Unable to retrieve camunda user tasks for assignee " + assignee;
                Helpers.Helpers.LogError(_dbContext, ex, message);
                throw new Exception(message);
            }
        }

        private async Task<List<DWExceptionTaskItemResource>> GetDWExceptionUserTasks(string assignee)
        {
            try
            {
                using (var client = new HttpClient())
                {
                    string definitionKey = "&processDefinitionKey=New-DW-Exception";

                    var uri = _configSettings.CamundaBaseAddress + "/task?assignee=" + assignee + definitionKey;
                    var result = client.Send(new HttpRequestMessage(HttpMethod.Get, uri));

                    result.EnsureSuccessStatusCode();

                    var resultString = result.Content.ReadAsString();
                    var tasksResourceList = JsonConvert.DeserializeObject<List<TaskGetResource>>(resultString);

                    if (tasksResourceList.Count > 0)
                    {
                        var dwtaskItems = PopulateDWExceptionTaskDetails(tasksResourceList);
                        return dwtaskItems;
                    }

                    return new List<DWExceptionTaskItemResource>();
                }
            }
            catch (Exception ex)
            {
                var message = "Unable to retrieve camunda user tasks for assignee " + assignee;
                Helpers.Helpers.LogError(_dbContext, ex, message);
                throw new Exception(message);
            }
        }

        public void PopulateMemberDetails(List<TaskGetResource> tasksResourceList)
        {
            try
            {
                using (var client = new HttpClient())
                {
                    foreach (var task in tasksResourceList)
                    {
                        // TODO: Find better way to fetch and map camunda variables
                        var variablesUri = _configSettings.CamundaBaseAddress +
                                           "/variable-instance?processInstanceIdIn=" + task.ProcessInstanceId;
                        var variables = client.Send(new HttpRequestMessage(HttpMethod.Get, variablesUri));

                        variables.EnsureSuccessStatusCode();

                        var variablesResultString = variables.Content.ReadAsString();
                        JArray array = JArray.Parse(variablesResultString);
                        var found = false;

                        foreach (JObject content in array.Children<JObject>())
                        {
                            foreach (JProperty prop in content.Properties())
                            {
                                if (prop.Name == "type" && prop.First.Value<string>() == "Object")
                                {
                                    content.Remove();
                                    found = true;

                                    break;
                                }
                            }

                            if (found)
                            {
                                break;
                            }
                        }

                        var variablesResultStringModified = array.ToString();
                        var variablesResourceList =
                            JsonConvert.DeserializeObject<List<VariableInstanceGetResource>>(
                                variablesResultStringModified);
                        var memberIDs = variablesResourceList.Where(i =>
                                (i.Type == "Integer" || i.Type == "Long") && (i.Name == "OrganisationID" ||
                                                                              i.Name == "MemberId" ||
                                                                              i.Name == "memberId"))
                            .ToList();

                        if (memberIDs.Count > 0)
                        {
                            task.MemberId = Convert.ToInt32(memberIDs[0].Value);
                        }

                        if (task.MemberId > 0)
                        {
                            var member = _dbContext.Members
                                .Include(i => i.StakeholderManager)
                                .FirstOrDefault(i => i.Id == task.MemberId);
                            if (member != null)
                            {
                                if (member.MembershipTypeId ==
                                    Sacrra.Membership.Database.Enums.MembershipTypes.ALGClient)
                                {
                                    var leader = _dbContext.ALGClientLeaders
                                        .Include(i => i.Leader)
                                        .FirstOrDefault(i => i.ClientId == member.Id);
                                    if (leader != null)
                                    {
                                        task.ALGLeader = leader.Leader.RegisteredName;
                                    }

                                }

                                task.MembershipType = member.MembershipTypeId;
                                task.SacrraIndustryCategory = member.IndustryClassificationId;
                                task.StakeHolderManager = (member.StakeholderManager != null)
                                    ? member.StakeholderManager.FullName
                                    : null;
                                task.RegisteredName = member.RegisteredName;
                                task.Member = member.RegisteredName;
                            }
                        }

                        if (string.IsNullOrEmpty(task.RegisteredName) && task.MemberId > 0)
                        {
                            var member = await _dbContext.Set<Member>()
                                .Include(i => i.StakeholderManager)
                                .AsNoTracking()
                                .FirstOrDefaultAsync(i => i.Id == task.MemberId);

                            task.RegisteredName = (member != null) ? member.RegisteredName : null;
                            task.StakeHolderManager = (member.StakeholderManager != null)
                                ? member.StakeholderManager.FullName
                                : null;
                        }

                        var srnIDs = variablesResourceList
                            .Where(i => (i.Type == "Integer" || i.Type == "Long") && i.Name == "SRNId").ToList();

                        if (srnIDs.Count > 0)
                        {
                            task.SRNId = Convert.ToInt32(srnIDs[0].Value);

                            if (string.IsNullOrEmpty(task.RegisteredName))
                            {
                                var srn = await _dbContext.Set<SRN>()
                                    .Include(i => i.Member)
                                    .ThenInclude(x => x.StakeholderManager)
                                    .AsNoTracking()
                                    .FirstOrDefaultAsync(i => i.Id == Convert.ToInt32(srnIDs[0].Value));

                                task.RegisteredName = (srn != null) ? srn.Member.RegisteredName : null;
                                task.StakeHolderManager = (srn.Member.StakeholderManager != null)
                                    ? srn.Member.StakeholderManager.FullName
                                    : null;

                            }
                        }

                        if (task.SRNId <= 0)
                        {
                            var srnId = 0;
                            var requestType = "";
                            var requestTypeVariable =
                                variablesResourceList.FirstOrDefault(i =>
                                    i.Type == "String" && i.Name == "RequestType");

                            if (requestTypeVariable != null)
                            {
                                requestType = requestTypeVariable.Value;
                            }

                            if (!string.IsNullOrEmpty(requestType))
                            {
                                task.RequestType = requestType;

                                if (srnId > 0)
                                {
                                    var srn = await _dbContext.SRNs
                                        .Include(i => i.Member)
                                        .AsNoTracking()
                                        .FirstOrDefaultAsync(i => i.Id == srnId);

                                    if (srn != null)
                                    {
                                        if (srn.Member != null)
                                        {
                                            task.SRNId = srnId;
                                            task.MemberId = srn.MemberId;
                                            task.RegisteredName = srn.Member.RegisteredName;
                                            task.TradingName = srn.TradingName;
                                            task.SRNNumber = srn.SRNNumber;
                                        }
                                    }
                                }
                            }
                        }
                        else if (task.SRNId > 0)
                        {
                            var srn = await _dbContext.SRNs
                                .Include(i => i.Member)
                                .Include(i => i.SRNStatusUpdates)
                                .Include(x => x.ALGLeader)
                                .AsNoTracking()
                                .FirstOrDefaultAsync(i => i.Id == task.SRNId);


                            if (srn != null)
                            {
                                task.TradingName = srn.TradingName;
                                task.ALGLeader = srn.ALGLeader?.RegisteredName;
                                task.SRNNumber = srn.SRNNumber;

                                // TODO: Keep these 2 lines
                                task.IsPossibleTradingNameDuplicate = (task.Name == "SHM Reviews SRN(s) Application")
                                    ? IsPossibleDuplicateTradingName(srn.Id, srn.TradingName)
                                    : false;
                                task.FileType = variablesResourceList.Find(x => x.Name == "fileType").Value;

                                var srnUpdateTypeVariable =
                                    variablesResourceList.FirstOrDefault(i =>
                                        i.Type == "Long" && i.Name == "SRNUpdateType");
                                if (srnUpdateTypeVariable != null)
                                {
                                    task.SRNUpdateType = Convert.ToInt32(srnUpdateTypeVariable.Value);
                                }
                            }
                        }

                        var fileSubmissionRequestIdVariable =
                            variablesResourceList.Find(x => x.Name == "FileSubmissionRequestId");

                        if (fileSubmissionRequestIdVariable != null)
                        {
                            task.FileSubmissionRequestId = int.Parse(fileSubmissionRequestIdVariable.Value);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                var message = "Unable to retrieve camunda tasks";
                throw new Exception(message);
            }
        }

        public bool IsPossibleDuplicateTradingName(int srnId, string tradingName)
        {
            var tradingNameExists = false;

            if (!string.IsNullOrEmpty(tradingName))
            {
                tradingName = tradingName.Trim();

                //Search for this trading name from the list of SRNs
                var srns = _dbContext.SRNs
                    .Where(i => i.TradingName == tradingName)
                    .Select(x => new SRN { TradingName = x.TradingName })
                    .AsQueryable();

                if (!srns.Any())
                {
                    return false;
                }
                else
                {
                    //Search for this trading name from other SRNs
                    var otherMemberSRNs = _dbContext.SRNs
                        .Where(x => x.Id != srnId)
                        .Select(x => new SRN { TradingName = x.TradingName })
                        .AsEnumerable();

                    if (otherMemberSRNs.Any())
                        tradingNameExists = otherMemberSRNs.Any(i => i.TradingName == tradingName);
                }
            }

            return tradingNameExists;
        }


        private async Task<List<DWExceptionTaskItemResource>> PopulateDWExceptionTaskDetails(List<TaskGetResource> tasksResourceList)
        {
            try
            {
                using (var client = new HttpClient())
                {
                    var dwTaskItemList = new List<DWExceptionTaskItemResource>();

                    foreach (var task in tasksResourceList)
                    {
                        var variablesUri = _configSettings.CamundaBaseAddress + "/variable-instance?processInstanceIdIn=" + task.ProcessInstanceId;
                        var variables = client.Send(new HttpRequestMessage(HttpMethod.Get, variablesUri));
                        variables.EnsureSuccessStatusCode();

                        var variablesResultString = variables.Content.ReadAsString();

                        JArray array = JArray.Parse(variablesResultString);
                        var found = false;

                        foreach (JObject content in array.Children<JObject>())
                        {
                            foreach (JProperty prop in content.Properties())
                            {
                                if (prop.Name == "type" && prop.First.Value<string>() == "Object")
                                {
                                    content.Remove();
                                    found = true;
                                    break;
                                }
                            }

                            if (found)
                            {
                                break;
                            }
                        }

                        var variablesResultStringModified = array.ToString();
                        var variablesResourceList = JsonConvert.DeserializeObject<List<VariableInstanceGetResource>>(variablesResultStringModified);

                        var fctWarehouseExceptionIDVariable = variablesResourceList.FirstOrDefault(i => i.Name == "FctWarehouseExceptionID");
                        long fctWarehouseExceptionID = 0;

                        if (fctWarehouseExceptionIDVariable != null)
                        {
                            fctWarehouseExceptionID = Convert.ToInt64(fctWarehouseExceptionIDVariable.Value);
                        }

                        if (fctWarehouseExceptionID > 0)
                        {
                            var dWException = _dWExceptionRepository.GetByDWExceptionId(fctWarehouseExceptionID);

                            var dwTaskItem = _mapper.Map<DWExceptionTaskItemResource>(dWException);
                            dwTaskItem.TaskId = task.Id;
                            dwTaskItem.TaskName = task.Name;

                            dwTaskItemList.Add(dwTaskItem);
                        }
                    }

                    return dwTaskItemList;
                }
            }
            catch (Exception ex)
            {
                var message = "Unable to retrieve camunda tasks";
                Helpers.Helpers.LogError(_dbContext, ex, message);
                throw new Exception(message);
            }
        }

        public StakeHolderManagerTaskListResource GetStakeHolderManagerTasks(string id, List<string> processDefinitionKeys = null)
        {
            var stakeHolderManager = new StakeHolderManagerTaskListResource();

            foreach (var key in processDefinitionKeys)
            {
                stakeHolderManager.Tasks.AddRange(GetUserTasks(id, key));
            }

            return stakeHolderManager;
        }

        public GenericTaskListResource GetFinancialAdminTasks(string id, List<string> processDefinitionKeys = null)
        {
            var financialAdminstratorManager = new GenericTaskListResource();

            foreach (var key in processDefinitionKeys)
            {
                financialAdminstratorManager.Tasks.AddRange(GetUserTasks(id, key));
            }

            return financialAdminstratorManager;
        }

        public UserTaskListGetResource GetSacrraAdminTasks(string id, List<string> processDefinitionKeys = null)
        {
            var sacrraAdministratorManager = new UserTaskListGetResource();

            foreach (var key in processDefinitionKeys)
            {
                sacrraAdministratorManager.Tasks.AddRange(GetUserTasks(id, key));
            }

            return sacrraAdministratorManager;
        }
        public DWExceptionTaskGetResource GetDWExceptionTasks()
        {
            var taskGetResource = new DWExceptionTaskGetResource();
            var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext);

            taskGetResource.Tasks = GetDWExceptionUserTasks(user.Id.ToString());

            return taskGetResource;
        }

        public async Task<List<UserTaskListGetResource>> Tasks(List<string> processDefinitionKeys = null)
        {
            List<UserRoles> roles = new List<UserRoles>() { UserRoles.StakeHolderAdministrator, UserRoles.StakeHolderManager };

            var userTasks = _userRepository.ListInternalUsers(roles);

            foreach (var user in userTasks)
            {
                foreach (var key in processDefinitionKeys)
                {
                    user.Tasks.AddRange(GetUserTasks(user.Id, key));
                }
            }

            return userTasks;
        }

        public async Task<List<UserTaskListGetResource>> GetStakeHolderAdminTasks(int id, string processDefinitionKey = null)
        {
            List<UserRoles> roles = new List<UserRoles>() { UserRoles.StakeHolderAdministrator, UserRoles.StakeHolderManager };

            var allUsers = _userRepository.ListInternalUsers(roles);
            var stakeHolderAdmin = allUsers.FirstOrDefault(i => i.Id == id.ToString());
            var stakeHolderManagers = allUsers.Where(i => i.Role == "StakeHolderManager").ToList();

            stakeHolderAdmin.Tasks = GetUserTasks(id.ToString(), processDefinitionKey);

            List<UserTaskListGetResource> allUserTasks = new List<UserTaskListGetResource>();

            allUserTasks.Add(stakeHolderAdmin);
            foreach (var manager in stakeHolderManagers)
            {
                manager.Tasks = GetUserTasks(manager.Id, processDefinitionKey);
                allUserTasks.Add(manager);
            }

            return allUserTasks;
        }

        public void ReAssignTask(string taskId, TaskUpdateResource taskResource)
        {
            DefaultContractResolver contractResolver = new DefaultContractResolver
            {
                NamingStrategy = new CamelCaseNamingStrategy()
            };

            using (var client = new HttpClient())
            {
                var json = JsonConvert.SerializeObject(taskResource, new JsonSerializerSettings
                {
                    ContractResolver = contractResolver,
                    Formatting = Formatting.Indented
                });
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                //  send a POST request
                var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/assignee";
                var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });

                // on error throw a exception
                result.EnsureSuccessStatusCode();
            }
        }

        private TaskGetResource GetTask(string id)
        {
            try
            {
                using (var client = new HttpClient())
                {
                    var uri = _configSettings.CamundaBaseAddress + "/task/" + id;
                    var result = client.Send(new HttpRequestMessage(HttpMethod.Get, uri));
                    result.EnsureSuccessStatusCode();

                    var resultString = result.Content.ReadAsString();
                    var taskResource = JsonConvert.DeserializeObject<TaskGetResource>(resultString);

                    return taskResource;
                }
            }
            catch (Exception ex)
            {
                var message = "Unable to retrieve camunda task with id " + id;
                Helpers.Helpers.LogError(_dbContext, ex, message);
                throw new Exception(message);
            }
        }

        public async Task<List<VariableInstanceGetResource>> GetVariables(string processInstanceId)
        {
            try
            {
                using (var client = new HttpClient())
                {
                    var uri = _configSettings.CamundaBaseAddress + "/variable-instance?processInstanceIdIn=" + processInstanceId;
                    var result = client.Send(new HttpRequestMessage(HttpMethod.Get, uri));
                    result.EnsureSuccessStatusCode();

                    var resultString = result.Content.ReadAsString();
                    var variablesResourceList = JsonConvert.DeserializeObject<List<VariableInstanceGetResource>>(resultString);

                    return variablesResourceList;
                }
            }
            catch (Exception ex)
            {
                var message = "Unable to retrieve variables for process id " + processInstanceId;
                Helpers.Helpers.LogError(_dbContext, ex, message);
                throw new Exception(message);
            }
        }

        public async Task<List<VariableInstanceGetResource>> GetMemberVariables(string processInstanceId)
        {
            try
            {
                using (var client = new HttpClient())
                {
                    var uri = _configSettings.CamundaBaseAddress + "/variable-instance?processInstanceIdIn=" + processInstanceId;
                    var result = client.Send(new HttpRequestMessage(HttpMethod.Get, uri));
                    result.EnsureSuccessStatusCode();

                    var resultString = result.Content.ReadAsString();

                    return PrepareCamundaVariables(resultString);
                }
            }
            catch (Exception ex)
            {
                var message = "Unable to retrieve variables for process id " + processInstanceId;
                Helpers.Helpers.LogError(_dbContext, ex, message);
                throw new Exception(message);
            }
        }

        private List<VariableInstanceGetResource> PrepareCamundaVariables(string httpResultString)
        {
            try
            {
                JArray array = JArray.Parse(httpResultString);
                var found = false;

                foreach (JObject content in array.Children<JObject>())
                {
                    foreach (JProperty prop in content.Properties())
                    {
                        if (prop.Name == "type" && prop.First.Value<string>() == "Object")
                        {
                            content.Remove();
                            found = true;
                            break;
                        }
                    }

                    if (found)
                    {
                        break;
                    }
                }

                var variablesResultStringModified = array.ToString();
                var variablesResourceList = JsonConvert.DeserializeObject<List<VariableInstanceGetResource>>(variablesResultStringModified);

                return variablesResourceList;
            }
            catch (Exception ex)
            {
                var message = "Unable to prepare variables for the result string:  " + httpResultString;
                Helpers.Helpers.LogError(_dbContext, ex, message);
                throw new Exception(message);
            }
        }

        public void CompleteFinancialAdminGenerateInvoiceTask(string taskId)
        {
            var task = GetTask(taskId);
            var variables = GetVariables(task.ProcessInstanceId);

            var memberId = variables.FirstOrDefault(i => i.Name == "OrganisationID").Value;

            var member = await _dbContext.Members
            .AsNoTracking()
            .FirstOrDefaultAsync(i => i.Id == Convert.ToInt32(memberId));

            ApplicationStatuses oldMemberStatus = member.ApplicationStatusId;

            if (member != null)
                member.ApplicationStatusId = ApplicationStatuses.MemberRegistrationAwaitingPayment;

            _dbContext.Set<Member>().Update(member);
            _dbContext.SaveChanges();

            using (var client = new HttpClient())
            {
                var content = new StringContent("", Encoding.UTF8, "application/json");
                var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });
                result.EnsureSuccessStatusCode();
            }

            MemberStagingChangeLogResource stagingChangeLog = new();

            stagingChangeLog.Changes.Add(new StagingChange
            {
                Name = "Application Status",
                OldValue = EnumHelper.GetEnumIdValuePair<ApplicationStatuses>((int)oldMemberStatus).Value,
                NewValue = EnumHelper.GetEnumIdValuePair<ApplicationStatuses>((int)member.ApplicationStatusId).Value
            });

            CreateMemberStatusUpdateEventLog(member, stagingChangeLog);
        }

        public void CompleteFinancialAdminGenerateAssessmentInvoiceTask(string taskId)
        {
            var contractResolver = new DefaultContractResolver
            {
                NamingStrategy = new CamelCaseNamingStrategy()
            };

            var task = GetTask(taskId);
            var variables = GetVariables(task.ProcessInstanceId);

            var memberId = variables.FirstOrDefault(i => i.Name == "OrganisationID").Value;

            var member = await _dbContext.Members
            .AsNoTracking()
            .FirstOrDefaultAsync(i => i.Id == Convert.ToInt32(memberId));

            ApplicationStatuses oldMemberStatus = member.ApplicationStatusId;

            if (member != null)
                member.ApplicationStatusId = ApplicationStatuses.MemberRegistrationAwaitingInitialAssessmentInvoicePayment;

            _dbContext.Set<Member>().Update(member);
            _dbContext.SaveChanges();

            using (var client = new HttpClient())
            {
                var content = new StringContent("", Encoding.UTF8, "application/json");
                var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });
                result.EnsureSuccessStatusCode();
            }

            MemberStagingChangeLogResource stagingChangeLog = new();

            stagingChangeLog.Changes.Add(new StagingChange
            {
                Name = "Application Status",
                OldValue = EnumHelper.GetEnumIdValuePair<ApplicationStatuses>((int)oldMemberStatus).Value,
                NewValue = EnumHelper.GetEnumIdValuePair<ApplicationStatuses>((int)member.ApplicationStatusId).Value
            });

            CreateMemberStatusUpdateEventLog(member, stagingChangeLog);
        }

        public void CompleteFinancialAdminGenerateOnboardingInvoiceTask(string taskId)
        {
            var contractResolver = new DefaultContractResolver
            {
                NamingStrategy = new CamelCaseNamingStrategy()
            };

            var task = GetTask(taskId);
            var variables = GetVariables(task.ProcessInstanceId);

            var memberId = variables.FirstOrDefault(i => i.Name == "OrganisationID").Value;

            var member = await _dbContext.Members
                .AsNoTracking()
                .FirstOrDefaultAsync(i => i.Id == Convert.ToInt32(memberId));

            ApplicationStatuses oldMemberStatus = member.ApplicationStatusId;

            if (member != null)
                member.ApplicationStatusId = ApplicationStatuses.MemberRegistrationAwaitingOnboardingInvoicePayment;

            _dbContext.Set<Member>().Update(member);
            _dbContext.SaveChanges();

            using (var client = new HttpClient())
            {
                var content = new StringContent("", Encoding.UTF8, "application/json");
                var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });
                result.EnsureSuccessStatusCode();
            }

            MemberStagingChangeLogResource stagingChangeLog = new();

            stagingChangeLog.Changes.Add(new StagingChange
            {
                Name = "Application Status",
                OldValue = EnumHelper.GetEnumIdValuePair<ApplicationStatuses>((int)oldMemberStatus).Value,
                NewValue = EnumHelper.GetEnumIdValuePair<ApplicationStatuses>((int)member.ApplicationStatusId).Value
            });

            CreateMemberStatusUpdateEventLog(member, stagingChangeLog);
        }

        public void CompleteFinancialAdminCheckPaymentTask(string taskId, TaskUpdateFinancialAdminResource taskUpdateResource)
        {
            var contractResolver = new DefaultContractResolver
            {
                NamingStrategy = new CamelCaseNamingStrategy()
            };

            var task = GetTask(taskId);
            var variables = GetMemberVariables(task.ProcessInstanceId);

            var memberId = variables.FirstOrDefault(i => i.Name == "OrganisationID").Value;

            var member = await _dbContext.Members
            .AsNoTracking()
            .FirstOrDefaultAsync(i => i.Id == Convert.ToInt32(memberId));

            string paymentReceived = "";

            if (taskUpdateResource.PaymentRecieved == "received")
            {
                member.ApplicationStatusId = ApplicationStatuses.MemberRegistrationPaymentReceived;
                paymentReceived = "received";
            }
            //Pending
            else if (taskUpdateResource.PaymentRecieved == "notReceived")
            {
                member.ApplicationStatusId = ApplicationStatuses.MemberRegistrationAwaitingPayment;
                paymentReceived = "pending";
            }
            else if (taskUpdateResource.PaymentRecieved == "expired")
            {
                member.ApplicationStatusId = ApplicationStatuses.MemberRegistrationDisqualified;
                member.DisqualificationReason = "Payment not received within grace period";
                paymentReceived = "notReceived";
            }

            _dbContext.Set<Member>().Update(member);
            _dbContext.SaveChanges();

            using (var client = new HttpClient())
            {
                var taskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                        {
                            {
                                "variables",
                                new Dictionary<string, Dictionary<string, string>>
                                {
                                    {
                                        "fullMemberPaymentReceived",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", paymentReceived },
                                            { "type", "String" }
                                        }
                                    }
                                }
                            }
                        };

                var json = JsonConvert.SerializeObject(taskVariables, new JsonSerializerSettings
                {
                    ContractResolver = contractResolver,
                    Formatting = Formatting.Indented
                });
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });

                result.EnsureSuccessStatusCode();
            }
        }

        public void CompleteStakeHolderAdminTask(string taskId, TaskUpdateStakeHolderAdminResource taskUpdateResource)
        {
            var contractResolver = new DefaultContractResolver
            {
                NamingStrategy = new CamelCaseNamingStrategy()
            };

            using (var client = new HttpClient())
            {
                var task = GetTask(taskId);
                var variables = GetVariables(task.ProcessInstanceId);
                var completeStakeHolderAdminResource = new CompleteStakeHolderAdministratorTaskResource();

                if (variables.Count > 0)
                {
                    var memberId = variables.FirstOrDefault(i => i.Name == "OrganisationID").Value;

                    var member = await _dbContext.Members
                    .AsNoTracking()
                    .FirstOrDefaultAsync(i => i.Id == Convert.ToInt32(memberId));

                    ApplicationStatuses oldMemberStatus = member.ApplicationStatusId;

                    if (member != null)
                    {
                        member.StakeholderManagerId = taskUpdateResource.UserId;
                        member.ApplicationStatusId = ApplicationStatuses.MemberRegistrationReview;
                        _dbContext.Set<Member>().Update(member);
                        _dbContext.SaveChanges();
                    }

                    completeStakeHolderAdminResource.Variables.StakeHolderManagerAssignee.Value = taskUpdateResource.UserId.ToString();
                    completeStakeHolderAdminResource.Variables.StakeHolderManagerAssignee.Type = "String";

                    var json = JsonConvert.SerializeObject(completeStakeHolderAdminResource, new JsonSerializerSettings
                    {
                        ContractResolver = contractResolver,
                        Formatting = Formatting.Indented
                    });
                    var content = new StringContent(json, Encoding.UTF8, "application/json");

                    //  send a POST request
                    var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                    var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });

                    // on error throw a exception
                    result.EnsureSuccessStatusCode();

                    MemberStagingChangeLogResource stagingChangeLog = new();

                    stagingChangeLog.Changes.Add(new StagingChange
                    {
                        Name = "Application Status",
                        OldValue = EnumHelper.GetEnumIdValuePair<ApplicationStatuses>((int)oldMemberStatus).Value,
                        NewValue = EnumHelper.GetEnumIdValuePair<ApplicationStatuses>((int)member.ApplicationStatusId).Value
                    });

                    if (taskUpdateResource.UserId > 0)
                    {
                        var shm = _dbContext.Users
                            .Select(m => new User
                            {
                                Id = m.Id,
                                FirstName = m.FirstName,
                                LastName = m.LastName
                            })
                            .FirstOrDefault(i => i.Id == taskUpdateResource.UserId);

                        if (shm != null)
                        {
                            stagingChangeLog.Changes.Add(new StagingChange
                            {
                                Name = "Stakeholder Manager",
                                OldValue = "",
                                NewValue = $"{shm.FirstName} {shm.LastName}"
                            });
                        }
                    }

                    CreateMemberStatusUpdateEventLog(member, stagingChangeLog);
                }
            }
        }

        public void CompleteStakeHolderManagerReviewMemberApplicationTask(string taskId, TaskUpdateStakeHolderManagerResource taskUpdateResource)
        {
            var contractResolver = new DefaultContractResolver
            {
                NamingStrategy = new CamelCaseNamingStrategy()
            };

            var task = GetTask(taskId);
            var variables = GetVariables(task.ProcessInstanceId);
            var shmDecision = "";

            if (variables.Count > 0)
            {
                var memberId = variables.FirstOrDefault(i => i.Name == "OrganisationID").Value;

                var member = await _dbContext.Members
                    .AsNoTracking()
                    .FirstOrDefaultAsync(i => i.Id == Convert.ToInt32(memberId));

                ApplicationStatuses oldMemberStatus = member.ApplicationStatusId;
                MembershipTypes oldMemershipType = member.MembershipTypeId;

                if (!string.IsNullOrEmpty(taskUpdateResource.ReviewDecision))
                {
                    if (taskUpdateResource.ReviewDecision.ToLower() == "disqualified" || taskUpdateResource.ReviewDecision.ToLower() == "no")
                    {
                        shmDecision = "disqualified";
                        if (member != null)
                        {
                            member.DisqualificationReason = taskUpdateResource.RejectReason;
                            member.ApplicationStatusId = ApplicationStatuses.MemberRegistrationDisqualified;
                        }
                    }
                    else if (taskUpdateResource.ReviewDecision.ToLower() == "yes")
                    {
                        if (taskUpdateResource.MembershipTypeId == null)
                        {
                            if (member.MembershipTypeId == MembershipTypes.FullMember)
                            {
                                shmDecision = "fullMember";
                                member.ApplicationStatusId = ApplicationStatuses.MemberRegistrationWaitingForInvoiceToBeGenerated;
                            }
                            else if (member.MembershipTypeId == MembershipTypes.NonMember)
                            {
                                shmDecision = "nonMember";
                                member.ApplicationStatusId = ApplicationStatuses.MemberRegistrationAwaitingOnboardingInvoicePayment;
                            }
                            else if (member.MembershipTypeId == MembershipTypes.ALGClient)
                            {
                                shmDecision = "algClient";
                                member.ApplicationStatusId = ApplicationStatuses.MemberRegistrationCompleted;
                            }
                        }

                        else if (taskUpdateResource.MembershipTypeId == MembershipTypes.FullMember)
                        {
                            shmDecision = "fullMember";
                            member.MembershipTypeId = MembershipTypes.FullMember;
                            member.ApplicationStatusId = ApplicationStatuses.MemberRegistrationWaitingForInvoiceToBeGenerated;
                        }
                        else if (taskUpdateResource.MembershipTypeId == MembershipTypes.NonMember)
                        {
                            shmDecision = "nonMember";
                            member.MembershipTypeId = MembershipTypes.NonMember;
                            member.ApplicationStatusId = ApplicationStatuses.MemberRegistrationAwaitingOnboardingInvoicePayment;
                        }
                        else if (taskUpdateResource.MembershipTypeId == MembershipTypes.ALGClient)
                        {
                            shmDecision = "algClient";
                            member.MembershipTypeId = MembershipTypes.ALGClient;
                            member.ApplicationStatusId = ApplicationStatuses.MemberRegistrationCompleted;
                        }
                    }

                    else if (taskUpdateResource.ReviewDecision == "fullMember")
                    {
                        shmDecision = "fullMember";
                        member.MembershipTypeId = MembershipTypes.FullMember;
                        member.ApplicationStatusId = ApplicationStatuses.MemberRegistrationWaitingForInvoiceToBeGenerated;
                    }
                    else if (taskUpdateResource.ReviewDecision == "nonMember")
                    {
                        shmDecision = "nonMember";
                        member.MembershipTypeId = MembershipTypes.NonMember;
                        member.ApplicationStatusId = ApplicationStatuses.MemberRegistrationAwaitingOnboardingInvoicePayment;
                    }
                    else if (taskUpdateResource.ReviewDecision == "algClient")
                    {
                        shmDecision = "algClient";
                        member.MembershipTypeId = MembershipTypes.ALGClient;
                        member.ApplicationStatusId = ApplicationStatuses.MemberRegistrationCompleted;
                    }

                    _dbContext.Set<Member>().Update(member);
                    _dbContext.SaveChanges();

                    MemberStagingChangeLogResource stagingChangeLog = new();

                    stagingChangeLog.Changes.Add(new StagingChange
                    {
                        Name = "Application Status",
                        OldValue = EnumHelper.GetEnumIdValuePair<ApplicationStatuses>((int)oldMemberStatus).Value,
                        NewValue = EnumHelper.GetEnumIdValuePair<ApplicationStatuses>((int)member.ApplicationStatusId).Value
                    });

                    stagingChangeLog.Changes.Add(new StagingChange
                    {
                        Name = "Membership Type",
                        OldValue = EnumHelper.GetEnumIdValuePair<MembershipTypes>((int)oldMemershipType).Value,
                        NewValue = EnumHelper.GetEnumIdValuePair<MembershipTypes>((int)member.MembershipTypeId).Value
                    });

                    if (!string.IsNullOrWhiteSpace(member.DisqualificationReason))
                    {
                        stagingChangeLog.Changes.Add(new StagingChange
                        {
                            Name = "Disqualification Reason",
                            OldValue = "",
                            NewValue = member.DisqualificationReason
                        });
                    }

                    CreateMemberStatusUpdateEventLog(member, stagingChangeLog);

                    var taskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                        {
                            {
                                "variables",
                                new Dictionary<string, Dictionary<string, string>>
                                {
                                    {
                                        "reviewApplicationDecision",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", shmDecision },
                                            { "type", "String" }
                                        }
                                    }
                                }
                            }
                        };

                    using (var client = new HttpClient())
                    {
                        var json = JsonConvert.SerializeObject(taskVariables, new JsonSerializerSettings
                        {
                            Formatting = Formatting.Indented
                        });
                        var content = new StringContent(json, Encoding.UTF8, "application/json");
                        var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                        var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });
                        result.EnsureSuccessStatusCode();
                    }
                }
            }
        }
        public void CompleteStakeHolderManagerFinalTakeOnTask(string taskId)
        {
            var task = GetTask(taskId);
            var variables = GetMemberVariables(task.ProcessInstanceId);

            var memberId = variables.FirstOrDefault(i => i.Name == "OrganisationID").Value;

            var member = await _dbContext.Members
            .AsNoTracking()
            .FirstOrDefaultAsync(i => i.Id == Convert.ToInt32(memberId));

            member.ApplicationStatusId = ApplicationStatuses.MemberRegistrationCompleted;
            _dbContext.Set<Member>().Update(member);
            _dbContext.SaveChanges();

            using (var client = new HttpClient())
            {
                var content = new StringContent("", Encoding.UTF8, "application/json");
                var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });
                result.EnsureSuccessStatusCode();
            }
        }

        public MemberGetResource GetMember(int id)
        {
            var member = await _dbContext.Members
                    .Include(i => i.StakeholderManager)
                    .AsNoTracking()
                    .FirstOrDefaultAsync(s => s.Id == id);

            var resouce = _mapper.Map<MemberGetResource>(member);

            return resouce;
        }

        public void SendMemberApplicationCancellationEmail(int memberId)
        {
            try
            {
                var member = await _dbContext.Members
                    .AsNoTracking()
                    .FirstOrDefaultAsync(i => i.Id == memberId);

                if (member != null)
                {
                    var mainContact = Helpers.Helpers.GetMemberMainContact(_dbContext, memberId);

                    if (mainContact != null)
                    {
                        var applicant = mainContact;
                        var placeholders = new List<KeyValuePair<string, string>>
                        {
                            new KeyValuePair<string, string>("[MemberRegisteredName]", member.RegisteredName)
                        };

                        _emailService.SendEmail(applicant.Email, applicant.FirstName, "Member Application Cancellation", "MemberApplicationCancellationApplicant.html", placeholders);
                    }
                }
            }
            catch (Exception ex)
            {
                var message = "Unable to email applicant for member application cancellation. Member Id " + memberId;
                Helpers.Helpers.LogError(_dbContext, ex, message);
                throw new Exception(message);
            }
        }

        public void CompleteStakeHolderManagerSRNFirstReviewTask(string taskId, TaskCompleteSRNReviewResource taskCompleteResource)
        {
            using (var client = new HttpClient())
            {
                var task = GetTask(taskId);
                var currentTaskVariables = GetVariables(task.ProcessInstanceId);

                var newTaskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                    {
                        {
                            "variables",
                            new Dictionary<string, Dictionary<string, string>>
                            {
                                {
                                    "srnVerified1",
                                    new Dictionary<string, string>()
                                    {
                                        { "value", taskCompleteResource.IsVerified },
                                        { "type", "String" }
                                    }
                                }
                            }
                        }
                    };

                if (currentTaskVariables.Count > 0)
                {
                    var json = JsonConvert.SerializeObject(newTaskVariables);
                    var content = new StringContent(json, Encoding.UTF8, "application/json");
                    var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                    var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });
                    result.EnsureSuccessStatusCode();

                    //Create audit log and update SRN status
                    var srnId = currentTaskVariables.FirstOrDefault(i => i.Name == "SRNId").Value;

                    var srn = await _dbContext.Set<SRN>()
                        .Include(i => i.SRNStatus)
                        .Include(i => i.SRNStatusUpdates)
                        .FirstOrDefaultAsync(i => i.Id == Convert.ToInt32(srnId)
                            && i.SRNStatusUpdates.Any(x => x.ProcessInstanceId == task.ProcessInstanceId));

                    if (srn != null)
                    {
                        srn.FirstReviewRejectReason = (!string.IsNullOrEmpty(taskCompleteResource.RejectReason)) ? taskCompleteResource.RejectReason : taskCompleteResource.RejectReason;

                        var srnUpdateResource = _mapper.Map<SRNUpdateResource>(srn);
                        var entityBlob = JsonConvert.SerializeObject(srnUpdateResource);

                        var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext);

                        var stagingChangeLog = new MemberStagingChangeLogResource();

                        var stagingChange = new StagingChange
                        {
                            Name = "SRN Status",
                            OldValue = srn.SRNStatus.Name
                        };

                        var recentUpdate = srn.SRNStatusUpdates.FirstOrDefault(i => i.ProcessInstanceId == task.ProcessInstanceId);

                        if (taskCompleteResource.IsVerified == "yes")
                        {
                            var status = _dbContext.SRNStatuses
                                .AsNoTracking()
                                .FirstOrDefault(i => i.Name == "SHM Verification");

                            if (srn.FileType == SRNStatusFileTypes.MonthlyAndDailyFile && srn.SRNStatusId == recentUpdate.SRNStatusId)
                            {
                                _srnRepository.UpdateSRNStatus("SHM Verification", srn);
                                stagingChange.NewValue = "SHM Verification";
                            }
                            else if (srn.FileType == SRNStatusFileTypes.DailyFile || srn.FileType == SRNStatusFileTypes.MonthlyFile)
                            {
                                _srnRepository.UpdateSRNStatus("SHM Verification", srn);
                            }

                            srn.SRNStatusUpdates.FirstOrDefault(i => i.ProcessInstanceId == task.ProcessInstanceId).SRNStatusId = status.Id;
                        }
                        else if (taskCompleteResource.IsVerified == "no")
                        {
                            var status = _dbContext.SRNStatuses
                                .AsNoTracking()
                                .FirstOrDefault(i => i.Name == "SHM Verification");

                            _srnRepository.UpdateSRNStatus("Rejected", srn);
                            stagingChange.NewValue = "Rejected";

                            srn.SRNStatusUpdates.FirstOrDefault(i => i.ProcessInstanceId == task.ProcessInstanceId).SRNStatusId = status.Id;
                        }


                        stagingChangeLog.Changes.Add(stagingChange);

                        _dbContext.SaveChanges();

                        var changeBlob = JsonConvert.SerializeObject(stagingChangeLog);

                        Helpers.Helpers.CreateEventLog(_dbContext, user.Id, "SRN Update", srn.TradingName, entityBlob, changeBlob, srn.Id, "SRN");
                    }
                }
            }
        }

        public void CompleteStakeHolderManagerSecondSRNReviewTask(string taskId, TaskCompleteSRNReviewResource taskCompleteResource)
        {
            using (var client = new HttpClient())
            {
                var task = GetTask(taskId);
                var currentTaskVariables = GetVariables(task.ProcessInstanceId);

                SRN srn = null;
                var recentSRNStatusUpdate = new SRNStatusUpdateHistory();

                if (currentTaskVariables.Count > 0)
                {
                    var srnId = currentTaskVariables.FirstOrDefault(i => i.Name == "SRNId").Value;

                    srn = await _dbContext.Set<SRN>()
                        .Include(i => i.SRNStatus)
                        .Include(i => i.SRNStatusUpdates)
                        .FirstOrDefaultAsync(i => i.Id == Convert.ToInt32(srnId));

                    recentSRNStatusUpdate = srn.SRNStatusUpdates.FirstOrDefault(i => i.ProcessInstanceId == task.ProcessInstanceId);
                }

                var newTaskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                    {
                        {
                            "variables",
                            new Dictionary<string, Dictionary<string, string>>
                            {
                                {
                                    "srnVerified2",
                                    new Dictionary<string, string>()
                                    {
                                        { "value", taskCompleteResource.IsVerified },
                                        { "type", "String" }
                                    }
                                }
                            }
                        }
                    };

                if (srn != null)
                {
                    srn.SecondReviewRejectReason = (!string.IsNullOrEmpty(taskCompleteResource.RejectReason)) ? taskCompleteResource.RejectReason : taskCompleteResource.RejectReason;

                    var srnUpdateResource = _mapper.Map<SRNUpdateResource>(srn);
                    var entityBlob = JsonConvert.SerializeObject(srnUpdateResource);

                    var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext);

                    var stagingChangeLog = new MemberStagingChangeLogResource();

                    var stagingChange = new StagingChange
                    {
                        Name = "SRN Status",
                        OldValue = srn.SRNStatus.Name
                    };

                    if (taskCompleteResource.IsVerified == "yes")
                    {
                        var status = _dbContext.SRNStatuses
                                .AsNoTracking()
                                .FirstOrDefault(i => i.Name == "Second Verification");

                        if (srn.FileType == SRNStatusFileTypes.MonthlyAndDailyFile && srn.SRNStatusId == recentSRNStatusUpdate.SRNStatusId)
                        {
                            _srnRepository.UpdateSRNStatus("Second Verification", srn);
                            stagingChange.NewValue = "Second Verification";
                        }
                        else if (srn.FileType == SRNStatusFileTypes.DailyFile || srn.FileType == SRNStatusFileTypes.MonthlyFile)
                        {
                            _srnRepository.UpdateSRNStatus("Second Verification", srn);
                            stagingChange.NewValue = "Second Verification";
                        }

                        recentSRNStatusUpdate.SRNStatusId = status.Id;
                    }
                    else if (taskCompleteResource.IsVerified == "no")
                    {
                        var status = _dbContext.SRNStatuses
                                .AsNoTracking()
                                .FirstOrDefault(i => i.Name == "Second Verification");

                        if (srn.FileType == SRNStatusFileTypes.MonthlyAndDailyFile && srn.SRNStatusId == recentSRNStatusUpdate.SRNStatusId)
                        {
                            _srnRepository.UpdateSRNStatus("Rejected", srn);
                            stagingChange.NewValue = "Rejected";
                        }
                        else if (srn.FileType == SRNStatusFileTypes.DailyFile || srn.FileType == SRNStatusFileTypes.MonthlyFile)
                        {
                            _srnRepository.UpdateSRNStatus("Rejected", srn);
                            stagingChange.NewValue = "Rejected";
                        }

                        recentSRNStatusUpdate.SRNStatusId = status.Id;
                    }

                    stagingChangeLog.Changes.Add(stagingChange);

                    _dbContext.SaveChanges();

                    var changeBlob = JsonConvert.SerializeObject(stagingChangeLog);

                    Helpers.Helpers.CreateEventLog(_dbContext, user.Id, "SRN Update", srn.TradingName, entityBlob, changeBlob, srn.Id, "SRN");
                }

                var json = JsonConvert.SerializeObject(newTaskVariables);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });
                result.EnsureSuccessStatusCode();
            }
        }

        public void CompleteStakeHolderManagerSRNAssignToSecondReviewerTask(string taskId, int userId)
        {
            using (var client = new HttpClient())
            {
                var task = GetTask(taskId);
                var currentTaskVariables = GetVariables(task.ProcessInstanceId);

                var newTaskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                    {
                        {
                            "variables",
                            new Dictionary<string, Dictionary<string, string>>
                            {
                                {
                                    "SecondReviewerAssignee",
                                    new Dictionary<string, string>()
                                    {
                                        { "value", userId.ToString() },
                                        { "type", "String" }
                                    }
                                }
                            }
                        }
                    };

                if (currentTaskVariables.Count > 0)
                {
                    var srnId = currentTaskVariables.FirstOrDefault(i => i.Name == "SRNId").Value;

                    var srn = await _dbContext.Set<SRN>()
                        .Include(i => i.SRNStatusUpdates)
                        .FirstOrDefaultAsync(i => i.Id == Convert.ToInt32(srnId));

                    if (srn != null)
                    {
                        var status = _dbContext.SRNStatuses
                                .AsNoTracking()
                                .FirstOrDefault(i => i.Name == "Second Verification");

                        var recentUpdate = srn.SRNStatusUpdates.FirstOrDefault(i => i.ProcessInstanceId == task.ProcessInstanceId);

                        if (srn.FileType == SRNStatusFileTypes.MonthlyAndDailyFile && srn.SRNStatusId == recentUpdate.SRNStatusId)
                        {
                            _srnRepository.UpdateSRNStatus("Second Verification", srn);
                        }
                        else if (srn.FileType == SRNStatusFileTypes.DailyFile || srn.FileType == SRNStatusFileTypes.MonthlyFile)
                        {
                            _srnRepository.UpdateSRNStatus("Second Verification", srn);
                        }

                        recentUpdate.SRNStatusId = status.Id;

                        _dbContext.SaveChanges();
                    }

                    var json = JsonConvert.SerializeObject(newTaskVariables);
                    var content = new StringContent(json, Encoding.UTF8, "application/json");
                    var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                    var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });
                    result.EnsureSuccessStatusCode();
                }
            }
        }

        public void CompleteSACRRAAdminSRNTakeOnUpdateDTHTask(string taskId)
        {
            using (var client = new HttpClient())
            {
                var task = GetTask(taskId);
                var currentTaskVariables = GetVariables(task.ProcessInstanceId);

                if (currentTaskVariables.Count > 0)
                {
                    var srnIdVariable = currentTaskVariables.FirstOrDefault(i => i.Name == "SRNId" || i.Name == "memberId").Value;
                    int srnId = (!string.IsNullOrEmpty(srnIdVariable)) ? Convert.ToInt32(srnIdVariable) : 0;

                    if (srnId > 0)
                    {
                        var newTaskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                            {
                                {
                                    "variables",
                                    new Dictionary<string, Dictionary<string, string>>{}
                                }
                            };

                        var json = JsonConvert.SerializeObject(newTaskVariables);
                        var content = new StringContent(json, Encoding.UTF8, "application/json");
                        var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                        var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });
                        result.EnsureSuccessStatusCode();
                    }
                }
            }
        }

        public void CompleteSACRRAAdminSRNSaleSplitMergeUpdateDTHTask(string taskId)
        {
            using (var client = new HttpClient())
            {
                var newTaskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                            {
                                {
                                    "variables",
                                    new Dictionary<string, Dictionary<string, string>>{}
                                }
                            };

                var json = JsonConvert.SerializeObject(newTaskVariables);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });
                result.EnsureSuccessStatusCode();
            }
        }

        public void CompleteSACRRAAdminSRNStatusChangedConfirmUpdateDTHTask(string taskId)
        {
            using (var client = new HttpClient())
            {
                var newTaskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                            {
                                {
                                    "variables",
                                    new Dictionary<string, Dictionary<string, string>>{}
                                }
                            };

                var json = JsonConvert.SerializeObject(newTaskVariables);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });
                result.EnsureSuccessStatusCode();
            }
        }


        public void CompleteSACRRAAdminSRNTestAddedToDTH(string taskId)
        {
            using (var client = new HttpClient())
            {
                var newTaskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                            {
                                {
                                    "variables",
                                    new Dictionary<string, Dictionary<string, string>>{}
                                }
                            };

                var json = JsonConvert.SerializeObject(newTaskVariables);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });
                result.EnsureSuccessStatusCode();
            }
        }

        public void CompleteStakeHolderManagerReviewMemberChangesTask(string taskId, TaskMemberChangesReviewResource taskChangesReviewResource)
        {
            var contractResolver = new DefaultContractResolver
            {
                NamingStrategy = new CamelCaseNamingStrategy()
            };

            var task = GetTask(taskId);
            var variables = GetVariables(task.ProcessInstanceId);

            if (variables.Count > 0)
            {
                var taskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                        {
                            {
                                "variables",
                                new Dictionary<string, Dictionary<string, string>>
                                {
                                    {
                                        "memberChangesDecision",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", taskChangesReviewResource.ReviewDecision },
                                            { "type", "String" }
                                        }
                                    }
                                }
                            }
                        };

                var memberVariables = variables.FirstOrDefault(i => i.Name == "ChangeRequestId");
                var requestId = (memberVariables != null) ? memberVariables.Value : "0";
                int memberId = 0;

                if (!string.IsNullOrEmpty(requestId))
                {
                    var changeRequest = GetMemberChangeRequest(Convert.ToInt32(requestId));
                    if (changeRequest != null)
                    {
                        memberId = changeRequest.ObjectId;

                        var member = await _dbContext.Members
                            .AsNoTracking()
                            .FirstOrDefaultAsync(i => i.Id == memberId);

                        if (taskChangesReviewResource.ReviewDecision.ToLower() == "accepted")
                        {
                            if (member != null)
                            {
                                if (changeRequest != null)
                                {
                                    changeRequest.Status = ChangeRequestStatus.Accepted;
                                    changeRequest.ReviewComments = (!string.IsNullOrEmpty(taskChangesReviewResource.ReviewComments)) ? taskChangesReviewResource.ReviewComments : null;
                                    _dbContext.Set<ChangeRequestStaging>().Update(changeRequest);
                                }

                                var modelForUpdate = JsonConvert.DeserializeObject<MemberUpdateAllTypesResource>(changeRequest.UpdatedDetailsBlob);
                                _memberExtensions.ApplyMemberChanges(_dbContext, member, _mapper, modelForUpdate);

                                NotifyApplicantOfMemberUpdateAccepted(member.Id);
                            }
                        }

                        else if (taskChangesReviewResource.ReviewDecision.ToLower() == "rejected")
                        {
                            NotifyApplicantOfMemberUpdateDecline(member.Id);
                        }

                        _dbContext.Remove(changeRequest);
                    }
                }

                _dbContext.SaveChanges();

                using (var client = new HttpClient())
                {
                    var json = JsonConvert.SerializeObject(taskVariables, new JsonSerializerSettings
                    {
                        Formatting = Formatting.Indented
                    });
                    var content = new StringContent(json, Encoding.UTF8, "application/json");
                    var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                    var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });
                    result.EnsureSuccessStatusCode();
                }
            }
        }

        public void CompleteReviewSRNChangesTask(string taskId, TaskMemberChangesReviewResource taskChangesReviewResource)
        {
            var contractResolver = new DefaultContractResolver
            {
                NamingStrategy = new CamelCaseNamingStrategy()
            };

            var task = GetTask(taskId);
            var variables = GetVariables(task.ProcessInstanceId);

            if (variables.Count > 0)
            {
                var taskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                        {
                            {
                                "variables",
                                new Dictionary<string, Dictionary<string, string>>
                                {
                                    {
                                        "srnChangesDecision",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", taskChangesReviewResource.ReviewDecision },
                                            { "type", "String" }
                                        }
                                    }
                                }
                            }
                        };

                var srnVariables = variables.FirstOrDefault(i => i.Name == "ChangeRequestId");
                var requestId = (srnVariables != null) ? srnVariables.Value : "0";

                if (!string.IsNullOrEmpty(requestId))
                {
                    var changeRequest = GetMemberChangeRequest(Convert.ToInt32(requestId));
                    if (changeRequest != null)
                    {
                        if (taskChangesReviewResource.ReviewDecision.ToLower() == "accepted")
                        {
                            changeRequest.Status = ChangeRequestStatus.Accepted;
                            changeRequest.ReviewComments = (!string.IsNullOrEmpty(taskChangesReviewResource.ReviewComments)) ? taskChangesReviewResource.ReviewComments : null;
                            _dbContext.Set<ChangeRequestStaging>().Update(changeRequest);
                        }

                        _dbContext.SaveChanges();
                    }
                }

                using (var client = new HttpClient())
                {
                    var json = JsonConvert.SerializeObject(taskVariables, new JsonSerializerSettings
                    {
                        Formatting = Formatting.Indented
                    });
                    var content = new StringContent(json, Encoding.UTF8, "application/json");
                    var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                    var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });
                    result.EnsureSuccessStatusCode();
                }
            }
        }

        public void CompleteFinancialAdminCheckAssessmentInvoicePaymentTask(string taskId, TaskUpdateFinancialAdminResource taskUpdateResource)
        {
            var contractResolver = new DefaultContractResolver
            {
                NamingStrategy = new CamelCaseNamingStrategy()
            };

            var task = GetTask(taskId);
            var variables = GetVariables(task.ProcessInstanceId);

            var memberId = variables.FirstOrDefault(i => i.Name == "OrganisationID").Value;

            var member = await _dbContext.Members
            .AsNoTracking()
            .FirstOrDefaultAsync(i => i.Id == Convert.ToInt32(memberId));

            var taskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                        {
                            {
                                "variables",
                                new Dictionary<string, Dictionary<string, string>>
                                {
                                    {
                                        "initialAssessmentInvoicePaid",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", taskUpdateResource.PaymentRecieved },
                                            { "type", "String" }
                                        }
                                    }
                                }
                            }
                        };

            if (taskUpdateResource.PaymentRecieved == "yes")
            {
                member.ApplicationStatusId = ApplicationStatuses.MemberRegistrationSHMFinalReview;
            }

            else if (taskUpdateResource.PaymentRecieved == "no")
            {
                member.ApplicationStatusId = ApplicationStatuses.MemberRegistrationCancelled_InitialAssessmentInvoiceNotPaid;
                member.DisqualificationReason = "Initial assessment invoice not paid";
            }

            _dbContext.Set<Member>().Update(member);
            _dbContext.SaveChanges();

            if (taskUpdateResource.PaymentRecieved == "no")
            {
                SendMemberApplicationCancellationEmail(member.Id);
            }

            using (var client = new HttpClient())
            {
                var json = JsonConvert.SerializeObject(taskVariables, new JsonSerializerSettings
                {
                    ContractResolver = contractResolver,
                    Formatting = Formatting.Indented
                });
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                //  send a POST request
                var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });

                // on error throw a exception
                result.EnsureSuccessStatusCode();
            }
        }

        public void CompleteFinancialAdminCheckOnboardingInvoicePaymentTask(string taskId, TaskUpdateFinancialAdminResource taskUpdateResource)
        {
            var contractResolver = new DefaultContractResolver
            {
                NamingStrategy = new CamelCaseNamingStrategy()
            };

            var task = GetTask(taskId);
            var variables = GetVariables(task.ProcessInstanceId);

            var memberId = variables.FirstOrDefault(i => i.Name == "OrganisationID").Value;

            var member = await _dbContext.Members
            .AsNoTracking()
            .FirstOrDefaultAsync(i => i.Id == Convert.ToInt32(memberId));

            var taskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                        {
                            {
                                "variables",
                                new Dictionary<string, Dictionary<string, string>>
                                {
                                    {
                                        "onboardingPaymentReceived",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", taskUpdateResource.PaymentRecieved },
                                            { "type", "String" }
                                        }
                                    }
                                }
                            }
                        };

            if (taskUpdateResource.PaymentRecieved == "yes")
            {
                member.ApplicationStatusId = ApplicationStatuses.MemberRegistrationSHMFinalReview;
            }

            else if (taskUpdateResource.PaymentRecieved == "no")
            {
                member.ApplicationStatusId = ApplicationStatuses.MemberRegistrationCancelled_InitialAssessmentInvoiceNotPaid;
                member.DisqualificationReason = "Onboarding invoice not paid";
            }

            _dbContext.Set<Member>().Update(member);
            _dbContext.SaveChanges();

            if (taskUpdateResource.PaymentRecieved == "no")
            {
                SendMemberApplicationCancellationEmail(member.Id);
            }

            using (var client = new HttpClient())
            {
                var json = JsonConvert.SerializeObject(taskVariables, new JsonSerializerSettings
                {
                    ContractResolver = contractResolver,
                    Formatting = Formatting.Indented
                });
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                //  send a POST request
                var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });

                // on error throw a exception
                result.EnsureSuccessStatusCode();
            }
        }

        public void StartMemberUpdateWorkflow(Member member, bool isApprovalRequired, int changeRequestId)
        {
            if (member != null)
            {
                string requireApproval = isApprovalRequired ? "yes" : "no";

                _camundaClient.ProcessDefinitions.ByKey("Member-Update-Details").StartProcessInstance(new StartProcessInstance()
                {
                    Variables = new Dictionary<string, VariableValue>()
                            {
                                { "MemberId", VariableValue.FromObject(member.Id) },
                                { "requireApproval", VariableValue.FromObject(requireApproval) },
                                { "ChangeRequestId", VariableValue.FromObject(changeRequestId) }
                            }
                });
            }
        }

        public void NotifyApplicantOfMemberUpdateDecline(int memberId)
        {
            try
            {
                var member = await _dbContext.Members
                    .Include(i => i.Contacts)
                    .AsNoTracking()
                    .FirstOrDefaultAsync(i => i.Id == memberId);

                var mainContactType = await _dbContext.ContactTypes
                                    .AsNoTracking()
                                    .FirstOrDefaultAsync(i => i.Name == "Main Contact Details");

                if (member != null)
                {
                    if (member.MembershipTypeId == MembershipTypes.ALGClient)
                    {
                        var algLeaders = await _dbContext.ALGClientLeaders
                            .Include(i => i.Leader)
                                .ThenInclude(i => i.Contacts)
                            .Where(i => i.ClientId == memberId)
                            .AsNoTracking()
                            .ToListAsync();

                        foreach (var leader in algLeaders)
                        {
                            if (leader.Leader.Contacts.Count > 0)
                            {
                                var mainContact = leader.Leader
                                    .Contacts
                                    .FirstOrDefault(i => i.ContactTypeId == mainContactType.Id);

                                if (mainContact != null)
                                {
                                    var placeholders = new List<KeyValuePair<string, string>>
                                    {
                                        new KeyValuePair<string, string>("[MemberRegisteredName]", member.RegisteredName)
                                    };

                                    _emailService.SendEmail(mainContact.Email, mainContact.FirstName, "Member Details Update Declined", "MemberUpdateDeclinedApplicant.html", placeholders);
                                }
                            }
                        }
                    }
                    else
                    {
                        if (member.Contacts.Count > 0)
                        {
                            var mainContact = member.Contacts
                                    .FirstOrDefault(i => i.ContactTypeId == mainContactType.Id);

                            if (mainContact != null)
                            {
                                var placeholders = new List<KeyValuePair<string, string>>
                                {
                                    new KeyValuePair<string, string>("[MemberRegisteredName]", member.RegisteredName)
                                };

                                _emailService.SendEmail(mainContact.Email, mainContact.FirstName, "Member Details Update Declined", "MemberUpdateDeclinedApplicant.html", placeholders);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                var message = "Unable to email applicant for member details update rejection. Member Id " + memberId;
                Helpers.Helpers.LogError(_dbContext, ex, message);
                throw new Exception(message);
            }
        }

        public void NotifyApplicantOfMemberUpdateAccepted(int memberId)
        {
            try
            {
                var member = await _dbContext.Members
                    .Include(i => i.Contacts)
                    .AsNoTracking()
                    .FirstOrDefaultAsync(i => i.Id == memberId);

                var mainContactType = await _dbContext.ContactTypes
                                    .AsNoTracking()
                                    .FirstOrDefaultAsync(i => i.Name == "Main Contact Details");

                if (member != null)
                {
                    if (member.MembershipTypeId == MembershipTypes.ALGClient)
                    {
                        var algLeaders = await _dbContext.ALGClientLeaders
                            .Include(i => i.Leader)
                                .ThenInclude(i => i.Contacts)
                            .Where(i => i.ClientId == memberId)
                            .AsNoTracking()
                            .ToListAsync();

                        foreach (var leader in algLeaders)
                        {
                            if (leader.Leader.Contacts.Count > 0)
                            {
                                var mainContact = leader.Leader
                                    .Contacts
                                    .FirstOrDefault(i => i.ContactTypeId == mainContactType.Id);

                                if (mainContact != null)
                                {
                                    var placeholders = new List<KeyValuePair<string, string>>
                                    {
                                        new KeyValuePair<string, string>("[MemberRegisteredName]", member.RegisteredName)
                                    };

                                    _emailService.SendEmail(mainContact.Email, mainContact.FirstName, "Member Details Update Accepted", "MemberUpdateAcceptedApplicant.html", placeholders);
                                }
                            }
                        }
                    }
                    else
                    {
                        if (member.Contacts.Count > 0)
                        {
                            var mainContact = member.Contacts
                                    .FirstOrDefault(i => i.ContactTypeId == mainContactType.Id);

                            if (mainContact != null)
                            {
                                var placeholders = new List<KeyValuePair<string, string>>
                                {
                                    new KeyValuePair<string, string>("[MemberRegisteredName]", member.RegisteredName)
                                };

                                _emailService.SendEmail(mainContact.Email, mainContact.FirstName, "Member Details Update Accepted", "MemberUpdateAcceptedApplicant.html", placeholders);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                var message = "Unable to email applicant for member details update accepted. Member Id " + memberId;
                Helpers.Helpers.LogError(_dbContext, ex, message);
                throw new Exception(message);
            }
        }

        public void CompleteSellerStakeHolderManagerSRNReviewSRNSale(string taskId, TaskSellerSHMReviewSRNSale taskReview)
        {
            using (var client = new HttpClient())
            {
                var task = GetTask(taskId);
                var currentTaskVariables = GetVariables(task.ProcessInstanceId);
                var buyerSHMId = "";

                if (taskReview != null)
                {
                    if (taskReview.ReviewSaleDecision == SellerSRNSaleReviewDecision.Approved)
                    {
                        if (taskReview.BuyerMemberId > 0)
                        {
                            var member = await _dbContext.Members
                                .AsNoTracking()
                                .FirstOrDefaultAsync(i => i.Id == taskReview.BuyerMemberId);

                            if (member != null)
                            {
                                buyerSHMId = (member.StakeholderManagerId > 0) ? member.StakeholderManagerId.ToString() : "";
                            }
                        }
                    }
                    else
                    {
                        var shmVariable = currentTaskVariables.FirstOrDefault(i => i.Name == "stakeHolderManagerAssignee");

                        if (shmVariable != null)
                        {
                            buyerSHMId = shmVariable.Value;
                        }
                    }
                }

                if (currentTaskVariables.Count > 0)
                {
                    var saleRequestVariable = currentTaskVariables.FirstOrDefault(i => i.Name == "SRNSaleRequestId");
                    var requestId = 0;

                    if (saleRequestVariable != null)
                    {
                        requestId = Convert.ToInt32(saleRequestVariable.Value);
                    }

                    if (requestId > 0)
                    {
                        var existingSRNSale = await _dbContext.SRNSaleRequests
                                .AsNoTracking()
                                .FirstOrDefaultAsync(i => i.Id == requestId);

                        if (existingSRNSale != null)
                        {
                            existingSRNSale.BuyerMemberId = (taskReview.BuyerMemberId > 0) ? taskReview.BuyerMemberId : existingSRNSale.BuyerMemberId;

                            switch (taskReview.ReviewSaleDecision)
                            {
                                case SellerSRNSaleReviewDecision.Approved:
                                    existingSRNSale.Status = SRNSaleStatus.SellerSHMApproved;
                                    break;

                                case SellerSRNSaleReviewDecision.Pending:
                                    existingSRNSale.Status = SRNSaleStatus.Requested;
                                    break;

                                case SellerSRNSaleReviewDecision.Cancel:
                                    existingSRNSale.Status = SRNSaleStatus.Cancelled;
                                    //Change status to it's initial state

                                    var initialStatusIdVariable = currentTaskVariables.FirstOrDefault(i => i.Name == "InitialStatusId");
                                    var initialStatusId = 0;

                                    if (initialStatusIdVariable != null)
                                    {
                                        initialStatusId = Convert.ToInt32(initialStatusIdVariable.Value);
                                    }

                                    if (initialStatusId > 0)
                                    {
                                        var srn = await _dbContext.SRNs
                                            .AsNoTracking()
                                            .FirstOrDefaultAsync(i => i.Id == existingSRNSale.SRNId);

                                        srn.SRNStatusId = initialStatusId;
                                        srn.StatusLastUpdatedAt = DateTime.Now;

                                        _dbContext.Update(srn);
                                        _dbContext.SaveChanges();
                                    }
                                    break;

                                case SellerSRNSaleReviewDecision.UnconfirmedCreate:
                                    existingSRNSale.Status = SRNSaleStatus.Requested;
                                    break;

                                case SellerSRNSaleReviewDecision.FinaliseUnconfirmedSale:
                                    existingSRNSale.Status = SRNSaleStatus.Sold; //sell it even if the buyer is not confirmed

                                    var status = await _dbContext.SRNStatuses
                                        .AsNoTracking()
                                        .FirstOrDefaultAsync(i => i.Name == "Sold");

                                    if (status != null)
                                    {
                                        var srn = await _dbContext.SRNs
                                            .AsNoTracking()
                                            .FirstOrDefaultAsync(i => i.Id == existingSRNSale.SRNId);

                                        srn.SRNStatusId = status.Id;
                                        srn.StatusLastUpdatedAt = DateTime.Now;

                                        _dbContext.Update(srn);
                                        _dbContext.SaveChanges();
                                    }

                                    break;
                            }

                            existingSRNSale.ReviewComments = (!string.IsNullOrEmpty(taskReview.ReviewComments)) ? taskReview.ReviewComments : null;
                            _dbContext.Update(existingSRNSale);
                            _dbContext.SaveChanges();
                        }
                    }
                }

                var newTaskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                    {
                        {
                            "variables",
                            new Dictionary<string, Dictionary<string, string>>
                            {
                                {
                                    "reviewsale",
                                    new Dictionary<string, string>()
                                    {
                                        { "value", taskReview.ReviewSaleDecision.ToString() },
                                        { "type", "String" }
                                    }
                                },
                                {
                                    "BuyerMemberId",
                                    new Dictionary<string, string>()
                                    {
                                        { "value", taskReview.BuyerMemberId.ToString() },
                                        { "type", "Long" }
                                    }
                                },
                                {
                                    "stakeHolderManagerAssignee",
                                    new Dictionary<string, string>()
                                    {
                                        { "value", buyerSHMId },
                                        { "type", "String" }
                                    }
                                },
                                {
                                    "ReviewCommentsSeller",
                                    new Dictionary<string, string>()
                                    {
                                        { "value", taskReview.ReviewComments },
                                        { "type", "String" }
                                    }
                                }
                            }
                        }
                    };

                var json = JsonConvert.SerializeObject(newTaskVariables);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });
                result.EnsureSuccessStatusCode();
            }
        }

        public void CompleteBuyerStakeHolderManagerSRNReviewSRNSale(string taskId, TaskBuyerSHMReviewSRNSale taskReview)
        {
            using (var client = new HttpClient())
            {
                var task = GetTask(taskId);
                var currentTaskVariables = GetVariables(task.ProcessInstanceId);
                var compatibleSRNExists = "";
                var isTestingOrMigrationRequired = "";
                var buyerSRNId = "";
                var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext);

                if (taskReview != null)
                {
                    switch (taskReview.CompatibleSRNOptions)
                    {
                        case CompatibleSRNOptions.Approve:
                            compatibleSRNExists = "yes";
                            break;

                        case CompatibleSRNOptions.RequestNewSRN:
                            compatibleSRNExists = "pending";
                            break;

                        case CompatibleSRNOptions.Cancel:
                            compatibleSRNExists = "cancel";
                            break;
                    }

                    if (taskReview.IsTestingOrMigrationRequired != null)
                    {
                        isTestingOrMigrationRequired = (bool)taskReview.IsTestingOrMigrationRequired ? "yes" : "no";
                    }
                    buyerSRNId = (taskReview.BuyerSRNId > 0) ? taskReview.BuyerSRNId.ToString() : "";
                }

                var saleRequestVariable = currentTaskVariables.FirstOrDefault(i => i.Name == "SRNSaleRequestId");

                var saleRequestId = 0;
                if (saleRequestVariable != null)
                    saleRequestId = Convert.ToInt32(saleRequestVariable.Value);

                if (saleRequestId > 0)
                {
                    var saleRequest = await _dbContext.SRNSaleRequests
                        .AsNoTracking()
                        .FirstOrDefaultAsync(i => i.Id == saleRequestId);

                    if (saleRequest != null)
                    {
                        if (string.IsNullOrEmpty(buyerSRNId) && taskReview.CompatibleSRNOptions == CompatibleSRNOptions.Approve)
                        {
                            if (taskReview.IsApprovedWithNoCampatibleSRN == null)
                                throw new Exception("Compatible SRN exists but no compatible buyer SRN specified");
                            if (taskReview.IsApprovedWithNoCampatibleSRN != null)
                            {
                                if (!(bool)taskReview.IsApprovedWithNoCampatibleSRN)
                                    throw new Exception("Compatible SRN exists but no compatible buyer SRN specified");
                            }
                        }

                        var originalSRN = await _dbContext.SRNs
                            .Include(i => i.SRNStatus)
                            .AsNoTracking()
                            .FirstOrDefaultAsync(i => i.Id == saleRequest.SRNId);

                        taskReview.BuyerSRNId = (taskReview.BuyerSRNId != null) ? taskReview.BuyerSRNId : 0;

                        /* SA-824
                         * If the whole SRN sale request has been cancelled,
                         * cancel the workflow task and update the SRN status back to its original state
                        */
                        if (taskReview.CompatibleSRNOptions == CompatibleSRNOptions.Cancel)
                        {
                            compatibleSRNExists = "cancel";
                            UpdateSRNStatusToInitialState(currentTaskVariables, "SRNIdToBeSold", "SRN Sale");
                        }

                        /*
                         * When you do a full SRN Sale, and you select a compatible SRN on the buyer side,
                         * then the SRN being sold needs to be deactivated completely
                         */
                        else if (saleRequest.Type == SRNSaleType.Full && taskReview.CompatibleSRNExists && taskReview.BuyerSRNId > 0 && taskReview.CompatibleSRNOptions == CompatibleSRNOptions.Approve)
                        {
                            compatibleSRNExists = "yes";
                            var status = await _dbContext.SRNStatuses
                                .AsNoTracking()
                                .FirstOrDefaultAsync(i => i.Name == "Sold");

                            if (status != null)
                            {
                                var oldStatus = originalSRN.SRNStatus.Name;

                                originalSRN.SRNStatusId = status.Id;
                                originalSRN.StatusLastUpdatedAt = DateTime.Now;

                                Helpers.Helpers.PrepareSRNForUpdate(_dbContext, originalSRN);

                                _dbContext.Update(originalSRN);
                                _dbContext.SaveChanges();

                                Helpers.Helpers.CreateSRNStatusEventLog(_dbContext, _mapper, oldStatus, status.Name, "SRN Sale", originalSRN, user);
                            }
                        }
                        /*
                         * If you do a full sale and you do not select a compatible SRN (Meaning a compatible SRN does not exist),
                         * then the SRN being sold needs to be transferred to the buyer member.
                         */
                        else if (saleRequest.Type == SRNSaleType.Full && (!taskReview.CompatibleSRNExists || taskReview.BuyerSRNId <= 0) && taskReview.CompatibleSRNOptions == CompatibleSRNOptions.Approve)
                        {
                            if (taskReview.IsApprovedWithNoCampatibleSRN == null)
                                throw new Exception("The whole SRN needs to be transfered if there is no compatible SRN selected");
                            else if (taskReview.IsApprovedWithNoCampatibleSRN == false)
                                throw new Exception("The whole SRN needs to be transfered if there is no compatible SRN selected");

                            compatibleSRNExists = "yes";

                            UpdateSRNStatusToInitialState(currentTaskVariables, "SRNIdToBeSold", "SRN Sale");
                        }

                        /*
                         * If you do a partial sale, then the SRN being sold will not change (it will not be transferred either).
                           However; if Migration or Testing is required the Buyer SRN's status will be updated*/
                        else if (saleRequest.Type == SRNSaleType.Partial && taskReview.CompatibleSRNOptions == CompatibleSRNOptions.Approve)
                        {
                            if (taskReview.CompatibleSRNExists && taskReview.BuyerSRNId > 0)
                            {
                                compatibleSRNExists = "yes";
                                UpdateSRNStatusToInitialState(currentTaskVariables, "SRNIdToBeSold", "SRN Sale");

                                //Create event log
                                var stagingChangeLog = new MemberStagingChangeLogResource();
                                var changeType = "SRN Sale";

                                var buyerSRN = await _dbContext.Set<SRN>()
                                    .Include(i => i.SRNStatus)
                                    .FirstOrDefaultAsync(i => i.Id == Convert.ToInt32(buyerSRNId));
                                var assigneeVariable = currentTaskVariables.FirstOrDefault(i => i.Name == "stakeHolderManagerAssignee");
                                var userId = 0;

                                if (assigneeVariable != null)
                                    userId = Convert.ToInt32(assigneeVariable.Value);

                                UpdateSRNDates(buyerSRN, taskReview, stagingChangeLog);

                                var changeBlob = JsonConvert.SerializeObject(stagingChangeLog);
                                _dbContext.SaveChanges();

                                var srnUpdateResource = _mapper.Map<SRNUpdateResource>(buyerSRN);
                                var entityBlob = JsonConvert.SerializeObject(srnUpdateResource);

                                Helpers.Helpers.CreateEventLog(_dbContext, user.Id, changeType, buyerSRN.TradingName, entityBlob, changeBlob, buyerSRN.Id, "SRN");

                            }
                        }
                        /*
                         * If there's no compatible SRN selected and a new SRN has been requested, put the request on pending
                         */
                        else if (!taskReview.CompatibleSRNExists && taskReview.CompatibleSRNOptions == CompatibleSRNOptions.RequestNewSRN)
                        {
                            compatibleSRNExists = "pending";
                        }

                        saleRequest.BuyerSRNId = (!string.IsNullOrEmpty(buyerSRNId)) ? Convert.ToInt32(buyerSRNId) : 0;
                        saleRequest.BuyerSRNId = (saleRequest.BuyerSRNId > 0) ? saleRequest.BuyerSRNId : null;

                        if (taskReview.CompatibleSRNOptions == CompatibleSRNOptions.Approve)
                            saleRequest.Status = SRNSaleStatus.BuyerSHMApproved;
                        else if (taskReview.CompatibleSRNOptions == CompatibleSRNOptions.RequestNewSRN)
                            saleRequest.Status = SRNSaleStatus.Requested;
                        else if (taskReview.CompatibleSRNOptions == CompatibleSRNOptions.Cancel)
                            saleRequest.Status = SRNSaleStatus.Cancelled;

                        saleRequest.DailyFileDevelopmentStartDate = taskReview.DailyFileDevelopmentStartDate;
                        saleRequest.DailyFileDevelopmentEndDate = taskReview.DailyFileDevelopmentEndDate;
                        saleRequest.DailyFileTestStartDate = taskReview.DailyFileTestStartDate;
                        saleRequest.DailyFileTestEndDate = taskReview.DailyFileTestEndDate;
                        saleRequest.DailyFileGoLiveDate = taskReview.DailyFileGoLiveDate;

                        saleRequest.MonthlyFileDevelopmentStartDate = taskReview.MonthlyFileDevelopmentStartDate;
                        saleRequest.MonthlyFileDevelopmentEndDate = taskReview.MonthlyFileDevelopmentEndDate;
                        saleRequest.MonthlyFileTestStartDate = taskReview.MonthlyFileTestStartDate;
                        saleRequest.MonthlyFileTestEndDate = taskReview.MonthlyFileTestEndDate;
                        saleRequest.MonthlyFileGoLiveDate = taskReview.MonthlyFileGoLiveDate;

                        saleRequest.MigrationDate = taskReview.MigrationDate;
                        saleRequest.SPGroupId = (taskReview.SPGroupId == 0) ? null : taskReview.SPGroupId;

                        saleRequest.ReviewComments = (!string.IsNullOrEmpty(taskReview.ReviewComments)) ? taskReview.ReviewComments : null;
                        _dbContext.Update(saleRequest);
                        _dbContext.SaveChanges();
                    }
                }

                var newTaskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                    {
                        {
                            "variables",
                            new Dictionary<string, Dictionary<string, string>>
                            {
                                {
                                    "CompatibleSRNExists",
                                    new Dictionary<string, string>()
                                    {
                                        { "value", compatibleSRNExists },
                                        { "type", "String" }
                                    }
                                },
                                {
                                    "IsTestingMigrationRequired",
                                    new Dictionary<string, string>()
                                    {
                                        { "value", isTestingOrMigrationRequired },
                                        { "type", "String" }
                                    }
                                },
                                {
                                    "BuyerSRNId",
                                    new Dictionary<string, string>()
                                    {
                                        { "value", buyerSRNId },
                                        { "type", "String" }
                                    }
                                },
                                {
                                    "ReviewCommentsBuyer",
                                    new Dictionary<string, string>()
                                    {
                                        { "value", taskReview.ReviewComments },
                                        { "type", "String" }
                                    }
                                }
                            }
                        }
                    };

                var json = JsonConvert.SerializeObject(newTaskVariables);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });
                result.EnsureSuccessStatusCode();


            }
        }

        public void CompleteBuyerStakeHolderManagerConfirmSRNTestingAndMigration(string taskId, TaskBuyerSHMConfirmSRNTestingMigrationResource taskReview)
        {
            using (var client = new HttpClient())
            {
                var isTestingOrMigrationConfirmed = "";

                if (taskReview != null)
                {
                    isTestingOrMigrationConfirmed = taskReview.IsTestingOrMigrationConfirmed ? "yes" : "no";
                }

                var newTaskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                    {
                        {
                            "variables",
                            new Dictionary<string, Dictionary<string, string>>
                            {
                                {
                                    "IsTestingMigrationConfirmed",
                                    new Dictionary<string, string>()
                                    {
                                        { "value", isTestingOrMigrationConfirmed },
                                        { "type", "String" }
                                    }
                                }
                            }
                        }
                    };

                var json = JsonConvert.SerializeObject(newTaskVariables);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });
                result.EnsureSuccessStatusCode();
            }
        }
        public void CompleteBuyerStakeHolderManagerSRNApplication_ConfirmSRNTesting(string taskId, TaskConfirmSRNTestingCompleteResource confirmation)
        {
            using (var client = new HttpClient())
            {
                var isConfirmed = "";

                if (confirmation.IsTestingComplete == ConfirmSRNTestingCompleteEnum.Yes)
                    isConfirmed = "yes";
                else if (confirmation.IsTestingComplete == ConfirmSRNTestingCompleteEnum.No)
                    isConfirmed = "no";
                else if (confirmation.IsTestingComplete == ConfirmSRNTestingCompleteEnum.Cancel)
                    isConfirmed = "cancel";
                else
                    isConfirmed = "no";

                var task = GetTask(taskId);
                var currentTaskVariables = GetVariables(task.ProcessInstanceId);
                var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext);
                var srnIDVariable = currentTaskVariables.FirstOrDefault(i => i.Name == "SRNId");
                var srnId = 0;

                if (srnIDVariable != null)
                    srnId = Convert.ToInt32(srnIDVariable.Value);

                if (srnIDVariable != null)
                    srnId = Convert.ToInt32(srnIDVariable.Value);

                SRN srn = null;
                var recentSRNStatusUpdate = new SRNStatusUpdateHistory();


                if (srnId > 0)
                {
                    srn = await _dbContext.Set<SRN>()
                       .Include(i => i.SRNStatusUpdates)
                       .FirstOrDefaultAsync(i => i.Id == srnId);
                    recentSRNStatusUpdate = srn.SRNStatusUpdates.FirstOrDefault(i => i.ProcessInstanceId == task.ProcessInstanceId);
                }

                var developmentStartDate = "";
                var developmentEndDate = "";
                var testStartDate = "";
                var testEndDate = "";
                var goLiveDate = "";


                if (confirmation.TestEndDate != null)
                {
                    if (confirmation.TestEndDate > DateTime.Now.Date)
                        testEndDate = confirmation.TestEndDate.Value.AddDays(-3).ToString("yyyy-MM-ddTh:mm:ssZ");
                    else
                        testEndDate = (confirmation.TestEndDate != null && confirmation.TestEndDate != DateTime.MinValue) ? confirmation.TestEndDate.Value.ToString("yyyy-MM-ddTh:mm:ssZ") : "";
                }
                else
                {
                    if (recentSRNStatusUpdate != null)
                    {
                        if (recentSRNStatusUpdate.FileType == SRNStatusFileTypes.DailyFile)
                            testEndDate = recentSRNStatusUpdate.DailyFileTestEndDate.Value.ToString("yyyy-MM-ddTh:mm:ssZ");
                        else if (recentSRNStatusUpdate.FileType == SRNStatusFileTypes.MonthlyFile)
                            testEndDate = recentSRNStatusUpdate.MonthlyFileTestEndDate.Value.ToString("yyyy-MM-ddTh:mm:ssZ");
                    }
                }

                if (confirmation.GoLiveDate != null)
                {
                    if (confirmation.GoLiveDate > DateTime.Now.Date)
                        goLiveDate = confirmation.GoLiveDate.Value.AddDays(-3).ToString("yyyy-MM-ddTh:mm:ssZ");
                    else
                        goLiveDate = (confirmation.GoLiveDate != null && confirmation.GoLiveDate != DateTime.MinValue) ? confirmation.GoLiveDate.Value.ToString("yyyy-MM-ddTh:mm:ssZ") : "";
                }
                else
                {
                    if (recentSRNStatusUpdate != null)
                    {
                        if (recentSRNStatusUpdate.FileType == SRNStatusFileTypes.DailyFile)
                            goLiveDate = recentSRNStatusUpdate.DailyFileGoLiveDate.Value.ToString("yyyy-MM-ddTh:mm:ssZ");
                        else if (recentSRNStatusUpdate.FileType == SRNStatusFileTypes.MonthlyFile)
                            goLiveDate = recentSRNStatusUpdate.MonthlyFileGoLiveDate.Value.ToString("yyyy-MM-ddTh:mm:ssZ");
                    }
                }

                var newTaskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                    {
                        {
                            "variables",
                            new Dictionary<string, Dictionary<string, string>>
                            {
                                {
                                    "IsTestingMigrationConfirmed",
                                    new Dictionary<string, string>()
                                    {
                                        { "value", isConfirmed },
                                        { "type", "String" }
                                    }
                                },
                                {
                                    "testEndDate",
                                    new Dictionary<string, string>()
                                    {
                                        { "value", testEndDate },
                                        { "type", "String" }
                                    }
                                },
                                {
                                    "goLiveDate",
                                    new Dictionary<string, string>()
                                    {
                                        { "value", goLiveDate },
                                        { "type", "String" }
                                    }
                                }
                            }
                        }
                    };

                var json = JsonConvert.SerializeObject(newTaskVariables);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var uri = _configSettings.CamundaBaseAddress + "/task/" + task.Id + "/complete";
                var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });
                result.EnsureSuccessStatusCode();

                if (srn != null)
                {
                    var stagingChangeLog = new MemberStagingChangeLogResource();

                    if (confirmation.DevelopmentStartDate != null && (recentSRNStatusUpdate.FileType == SRNStatusFileTypes.DailyFile || recentSRNStatusUpdate.FileType == SRNStatusFileTypes.MonthlyAndDailyFile))
                    {

                        var stagingChange = new StagingChange
                        {
                            Name = "Daily File Development Start Date",
                            OldValue = string.Format("{0:yyyy-MM-dd}", recentSRNStatusUpdate.DailyFileDevelopmentStartDate.Value),
                            NewValue = string.Format("{0:yyyy-MM-dd}", confirmation.DevelopmentStartDate.Value)
                        };

                        stagingChangeLog.Changes.Add(stagingChange);
                        recentSRNStatusUpdate.DailyFileDevelopmentStartDate = confirmation.DevelopmentStartDate.Value;
                    }

                    if (confirmation.DevelopmentStartDate != null && (recentSRNStatusUpdate.FileType == SRNStatusFileTypes.MonthlyFile || recentSRNStatusUpdate.FileType == SRNStatusFileTypes.MonthlyAndDailyFile))
                    {
                        var stagingChange = new StagingChange
                        {
                            Name = "Monthly File Development Start Date",
                            OldValue = string.Format("{0:yyyy-MM-dd}", recentSRNStatusUpdate.MonthlyFileDevelopmentStartDate.Value),
                            NewValue = string.Format("{0:yyyy-MM-dd}", confirmation.DevelopmentStartDate.Value)
                        };

                        stagingChangeLog.Changes.Add(stagingChange);
                        recentSRNStatusUpdate.MonthlyFileDevelopmentStartDate = confirmation.DevelopmentStartDate.Value;
                    }

                    if (confirmation.DevelopmentEndDate != null && (recentSRNStatusUpdate.FileType == SRNStatusFileTypes.DailyFile || recentSRNStatusUpdate.FileType == SRNStatusFileTypes.MonthlyAndDailyFile))
                    {

                        var stagingChange = new StagingChange
                        {
                            Name = "Daily File Development End Date",
                            OldValue = string.Format("{0:yyyy-MM-dd}", recentSRNStatusUpdate.DailyFileDevelopmentEndDate.Value),
                            NewValue = string.Format("{0:yyyy-MM-dd}", confirmation.DevelopmentEndDate.Value)
                        };

                        stagingChangeLog.Changes.Add(stagingChange);
                        recentSRNStatusUpdate.DailyFileDevelopmentEndDate = confirmation.DevelopmentEndDate.Value;
                    }

                    if (confirmation.DevelopmentEndDate != null && (recentSRNStatusUpdate.FileType == SRNStatusFileTypes.MonthlyFile || recentSRNStatusUpdate.FileType == SRNStatusFileTypes.MonthlyAndDailyFile))
                    {
                        var stagingChange = new StagingChange
                        {
                            Name = "Monthly File Development End Date",
                            OldValue = string.Format("{0:yyyy-MM-dd}", recentSRNStatusUpdate.MonthlyFileDevelopmentEndDate.Value),
                            NewValue = string.Format("{0:yyyy-MM-dd}", confirmation.DevelopmentEndDate.Value)
                        };

                        stagingChangeLog.Changes.Add(stagingChange);
                        recentSRNStatusUpdate.MonthlyFileDevelopmentEndDate = confirmation.DevelopmentEndDate.Value;
                    }

                    if (confirmation.TestStartDate != null && (recentSRNStatusUpdate.FileType == SRNStatusFileTypes.DailyFile || recentSRNStatusUpdate.FileType == SRNStatusFileTypes.MonthlyAndDailyFile))
                    {

                        var stagingChange = new StagingChange
                        {
                            Name = "Daily File Test Start Date",
                            OldValue = string.Format("{0:yyyy-MM-dd}", recentSRNStatusUpdate.DailyFileTestStartDate.Value),
                            NewValue = string.Format("{0:yyyy-MM-dd}", confirmation.TestStartDate.Value)
                        };

                        stagingChangeLog.Changes.Add(stagingChange);
                        recentSRNStatusUpdate.DailyFileTestStartDate = confirmation.TestStartDate.Value;
                    }

                    if (confirmation.TestStartDate != null && (recentSRNStatusUpdate.FileType == SRNStatusFileTypes.MonthlyFile || recentSRNStatusUpdate.FileType == SRNStatusFileTypes.MonthlyAndDailyFile))
                    {
                        var stagingChange = new StagingChange
                        {
                            Name = "Monthly File Test Start Date",
                            OldValue = string.Format("{0:yyyy-MM-dd}", recentSRNStatusUpdate.MonthlyFileTestStartDate.Value),
                            NewValue = string.Format("{0:yyyy-MM-dd}", confirmation.TestStartDate.Value)
                        };

                        stagingChangeLog.Changes.Add(stagingChange);
                        recentSRNStatusUpdate.MonthlyFileTestStartDate = confirmation.TestStartDate.Value;
                    }

                    if (confirmation.TestEndDate != null && (recentSRNStatusUpdate.FileType == SRNStatusFileTypes.DailyFile || recentSRNStatusUpdate.FileType == SRNStatusFileTypes.MonthlyAndDailyFile))
                    {

                        var stagingChange = new StagingChange
                        {
                            Name = "Daily File Test End Date",
                            OldValue = string.Format("{0:yyyy-MM-dd}", recentSRNStatusUpdate.DailyFileTestEndDate.Value),
                            NewValue = string.Format("{0:yyyy-MM-dd}", confirmation.TestEndDate.Value)
                        };

                        stagingChangeLog.Changes.Add(stagingChange);
                        recentSRNStatusUpdate.DailyFileTestEndDate = confirmation.TestEndDate.Value;
                    }

                    if (confirmation.TestEndDate != null && (recentSRNStatusUpdate.FileType == SRNStatusFileTypes.MonthlyFile || recentSRNStatusUpdate.FileType == SRNStatusFileTypes.MonthlyAndDailyFile))
                    {
                        var stagingChange = new StagingChange
                        {
                            Name = "Monthly File Test End Date",
                            OldValue = string.Format("{0:yyyy-MM-dd}", recentSRNStatusUpdate.MonthlyFileTestEndDate.Value),
                            NewValue = string.Format("{0:yyyy-MM-dd}", confirmation.TestEndDate.Value)
                        };

                        stagingChangeLog.Changes.Add(stagingChange);
                        recentSRNStatusUpdate.MonthlyFileTestEndDate = confirmation.TestEndDate.Value;
                    }

                    if (confirmation.GoLiveDate != null && (recentSRNStatusUpdate.FileType == SRNStatusFileTypes.DailyFile || recentSRNStatusUpdate.FileType == SRNStatusFileTypes.MonthlyAndDailyFile))
                    {

                        var stagingChange = new StagingChange
                        {
                            Name = "Daily File Go Live Date",
                            OldValue = string.Format("{0:yyyy-MM-dd}", recentSRNStatusUpdate.DailyFileGoLiveDate.Value),
                            NewValue = string.Format("{0:yyyy-MM-dd}", confirmation.GoLiveDate.Value)
                        };

                        stagingChangeLog.Changes.Add(stagingChange);
                        recentSRNStatusUpdate.DailyFileGoLiveDate = confirmation.GoLiveDate.Value;
                    }

                    if (confirmation.GoLiveDate != null && (recentSRNStatusUpdate.FileType == SRNStatusFileTypes.MonthlyFile || recentSRNStatusUpdate.FileType == SRNStatusFileTypes.MonthlyAndDailyFile))
                    {
                        var stagingChange = new StagingChange
                        {
                            Name = "Monthly File Go Live Date",
                            OldValue = string.Format("{0:yyyy-MM-dd}", recentSRNStatusUpdate.MonthlyFileGoLiveDate.Value),
                            NewValue = string.Format("{0:yyyy-MM-dd}", confirmation.GoLiveDate.Value)
                        };

                        stagingChangeLog.Changes.Add(stagingChange);
                        recentSRNStatusUpdate.MonthlyFileGoLiveDate = confirmation.GoLiveDate.Value;
                    }

                    if (confirmation.IsTestingComplete == ConfirmSRNTestingCompleteEnum.Yes)
                    {
                        recentSRNStatusUpdate.SignoffDate = DateTime.Now;

                        var srnFileSignedOffFirst = srn.SRNStatusUpdates
                                .Where(i => i.SignoffDate != null)
                                .OrderBy(i => i.SignoffDate)
                                .FirstOrDefault();

                        var updatedSRNSignoffDate = (srnFileSignedOffFirst != null) ? srnFileSignedOffFirst.SignoffDate : recentSRNStatusUpdate.SignoffDate;

                        var stagingChange = new StagingChange
                        {
                            Name = "SRN Sign-off Date",
                            OldValue = srn.SignoffDate?.ToString("yyyy-MM-dd") ?? "",
                            NewValue = updatedSRNSignoffDate?.ToString("yyyy-MM-dd") ?? ""
                        };

                        stagingChangeLog.Changes.Add(stagingChange);

                        srn.SignoffDate = updatedSRNSignoffDate;

                    }

                    else if (confirmation.IsTestingComplete == ConfirmSRNTestingCompleteEnum.Cancel)
                    {
                        recentSRNStatusUpdate.IsComple = true;
                        recentSRNStatusUpdate.DateCompleted = DateTime.Now;
                        recentSRNStatusUpdate.Comments = "Workflow cancelled by " + user.FullName;

                        var stagingChange = new StagingChange
                        {
                            Name = "Comments",
                            OldValue = (srn.Comments != null) ? srn.Comments : "",
                            NewValue =
                                (recentSRNStatusUpdate.FileType == SRNStatusFileTypes.DailyFile) ?
                                "Workflow for Daily file cancelled by " + user.FullName :
                                (recentSRNStatusUpdate.FileType == SRNStatusFileTypes.MonthlyAndDailyFile) ?
                                "Workflow for Monthly file cancelled by " + user.FullName : "Workflow for Monthly file cancelled by " + user.FullName
                        };

                        stagingChangeLog.Changes.Add(stagingChange);
                    }

                    var changeBlob = JsonConvert.SerializeObject(stagingChangeLog);

                    if (stagingChangeLog.Changes.Count > 0)
                    {
                        var srnUpdateResource = _mapper.Map<SRNUpdateResource>(srn);
                        var entityBlob = JsonConvert.SerializeObject(srnUpdateResource);

                        Helpers.Helpers.CreateEventLog(_dbContext, user.Id, "SRN Update", srn.SRNNumber, entityBlob, changeBlob, srn.Id, "SRN");

                        _dbContext.SaveChanges();
                    }
                }
            }
        }

        public void CompleteBuyerStakeHolderManagerCreateUnconfirmedBuyer(string taskId)
        {
            using (var client = new HttpClient())
            {
                var task = GetTask(taskId);
                var currentTaskVariables = GetVariables(task.ProcessInstanceId);

                var content = new StringContent("", Encoding.UTF8, "application/json");
                var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });
                result.EnsureSuccessStatusCode();
            }
        }

        public ChangeRequestStaging GetMemberChangeRequest(int id)
        {
            try
            {
                var changeRequest = await _dbContext.MemberChangeRequests
                    .AsNoTracking()
                    .FirstOrDefaultAsync(i => i.Id == id);

                return changeRequest;
            }
            catch (Exception ex)
            {
                var message = "Unable to retrieve member change request for member Id " + id;
                Helpers.Helpers.LogError(_dbContext, ex, message);
                throw new Exception(message);
            }
        }

        public void CompleteStakeholderManagerSrnMergeReview(string taskId, TaskSHMSrnMergeReviewPutResource taskReview)
        {
            using (var client = new HttpClient())
            {
                var compatibleSRNExists = "";
                var isTestingOrMigrationRequired = "";

                var task = GetTask(taskId);
                var currentTaskVariables = GetVariables(task.ProcessInstanceId);

                if (taskReview != null)
                {
                    switch (taskReview.CompatibleSrnExists)
                    {
                        case SRNMergeSplitRequestReviewEnum.Yes:
                            compatibleSRNExists = "yes";

                            if (currentTaskVariables.Count > 0)
                            {
                                var mergeListVariable = currentTaskVariables.FirstOrDefault(i => i.Name == "SRNIdMergeList");

                                if (mergeListVariable != null)
                                {
                                    if (!string.IsNullOrEmpty(mergeListVariable.Value))
                                    {
                                        var mergeList = mergeListVariable.Value.Split(',');
                                        foreach (var srnId in mergeList)
                                        {
                                            var mergeSRN = await _dbContext.SRNs
                                                .AsNoTracking()
                                                .FirstOrDefaultAsync(i => i.Id == Convert.ToInt32(srnId));

                                            var mergeStatus = await _dbContext.SRNStatuses
                                                .FirstOrDefaultAsync(i => i.Name == "Deactivated - Merged");

                                            mergeSRN.SRNStatusId = mergeStatus.Id;
                                            mergeSRN.StatusLastUpdatedAt = DateTime.Now;

                                            Helpers.Helpers.PrepareSRNSplitRequestForUpdate(_dbContext, mergeSRN);
                                            _dbContext.Update(mergeSRN);
                                            _dbContext.SaveChanges();
                                        }
                                    }
                                }
                            }

                            break;

                        case SRNMergeSplitRequestReviewEnum.Pending:
                            compatibleSRNExists = "pending";
                            break;

                        case SRNMergeSplitRequestReviewEnum.Cancel:
                            compatibleSRNExists = "cancel";

                            if (currentTaskVariables.Count > 0)
                            {
                                var mergeListVariable = currentTaskVariables.FirstOrDefault(i => i.Name == "SRNIdMergeList");

                                if (mergeListVariable != null)
                                {
                                    if (!string.IsNullOrEmpty(mergeListVariable.Value))
                                    {
                                        var mergeList = mergeListVariable.Value.Split(',');
                                        foreach (var srnId in mergeList)
                                        {
                                            UpdateSRNStatusToInitialState(currentTaskVariables, Convert.ToInt32(srnId));
                                        }
                                    }
                                }
                            }
                            break;
                    }

                    isTestingOrMigrationRequired = taskReview.MigrationTestingRequired ? "yes" : "no";
                }

                var newTaskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                    {
                        {
                            "variables",
                            new Dictionary<string, Dictionary<string, string>>
                            {
                                {
                                    "CompatibleSRNExists",
                                    new Dictionary<string, string>()
                                    {
                                        { "value", compatibleSRNExists },
                                        { "type", "String" }
                                    }
                                },
                                {
                                    "IsTestingMigrationRequired",
                                    new Dictionary<string, string>()
                                    {
                                        { "value", isTestingOrMigrationRequired },
                                        { "type", "String" }
                                    }
                                },
                                {
                                    "BuyerSRNId",
                                    new Dictionary<string, string>()
                                    {
                                        { "value", "" },
                                        { "type", "String" }
                                    }
                                }
                            }
                        }
                    };

                var json = JsonConvert.SerializeObject(newTaskVariables);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });
                result.EnsureSuccessStatusCode();
            }
        }
        public void CompleteStakeholderManagerSrnSplitReview(string taskId, TaskSHMSrnSplitReviewPutResource taskReview)
        {
            using (var client = new HttpClient())
            {
                var compatibleSRNExists = "";
                var isTestingOrMigrationRequired = "";
                var task = GetTask(taskId);
                var currentTaskVariables = GetVariables(task.ProcessInstanceId);

                if (taskReview != null)
                {
                    switch (taskReview.CompatibleSrnExists)
                    {
                        case SRNMergeSplitRequestReviewEnum.Yes:
                            compatibleSRNExists = "yes";

                            if (currentTaskVariables.Count > 0)
                            {
                                var requestTypeVariable = currentTaskVariables.FirstOrDefault(i => i.Name == "InitialStatusId");
                                var requestType = "";

                                if (requestTypeVariable != null)
                                {
                                    requestType = requestTypeVariable.Value;
                                    //If it's full split, lets deactivate the SRN that was split
                                    if (requestType == "full")
                                    {
                                        var status = await _dbContext.SRNStatuses
                                            .AsNoTracking()
                                            .FirstOrDefaultAsync(i => i.Name == "Deactivated - Split");
                                        if (status != null)
                                        {
                                            var srnIdVariable = currentTaskVariables.FirstOrDefault(i => i.Name == "SplitFromSRNId");
                                            var srnId = 0;

                                            if (srnIdVariable != null)
                                            {
                                                srnId = Convert.ToInt32(srnIdVariable.Value);
                                                var srn = _dbContext.SRNs.FirstOrDefaultAsync(i => i.Id == srnId);
                                                srn.SRNStatusId = status.Id;
                                                srn.StatusLastUpdatedAt = DateTime.Now;
                                                _dbContext.Update(srn);
                                                _dbContext.SaveChanges();
                                            }
                                        }
                                    }
                                    //Otherwise, lets change the status to the initial state
                                    else
                                    {
                                        UpdateSRNStatusToInitialState(currentTaskVariables, "SplitFromSRNId", "SRN Split");
                                    }
                                }
                            }

                            break;

                        case SRNMergeSplitRequestReviewEnum.Pending:
                            compatibleSRNExists = "pending";
                            break;

                        case SRNMergeSplitRequestReviewEnum.Cancel:
                            compatibleSRNExists = "cancel";

                            UpdateSRNStatusToInitialState(currentTaskVariables, "SplitFromSRNId", "SRN Split");

                            break;
                    }

                    isTestingOrMigrationRequired = taskReview.MigrationTestingRequired ? "yes" : "no";
                }

                var newTaskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                    {
                        {
                            "variables",
                            new Dictionary<string, Dictionary<string, string>>
                            {
                                {
                                    "CompatibleSRNExists",
                                    new Dictionary<string, string>()
                                    {
                                        { "value", compatibleSRNExists },
                                        { "type", "String" }
                                    }
                                },
                                {
                                    "IsTestingMigrationRequired",
                                    new Dictionary<string, string>()
                                    {
                                        { "value", isTestingOrMigrationRequired },
                                        { "type", "String" }
                                    }
                                },
                                {
                                    "BuyerSRNId",
                                    new Dictionary<string, string>()
                                    {
                                        { "value", "" },
                                        { "type", "String" }
                                    }
                                }
                            }
                        }
                    };

                var json = JsonConvert.SerializeObject(newTaskVariables);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });
                result.EnsureSuccessStatusCode();
            }
        }

        public TaskSRNMergeGetResource GetSHMSRNMergeTask(string taskId)
        {
            var task = GetTask(taskId);
            var variables = GetVariables(task.ProcessInstanceId);

            if (variables.Count > 0)
            {
                var mergeToSRNIdVariable = variables.FirstOrDefault(i => i.Name == "MergeToSRNId");
                var mergeToSRNId = (mergeToSRNIdVariable != null) ? mergeToSRNIdVariable.Value : "0";

                var srnIdMergeListVariable = variables.FirstOrDefault(i => i.Name == "SRNIdMergeList");
                var srnIdMergeList = (srnIdMergeListVariable != null) ? srnIdMergeListVariable.Value : "0";
                string[] srnIdMergeListArray = { };

                if (!string.IsNullOrEmpty(srnIdMergeList) && srnIdMergeList != "0")
                {
                    srnIdMergeListArray = srnIdMergeList.Split(',');
                }

                var srnMergeGetResource = new TaskSRNMergeGetResource();

                var srnMergeRequest = new SRNMergeRequest();
                if (!string.IsNullOrEmpty(mergeToSRNId) && !string.IsNullOrEmpty(srnIdMergeList))
                {
                    var mergeRequests = _dbContext.SRNMergeRequests
                        .Where(i => i.ToSRNId == Convert.ToInt32(mergeToSRNId)
                            && srnIdMergeListArray.Contains(i.FromSRNId.ToString())
                            && i.Status == SRNMergeStatus.Requested)
                        .AsEnumerable();

                    if (mergeRequests.Count() > 0)
                    {
                        srnMergeRequest = mergeRequests.FirstOrDefault();
                        if (srnMergeRequest != null)
                        {
                            srnMergeGetResource.DailyFileDevelopmentStartDate = srnMergeRequest.DailyFileDevelopmentStartDate;
                            srnMergeGetResource.DailyFileDevelopmentEndDate = srnMergeRequest.DailyFileDevelopmentEndDate;
                            srnMergeGetResource.DailyFileTestStartDate = srnMergeRequest.DailyFileTestStartDate;
                            srnMergeGetResource.DailyFileTestEndDate = srnMergeRequest.DailyFileTestEndDate;
                            srnMergeGetResource.DailyFileGoLiveDate = srnMergeRequest.DailyFileGoLiveDate;

                            srnMergeGetResource.MonthlyFileDevelopmentStartDate = srnMergeRequest.MonthlyFileDevelopmentStartDate;
                            srnMergeGetResource.MonthlyFileDevelopmentEndDate = srnMergeRequest.MonthlyFileDevelopmentEndDate;
                            srnMergeGetResource.MonthlyFileTestStartDate = srnMergeRequest.MonthlyFileTestStartDate;
                            srnMergeGetResource.MonthlyFileTestEndDate = srnMergeRequest.MonthlyFileTestEndDate;
                            srnMergeGetResource.MonthlyFileGoLiveDate = srnMergeRequest.MonthlyFileGoLiveDate;
                        }
                    }
                }

                if (!string.IsNullOrEmpty(mergeToSRNId))
                    srnMergeGetResource.MergeToSRNId = Convert.ToInt32(mergeToSRNId);

                srnMergeGetResource.SRNIdMergeFromList = new List<SRNGetResource>();
                foreach (var srnId in srnIdMergeListArray)
                {
                    var srnResource = _srnRepository.Get(Convert.ToInt32(srnId));
                    srnMergeGetResource.SRNIdMergeFromList.Add(srnResource);
                }

                return srnMergeGetResource;
            }

            return null;
        }

        public TaskSRNSplitGetResource GetSHMSRNSplitTask(string taskId)
        {
            var task = GetTask(taskId);
            var variables = GetVariables(task.ProcessInstanceId);

            if (variables.Count > 0)
            {
                var splitFromSRNIdVariable = variables.FirstOrDefault(i => i.Name == "SplitFromSRNId");
                var splitFromSRNId = (splitFromSRNIdVariable != null) ? splitFromSRNIdVariable.Value : "0";

                var srnIdSplitListVariable = variables.FirstOrDefault(i => i.Name == "SRNIdSplitList");
                var srnIdSplitList = (srnIdSplitListVariable != null) ? srnIdSplitListVariable.Value : "0";
                string[] srnIdSplitListArray = { };

                if (!string.IsNullOrEmpty(srnIdSplitList) && srnIdSplitList != "0")
                {
                    srnIdSplitListArray = srnIdSplitList.Split(',');
                }

                var typeVariable = variables.FirstOrDefault(i => i.Name == "SaleType");
                var splitType = (typeVariable != null) ? typeVariable.Value : "0";

                var srnSplitGetResource = new TaskSRNSplitGetResource();

                var srnSplitRequest = new SRNSplitRequest();
                if (!string.IsNullOrEmpty(splitFromSRNId) && !string.IsNullOrEmpty(srnIdSplitList))
                {
                    var splitRequests = _dbContext.SRNSplitRequests
                        .Where(i => i.FromSRNId == Convert.ToInt32(splitFromSRNId)
                            && srnIdSplitListArray.Contains(i.ToSRNId.ToString())
                            && i.Status == SRNSplitStatus.Requested)
                        .AsEnumerable();

                    if (splitRequests.Count() > 0)
                    {
                        srnSplitRequest = splitRequests.FirstOrDefault();
                        if (srnSplitRequest != null)
                        {
                            srnSplitGetResource.DailyFileDevelopmentStartDate = srnSplitRequest.DailyFileDevelopmentStartDate;
                            srnSplitGetResource.DailyFileDevelopmentEndDate = srnSplitRequest.DailyFileDevelopmentEndDate;
                            srnSplitGetResource.DailyFileTestStartDate = srnSplitRequest.DailyFileTestStartDate;
                            srnSplitGetResource.DailyFileTestEndDate = srnSplitRequest.DailyFileTestEndDate;
                            srnSplitGetResource.DailyFileGoLiveDate = srnSplitRequest.DailyFileGoLiveDate;

                            srnSplitGetResource.MonthlyFileDevelopmentStartDate = srnSplitRequest.MonthlyFileDevelopmentStartDate;
                            srnSplitGetResource.MonthlyFileDevelopmentEndDate = srnSplitRequest.MonthlyFileDevelopmentEndDate;
                            srnSplitGetResource.MonthlyFileTestStartDate = srnSplitRequest.MonthlyFileTestStartDate;
                            srnSplitGetResource.MonthlyFileTestEndDate = srnSplitRequest.MonthlyFileTestEndDate;
                            srnSplitGetResource.MonthlyFileGoLiveDate = srnSplitRequest.MonthlyFileGoLiveDate;
                        }
                    }
                }

                if (!string.IsNullOrEmpty(splitFromSRNId))
                    srnSplitGetResource.SplitFromSRNId = Convert.ToInt32(splitFromSRNId);

                srnSplitGetResource.SRNIdSplitList = new List<SRNGetResource>();
                foreach (var srnId in srnIdSplitListArray)
                {
                    var srnResource = _srnRepository.Get(Convert.ToInt32(srnId));
                    srnSplitGetResource.SRNIdSplitList.Add(srnResource);
                }

                srnSplitGetResource.Type = (splitType == "partial") ? "Partial" : "Full";

                return srnSplitGetResource;
            }

            return null;
        }

        public TaskSRNSellerGetResource GetSHMSellerSRNSaleTask(string taskId)
        {
            var task = GetTask(taskId);
            var variables = GetVariables(task.ProcessInstanceId);

            if (variables.Count > 0)
            {
                var srnIdToBeVariable = variables.FirstOrDefault(i => i.Name == "SRNIdToBeSold");
                var srnIdToBeSold = (srnIdToBeVariable != null) ? srnIdToBeVariable.Value : "0";

                var buyerRegisteredNameVariable = variables.FirstOrDefault(i => i.Name == "BuyerRegisteredName");
                var buyerRegisteredName = (buyerRegisteredNameVariable != null) ? buyerRegisteredNameVariable.Value : "";

                var buyerRegisteredNumberVariable = variables.FirstOrDefault(i => i.Name == "BuyerRegisteredNumber");
                var buyerRegisteredNumber = (buyerRegisteredNumberVariable != null) ? buyerRegisteredNumberVariable.Value : "";

                var saleTypeVariable = variables.FirstOrDefault(i => i.Name == "SaleType");
                var saleType = (saleTypeVariable != null) ? saleTypeVariable.Value : "";

                if (!string.IsNullOrEmpty(saleType))
                {
                    saleType = (saleType == "partial") ? "Partial" : "Full";
                }

                var srnSale = new TaskSRNSellerGetResource
                {
                    BuyerRegisteredName = buyerRegisteredName,
                    BuyerRegisteredNumber = buyerRegisteredNumber,
                    SRNIdToBeSold = Convert.ToInt32(srnIdToBeSold),
                    SaleType = saleType
                };

                return srnSale;
            }

            return null;
        }

        public TaskSRNBuyerGetResource GetSHMBuyerSRNSaleTask(string taskId)
        {
            var task = GetTask(taskId);
            var variables = GetVariables(task.ProcessInstanceId);

            if (variables.Count > 0)
            {
                var srnIdToBeVariable = variables.FirstOrDefault(i => i.Name == "SRNIdToBeSold");
                var srnIdToBeSold = (srnIdToBeVariable != null) ? srnIdToBeVariable.Value : "0";

                var saleTypeVariable = variables.FirstOrDefault(i => i.Name == "SaleType");
                var saleType = (saleTypeVariable != null) ? saleTypeVariable.Value : "";

                var buyerMemberVariable = variables.FirstOrDefault(i => i.Name == "BuyerMemberId");
                var buyerMemberId = (buyerMemberVariable != null) ? buyerMemberVariable.Value : "0";

                if (!string.IsNullOrEmpty(saleType))
                {
                    saleType = (saleType == "partial") ? "Partial" : "Full";
                }

                var srnSale = new TaskSRNBuyerGetResource
                {
                    SRNIdToBeSold = Convert.ToInt32(srnIdToBeSold),
                    SaleType = saleType,
                    BuyerMemberId = Convert.ToInt32(buyerMemberId)
                };

                return srnSale;
            }

            return null;
        }

        public void LogError(Exception ex, string message)
        {
            Helpers.Helpers.LogError(_dbContext, ex, message);
        }

        private void UpdateSRNStatusToInitialState(List<VariableInstanceGetResource> currentTaskVariables, string actionVariableName, string changeType)
        {
            if (currentTaskVariables.Count > 0)
            {
                var initialStatusIdVariable = currentTaskVariables.FirstOrDefault(i => i.Name == "InitialStatusId");
                var initialStatusId = 0;

                if (initialStatusIdVariable != null)
                {
                    initialStatusId = Convert.ToInt32(initialStatusIdVariable.Value);
                }

                var SRNIdVariable = currentTaskVariables.FirstOrDefault(i => i.Name == actionVariableName);
                var SRNId = 0;

                if (SRNIdVariable != null)
                {
                    SRNId = Convert.ToInt32(SRNIdVariable.Value);
                }

                if (initialStatusId > 0 && SRNId > 0)
                {
                    var srn = await _dbContext.Set<SRN>()
                        .Include(i => i.SRNStatus)
                        .FirstOrDefaultAsync(i => i.Id == SRNId);

                    var oldStatus = srn.SRNStatus.Name;

                    srn.SRNStatusId = initialStatusId;
                    srn.StatusLastUpdatedAt = DateTime.Now;

                    _dbContext.SaveChanges();

                    var initialStatus = await _dbContext.Set<SRNStatus>()
                        .FirstOrDefaultAsync(i => i.Id == initialStatusId);

                    Helpers.Helpers.CreateSRNStatusEventLog(_dbContext, _mapper, oldStatus, initialStatus.Name, changeType, srn, new User());
                }
            }
        }

        private void UpdateSRNStatusToInitialState(List<VariableInstanceGetResource> currentTaskVariables, int srnId)
        {
            if (currentTaskVariables.Count > 0)
            {
                var initialStatusIdVariable = currentTaskVariables.FirstOrDefault(i => i.Name == "InitialStatusId");
                var initialStatusId = 0;

                if (initialStatusIdVariable != null)
                {
                    initialStatusId = Convert.ToInt32(initialStatusIdVariable.Value);
                }

                if (initialStatusId > 0 && srnId > 0)
                {
                    var srn = await _dbContext.SRNs
                        .AsNoTracking()
                        .FirstOrDefaultAsync(i => i.Id == srnId);

                    srn.SRNStatusId = initialStatusId;
                    srn.StatusLastUpdatedAt = DateTime.Now;

                    _dbContext.Update(srn);
                    _dbContext.SaveChanges();
                }
            }
        }

        public void CompleteStakeHolderManagerSRNApplication_ConfirmSRNGoLive(string taskId, TaskConfirmSRNGoLiveResource confirmation)
        {
            using (var client = new HttpClient())
            {
                if (confirmation != null)
                {
                    var isConfirmed = "";

                    if (confirmation.IsLiveComplete == ConfirmSRNGoLiveEnum.Yes)
                        isConfirmed = "yes";
                    else if (confirmation.IsLiveComplete == ConfirmSRNGoLiveEnum.No)
                        isConfirmed = "no";
                    else if (confirmation.IsLiveComplete == ConfirmSRNGoLiveEnum.Cancel)
                        isConfirmed = "cancel";
                    else
                        isConfirmed = "no";

                    var developmentStartDate = (confirmation.DevelopmentStartDate != null && confirmation.DevelopmentStartDate != DateTime.MinValue) ? confirmation.DevelopmentStartDate.Value.Date.ToString("yyyy-MM-dd") : "";
                    var developmentEndDate = (confirmation.DevelopmentEndDate != null && confirmation.DevelopmentEndDate != DateTime.MinValue) ? confirmation.DevelopmentEndDate.Value.Date.ToString("yyyy-MM-dd") : "";
                    var testStartDate = (confirmation.TestStartDate != null && confirmation.TestStartDate != DateTime.MinValue) ? confirmation.TestStartDate.Value.Date.ToString("yyyy-MM-dd") : "";
                    var testEndDate = (confirmation.TestEndDate != null && confirmation.TestEndDate != DateTime.MinValue) ? confirmation.TestEndDate.Value.Date.ToString("yyyy-MM-dd") : "";
                    var goLiveDate = (confirmation.GoLiveDate != null && confirmation.GoLiveDate != DateTime.MinValue) ? confirmation.GoLiveDate.Value.Date.ToString("yyyy-MM-dd") : "";

                    var newTaskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                    {
                        {
                            "variables",
                            new Dictionary<string, Dictionary<string, string>>
                            {
                                {
                                    "IsSRNLive",
                                    new Dictionary<string, string>()
                                    {
                                        { "value", isConfirmed },
                                        { "type", "String" }
                                    }
                                },
                                {
                                    "goLiveDate",
                                    new Dictionary<string, string>()
                                    {
                                        { "value", goLiveDate },
                                        { "type", "String" }
                                    }
                                }
                            }
                        }
                    };

                    var task = GetTask(taskId);
                    var currentTaskVariables = GetVariables(task.ProcessInstanceId);

                    var json = JsonConvert.SerializeObject(newTaskVariables);
                    var content = new StringContent(json, Encoding.UTF8, "application/json");
                    var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                    var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });
                    result.EnsureSuccessStatusCode();

                    var srnIDVariable = currentTaskVariables.FirstOrDefault(i => i.Name == "SRNId");

                    var srnId = 0;
                    if (srnIDVariable != null)
                        srnId = Convert.ToInt32(srnIDVariable.Value);

                    if (srnId > 0)
                    {
                        var srn = await _dbContext.Set<SRN>()
                            .Include(i => i.SRNStatusUpdates)
                            .FirstOrDefaultAsync(i => i.Id == srnId);
                        var recentSRNStatusUpdate = srn.SRNStatusUpdates.FirstOrDefault(i => i.ProcessInstanceId == task.ProcessInstanceId);
                        var stagingChangeLog = new MemberStagingChangeLogResource();
                        
                        if (recentSRNStatusUpdate.FileType == SRNStatusFileTypes.DailyFile && !string.IsNullOrEmpty(developmentStartDate))
                        {
                            var stagingChange = new StagingChange
                            {
                                Name = "Daily Development Start Date",
                                OldValue = recentSRNStatusUpdate.DailyFileDevelopmentStartDate.HasValue ? recentSRNStatusUpdate.DailyFileDevelopmentStartDate.Value.ToString("yyyy-MM-dd") : "",
                                NewValue = (confirmation.DevelopmentStartDate != null) ? confirmation.DevelopmentStartDate.Value.ToString("yyyy-MM-dd") : ""
                            };

                            stagingChangeLog.Changes.Add(stagingChange);
                        }
                        else if (recentSRNStatusUpdate.FileType == SRNStatusFileTypes.MonthlyFile && !string.IsNullOrEmpty(developmentStartDate))
                        {
                            var stagingChange = new StagingChange
                            {
                                Name = "Monthly Development Start Date",
                                OldValue = recentSRNStatusUpdate.MonthlyFileDevelopmentStartDate.HasValue ? recentSRNStatusUpdate.MonthlyFileDevelopmentStartDate.Value.ToString("yyyy-MM-dd") : "",
                                NewValue = (confirmation.DevelopmentStartDate != null) ? confirmation.DevelopmentStartDate.Value.ToString("yyyy-MM-dd") : ""
                            };

                            stagingChangeLog.Changes.Add(stagingChange);
                        }

                        if (recentSRNStatusUpdate.FileType == SRNStatusFileTypes.DailyFile && !string.IsNullOrEmpty(developmentEndDate))
                        {
                            var stagingChange = new StagingChange
                            {
                                Name = "Daily Development End Date",
                                OldValue = recentSRNStatusUpdate.DailyFileDevelopmentEndDate.HasValue ? recentSRNStatusUpdate.DailyFileDevelopmentEndDate.Value.ToString("yyyy-MM-dd") : "",
                                NewValue = (confirmation.DevelopmentEndDate != null) ? confirmation.DevelopmentEndDate.Value.ToString("yyyy-MM-dd") : ""
                            };

                            stagingChangeLog.Changes.Add(stagingChange);
                        }
                        else if (recentSRNStatusUpdate.FileType == SRNStatusFileTypes.MonthlyFile && !string.IsNullOrEmpty(developmentStartDate))
                        {
                            var stagingChange = new StagingChange
                            {
                                Name = "Monthly Development End Date",
                                OldValue = recentSRNStatusUpdate.MonthlyFileDevelopmentEndDate.HasValue ? recentSRNStatusUpdate.MonthlyFileDevelopmentEndDate.Value.ToString("yyyy-MM-dd") : "",
                                NewValue = (confirmation.DevelopmentEndDate != null) ? confirmation.DevelopmentEndDate.Value.ToString("yyyy-MM-dd") : ""
                            };

                            stagingChangeLog.Changes.Add(stagingChange);
                        }

                        if (recentSRNStatusUpdate.FileType == SRNStatusFileTypes.DailyFile && !string.IsNullOrEmpty(testStartDate))
                        {
                            var stagingChange = new StagingChange
                            {
                                Name = "Daily Test Start Date",
                                OldValue = recentSRNStatusUpdate.DailyFileTestStartDate.HasValue ? recentSRNStatusUpdate.DailyFileTestStartDate.Value.ToString("yyyy-MM-dd") : "",
                                NewValue = (confirmation.TestStartDate != null) ? confirmation.TestStartDate.Value.ToString("yyyy-MM-dd") : ""
                            };

                            stagingChangeLog.Changes.Add(stagingChange);
                        }
                        else if (recentSRNStatusUpdate.FileType == SRNStatusFileTypes.MonthlyFile && !string.IsNullOrEmpty(testStartDate))
                        {
                            var stagingChange = new StagingChange
                            {
                                Name = "Monthly Test Start Date",
                                OldValue = recentSRNStatusUpdate.MonthlyFileTestStartDate.HasValue ? recentSRNStatusUpdate.MonthlyFileTestStartDate.Value.ToString("yyyy-MM-dd") : "",
                                NewValue = (confirmation.TestStartDate != null) ? confirmation.TestStartDate.Value.ToString("yyyy-MM-dd") : ""
                            };

                            stagingChangeLog.Changes.Add(stagingChange);
                        }

                        if (recentSRNStatusUpdate.FileType == SRNStatusFileTypes.DailyFile && !string.IsNullOrEmpty(testEndDate))
                        {
                            var stagingChange = new StagingChange
                            {
                                Name = "Daily Test End Date",
                                OldValue = recentSRNStatusUpdate.DailyFileTestEndDate.HasValue ? recentSRNStatusUpdate.DailyFileTestEndDate.Value.ToString("yyyy-MM-dd") : "",
                                NewValue = (confirmation.TestEndDate != null) ? confirmation.TestEndDate.Value.ToString("yyyy-MM-dd") : ""
                            };

                            stagingChangeLog.Changes.Add(stagingChange);
                        }
                        else if (recentSRNStatusUpdate.FileType == SRNStatusFileTypes.MonthlyFile && !string.IsNullOrEmpty(testEndDate))
                        {
                            var stagingChange = new StagingChange
                            {
                                Name = "Monthly Test End Date",
                                OldValue = recentSRNStatusUpdate.MonthlyFileTestEndDate.HasValue ? recentSRNStatusUpdate.MonthlyFileTestEndDate.Value.ToString("yyyy-MM-dd") : "",
                                NewValue = (confirmation.TestEndDate != null) ? confirmation.TestEndDate.Value.ToString("yyyy-MM-dd") : ""
                            };

                            stagingChangeLog.Changes.Add(stagingChange);
                        }

                        if (recentSRNStatusUpdate.FileType == SRNStatusFileTypes.DailyFile && !string.IsNullOrEmpty(goLiveDate))
                        {
                            var stagingChange = new StagingChange
                            {
                                Name = "Daily Go Live Date",
                                OldValue = recentSRNStatusUpdate.DailyFileGoLiveDate.HasValue ? recentSRNStatusUpdate.DailyFileGoLiveDate.Value.ToString("yyyy-MM-dd") : "",
                                NewValue = (confirmation.GoLiveDate != null) ? confirmation.GoLiveDate.Value.ToString("yyyy-MM-dd") : ""
                            };

                            stagingChangeLog.Changes.Add(stagingChange);
                        }
                        else if (recentSRNStatusUpdate.FileType == SRNStatusFileTypes.MonthlyFile && !string.IsNullOrEmpty(goLiveDate))
                        {
                            var stagingChange = new StagingChange
                            {
                                Name = "Monthly Go Live Date",
                                OldValue = recentSRNStatusUpdate.MonthlyFileGoLiveDate.HasValue ? recentSRNStatusUpdate.MonthlyFileGoLiveDate.Value.ToString("yyyy-MM-dd") : "",
                                NewValue = (confirmation.GoLiveDate != null) ? confirmation.GoLiveDate.Value.ToString("yyyy-MM-dd") : ""
                            };

                            stagingChangeLog.Changes.Add(stagingChange);
                        }

                        var changeBlob = JsonConvert.SerializeObject(stagingChangeLog);
                        var srnUpdateResource = _mapper.Map<SRNUpdateResource>(srn);
                        var entityBlob = JsonConvert.SerializeObject(srnUpdateResource);
                        var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext);

                        Helpers.Helpers.CreateEventLog(_dbContext, user.Id, "SRN Update", srn.TradingName, entityBlob, changeBlob, srn.Id, "SRN");

                        if (confirmation.IsLiveComplete == ConfirmSRNGoLiveEnum.Yes)
                        {
                            var liveStatus = await _dbContext.SRNStatuses
                                .FirstOrDefaultAsync(i => i.Name == "Live");
                            
                            if (srn.FileType == SRNStatusFileTypes.MonthlyAndDailyFile && srn.SRNStatusId != liveStatus.Id)
                            {
                                if (srn.SRNStatusId != 4)
                                {
                                    var newSrnStatusEntry = new SrnStatusHistory()
                                    {
                                        SrnId = srn.Id,
                                        StatusId = 4,
                                        StatusDate = DateTime.Now,
                                        StatusReasonId = srn.SRNStatusReasonId
                                    };

                                    _dbContext.SrnStatusHistory.Add(newSrnStatusEntry);
                                    _dbContext.SaveChanges();
                                }

                                srn.SRNStatusId = liveStatus.Id;
                                srn.StatusLastUpdatedAt = DateTime.Now;
                            }
                            else if (srn.FileType == SRNStatusFileTypes.DailyFile || srn.FileType == SRNStatusFileTypes.MonthlyFile)
                            {
                                if (srn.SRNStatusId != 4)
                                {
                                    var newSrnStatusEntry = new SrnStatusHistory()
                                    {
                                        SrnId = srn.Id,
                                        StatusId = 4,
                                        StatusDate = DateTime.Now,
                                        StatusReasonId = srn.SRNStatusReasonId
                                    };

                                    _dbContext.SrnStatusHistory.Add(newSrnStatusEntry);
                                    _dbContext.SaveChanges();
                                }

                                srn.SRNStatusId = 4; //Live status
                                srn.StatusLastUpdatedAt = DateTime.Now;
                            }
                        }



                        //Create new record with and set different values                      
                        if (confirmation.IsLiveComplete == ConfirmSRNGoLiveEnum.Yes)
                        {
                            var rolloutLiveStatus = await _dbContext.RolloutStatuses
                                        .FirstOrDefaultAsync(i => i.Name == "Live");
                            var newSrnStatusHistoryEntry = new SRNStatusUpdateHistory()
                            {
                                DailyFileDevelopmentStartDate = confirmation.DevelopmentStartDate ?? recentSRNStatusUpdate.DailyFileDevelopmentStartDate,
                                DailyFileDevelopmentEndDate = confirmation.DevelopmentEndDate ?? recentSRNStatusUpdate.DailyFileDevelopmentEndDate,
                                DailyFileTestStartDate = confirmation.TestStartDate ?? recentSRNStatusUpdate.DailyFileTestStartDate,
                                DailyFileTestEndDate = confirmation.TestEndDate ?? recentSRNStatusUpdate.DailyFileTestEndDate,
                                DailyFileGoLiveDate = recentSRNStatusUpdate != null ? recentSRNStatusUpdate.DailyFileGoLiveDate : confirmation.GoLiveDate,
                                MonthlyFileDevelopmentStartDate = confirmation.DevelopmentStartDate ?? recentSRNStatusUpdate.MonthlyFileDevelopmentStartDate,
                                MonthlyFileDevelopmentEndDate = confirmation.DevelopmentEndDate ?? recentSRNStatusUpdate.MonthlyFileDevelopmentEndDate,
                                MonthlyFileTestStartDate = confirmation.TestStartDate ?? recentSRNStatusUpdate.MonthlyFileTestStartDate,
                                MonthlyFileTestEndDate = confirmation.TestEndDate ?? recentSRNStatusUpdate.MonthlyFileTestEndDate,
                                MonthlyFileGoLiveDate = recentSRNStatusUpdate != null ? recentSRNStatusUpdate.MonthlyFileGoLiveDate : confirmation.GoLiveDate,
                                DateCreated = DateTime.Now,
                                FileType = recentSRNStatusUpdate.FileType,
                                IsComple = true,
                                IsLiveFileSubmissionsSuspended = false,
                                SRNId = srn.Id,
                                SRNStatusId = recentSRNStatusUpdate.SRNStatusId,
                                Comments = recentSRNStatusUpdate.Comments,
                                ProcessInstanceId = recentSRNStatusUpdate.ProcessInstanceId,
                                RolloutStatusId = rolloutLiveStatus.Id,
                                SignoffDate = recentSRNStatusUpdate.SignoffDate,
                                DateCompleted = DateTime.Now,
                                SRNStatusReasonId = recentSRNStatusUpdate.SRNStatusReasonId,
                                SRNFileTestingStatusReason = recentSRNStatusUpdate.SRNFileTestingStatusReason,
                                IsDailyFileLive = recentSRNStatusUpdate.FileType == SRNStatusFileTypes.DailyFile ? true : recentSRNStatusUpdate.IsDailyFileLive,
                                IsMonthlyFileLive = recentSRNStatusUpdate.FileType == SRNStatusFileTypes.MonthlyFile ? true : recentSRNStatusUpdate.IsMonthlyFileLive
                            };
                            _dbContext.SRNStatusUpdateHistory.Add(newSrnStatusHistoryEntry);
                            _dbContext.SaveChanges();
                        }
                        else if (confirmation.IsLiveComplete == ConfirmSRNGoLiveEnum.Cancel)
                        {
                            var newSrnStatusHistoryEntry = new SRNStatusUpdateHistory()
                            {
                                DailyFileDevelopmentStartDate = confirmation.DevelopmentStartDate ?? recentSRNStatusUpdate.DailyFileDevelopmentStartDate,
                                DailyFileDevelopmentEndDate = confirmation.DevelopmentEndDate ?? recentSRNStatusUpdate.DailyFileDevelopmentEndDate,
                                DailyFileTestStartDate = confirmation.TestStartDate ?? recentSRNStatusUpdate.DailyFileTestStartDate,
                                DailyFileTestEndDate = confirmation.TestEndDate ?? recentSRNStatusUpdate.DailyFileTestEndDate,
                                DailyFileGoLiveDate = recentSRNStatusUpdate != null ? recentSRNStatusUpdate.DailyFileGoLiveDate : confirmation.GoLiveDate,
                                MonthlyFileDevelopmentStartDate = confirmation.DevelopmentStartDate ?? recentSRNStatusUpdate.MonthlyFileDevelopmentStartDate,
                                MonthlyFileDevelopmentEndDate = confirmation.DevelopmentEndDate ?? recentSRNStatusUpdate.MonthlyFileDevelopmentEndDate,
                                MonthlyFileTestStartDate = confirmation.TestStartDate ?? recentSRNStatusUpdate.MonthlyFileTestStartDate,
                                MonthlyFileTestEndDate = confirmation.TestEndDate ?? recentSRNStatusUpdate.MonthlyFileTestEndDate,
                                MonthlyFileGoLiveDate = recentSRNStatusUpdate != null ? recentSRNStatusUpdate.MonthlyFileGoLiveDate : confirmation.GoLiveDate,
                                DateCreated = DateTime.Now,
                                FileType = recentSRNStatusUpdate.FileType,
                                IsComple = true,
                                IsLiveFileSubmissionsSuspended = false,
                                SRNId = srn.Id,
                                SRNStatusId = recentSRNStatusUpdate.SRNStatusId,
                                Comments = "Workflow cancelled by " + user.FullName,
                                ProcessInstanceId = recentSRNStatusUpdate.ProcessInstanceId,
                                RolloutStatusId = recentSRNStatusUpdate.RolloutStatusId,
                                SignoffDate = recentSRNStatusUpdate.SignoffDate,
                                DateCompleted = DateTime.Now,
                                SRNStatusReasonId = recentSRNStatusUpdate.SRNStatusReasonId,
                                SRNFileTestingStatusReason = recentSRNStatusUpdate.SRNFileTestingStatusReason,
                                IsDailyFileLive = recentSRNStatusUpdate.FileType == SRNStatusFileTypes.DailyFile ? true : recentSRNStatusUpdate.IsDailyFileLive,
                                IsMonthlyFileLive = recentSRNStatusUpdate.FileType == SRNStatusFileTypes.MonthlyFile ? true : recentSRNStatusUpdate.IsMonthlyFileLive,
                            };
                            _dbContext.SRNStatusUpdateHistory.Add(newSrnStatusHistoryEntry);
                            _dbContext.SaveChanges();
                        }

                    }
                    _dbContext.SaveChanges();
                }
            }
        }


        public void CompleteStakeHolderManagerSRNSaleSplitMerge_ConfirmSRNGoLive(string taskId, TaskConfirmSRNGoLiveResource confirmation)
        {
            using (var client = new HttpClient())
            {
                if (confirmation != null)
                {
                    var isConfirmed = "";

                    if (confirmation.IsLiveComplete == ConfirmSRNGoLiveEnum.Yes)
                        isConfirmed = "yes";
                    else if (confirmation.IsLiveComplete == ConfirmSRNGoLiveEnum.No)
                        isConfirmed = "no";
                    else if (confirmation.IsLiveComplete == ConfirmSRNGoLiveEnum.Cancel)
                        isConfirmed = "cancel";
                    else
                        isConfirmed = "no";

                    var goLiveDate = (confirmation.GoLiveDate != null && confirmation.GoLiveDate != DateTime.MinValue) ? confirmation.GoLiveDate.Value.Date.ToString("yyyy-MM-dd") : "";

                    var newTaskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                    {
                        {
                            "variables",
                            new Dictionary<string, Dictionary<string, string>>
                            {
                                {
                                    "IsSRNLive",
                                    new Dictionary<string, string>()
                                    {
                                        { "value", isConfirmed },
                                        { "type", "String" }
                                    }
                                },
                                {
                                    "goLiveDate",
                                    new Dictionary<string, string>()
                                    {
                                        { "value", goLiveDate },
                                        { "type", "String" }
                                    }
                                }
                            }
                        }
                    };

                    var task = GetTask(taskId);
                    var currentTaskVariables = GetVariables(task.ProcessInstanceId);

                    var json = JsonConvert.SerializeObject(newTaskVariables);
                    var content = new StringContent(json, Encoding.UTF8, "application/json");
                    var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                    var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });
                    result.EnsureSuccessStatusCode();

                    var requestType = currentTaskVariables.FirstOrDefault(i => i.Name == "RequestType").Value;
                    var srnId = 0;

                    if (requestType == "sale")
                    {
                        var srnIDVariable = currentTaskVariables.FirstOrDefault(i => i.Name == "BuyerSRNId");

                        if (srnIDVariable != null)
                        {
                            if (!string.IsNullOrEmpty(srnIDVariable.Value))
                                srnId = Convert.ToInt32(srnIDVariable.Value);
                        }


                        if (srnId > 0)
                        {
                            var srn = await _dbContext.Set<SRN>()
                                .Include(i => i.SRNStatus)
                                .Include(i => i.SRNStatusUpdates)
                                .FirstOrDefaultAsync(i => i.Id == srnId);

                            var srnUpdateResource = _mapper.Map<SRNUpdateResource>(srn);
                            var entityBlob = JsonConvert.SerializeObject(srnUpdateResource);

                            var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext);

                            var stagingChangeLog = new MemberStagingChangeLogResource();

                            var recentSRNStatusUpdate = srn.SRNStatusUpdates.FirstOrDefault(i => i.ProcessInstanceId == task.ProcessInstanceId);

                            if (recentSRNStatusUpdate.FileType == SRNStatusFileTypes.DailyFile && !string.IsNullOrEmpty(goLiveDate))
                            {
                                var stagingChange = new StagingChange
                                {
                                    Name = "Daily File Go Live Date",
                                    OldValue = (recentSRNStatusUpdate.DailyFileGoLiveDate != null) ? recentSRNStatusUpdate.DailyFileGoLiveDate.Value.ToString("yyyy-MM-dd") : "",
                                    NewValue = goLiveDate
                                };

                                stagingChangeLog.Changes.Add(stagingChange);

                                recentSRNStatusUpdate.DailyFileGoLiveDate = confirmation.GoLiveDate;
                            }
                            else if (recentSRNStatusUpdate.FileType == SRNStatusFileTypes.MonthlyFile && !string.IsNullOrEmpty(goLiveDate))
                            {

                                var stagingChange = new StagingChange
                                {
                                    Name = "Monthly File Go Live Date",
                                    OldValue = (recentSRNStatusUpdate.MonthlyFileGoLiveDate != null) ? recentSRNStatusUpdate.MonthlyFileGoLiveDate.Value.ToString("yyyy-MM-dd") : "",
                                    NewValue = goLiveDate
                                };

                                stagingChangeLog.Changes.Add(stagingChange);

                                recentSRNStatusUpdate.MonthlyFileGoLiveDate = confirmation.GoLiveDate;
                            }
                            if (confirmation.IsLiveComplete == ConfirmSRNGoLiveEnum.Yes)
                            {
                                var newSrnStatus = await _dbContext.SRNStatuses
                                    .FirstOrDefaultAsync(i => i.Name == "Live");

                                var stagingChange = new StagingChange
                                {
                                    Name = "SRN Status",
                                    OldValue = srn.SRNStatus.Name,
                                    NewValue = newSrnStatus.Name
                                };

                                stagingChangeLog.Changes.Add(stagingChange);

                                srn.SRNStatusId = newSrnStatus.Id;
                                srn.StatusLastUpdatedAt = DateTime.Now;
                            }

                            _dbContext.SaveChanges();

                            var changeBlob = JsonConvert.SerializeObject(stagingChangeLog);

                            Helpers.Helpers.CreateEventLog(_dbContext, user.Id, "SRN Sale", srn.TradingName, entityBlob, changeBlob, srn.Id, "SRN");
                        }
                    }
                    else if (requestType == "split")
                    {
                        var splitListVariable = currentTaskVariables.FirstOrDefault(i => i.Name == "SRNIdSplitList");

                        if (splitListVariable != null)
                        {
                            var splitList = splitListVariable.Value.Split(',');

                            foreach (var id in splitList)
                            {
                                if (!string.IsNullOrEmpty(id))
                                {
                                    srnId = Convert.ToInt32(id);

                                    if (srnId > 0)
                                    {
                                        var srn = await _dbContext.Set<SRN>()
                                            .Include(i => i.SRNStatus)
                                            .Include(i => i.SRNStatusUpdates)
                                            .FirstOrDefaultAsync(i => i.Id == srnId);

                                        var recentSRNStatusUpdate = srn.SRNStatusUpdates.FirstOrDefault(i => i.ProcessInstanceId == task.ProcessInstanceId);

                                        var srnUpdateResource = _mapper.Map<SRNUpdateResource>(srn);
                                        var entityBlob = JsonConvert.SerializeObject(srnUpdateResource);

                                        var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext);

                                        var stagingChangeLog = new MemberStagingChangeLogResource();

                                        if (confirmation.GoLiveDate != null && recentSRNStatusUpdate.FileType == SRNStatusFileTypes.DailyFile)
                                        {

                                            var stagingChange = new StagingChange
                                            {
                                                Name = "Daily Go Live Date",
                                                OldValue = string.Format("{0:yyyy-MM-dd}", confirmation.GoLiveDate.Value),
                                                NewValue = (confirmation.GoLiveDate != null) ? confirmation.GoLiveDate.Value.ToString("yyyy-MM-dd") : ""
                                            };

                                            stagingChangeLog.Changes.Add(stagingChange);

                                            recentSRNStatusUpdate.DailyFileGoLiveDate = confirmation.GoLiveDate;

                                        }
                                        else if (confirmation.GoLiveDate != null && recentSRNStatusUpdate.FileType == SRNStatusFileTypes.MonthlyFile)
                                        {
                                            var stagingChange = new StagingChange
                                            {
                                                Name = "Monthly Go Live Date",
                                                OldValue = string.Format("{0:yyyy-MM-dd}", confirmation.GoLiveDate.Value),
                                                NewValue = (confirmation.GoLiveDate != null) ? confirmation.GoLiveDate.Value.ToString("yyyy-MM-dd") : ""
                                            };

                                            stagingChangeLog.Changes.Add(stagingChange);

                                            recentSRNStatusUpdate.MonthlyFileGoLiveDate = confirmation.GoLiveDate;
                                        }
                                        if (confirmation.IsLiveComplete == ConfirmSRNGoLiveEnum.Yes)
                                        {
                                            var newSrnStatus = await _dbContext.SRNStatuses
                                                .FirstOrDefaultAsync(i => i.Name == "Live");

                                            var stagingChange = new StagingChange
                                            {
                                                Name = "SRN Status",
                                                OldValue = srn.SRNStatus.Name,
                                                NewValue = newSrnStatus.Name
                                            };

                                            stagingChangeLog.Changes.Add(stagingChange);

                                            srn.SRNStatusId = newSrnStatus.Id;
                                            srn.StatusLastUpdatedAt = DateTime.Now;
                                        }

                                        _dbContext.SaveChanges();


                                        var changeBlob = JsonConvert.SerializeObject(stagingChangeLog);

                                        Helpers.Helpers.CreateEventLog(_dbContext, user.Id, "SRN Split", srn.TradingName, entityBlob, changeBlob, srn.Id, "SRN");
                                    }
                                }
                            }
                        }

                    }

                    else if (requestType == "merge")
                    {
                        var srnIDVariable = currentTaskVariables.FirstOrDefault(i => i.Name == "MergeToSRNId");

                        if (srnIDVariable != null)
                            srnId = Convert.ToInt32(srnIDVariable.Value);

                        if (srnId > 0)
                        {
                            var srn = await _dbContext.Set<SRN>()
                                .Include(i => i.SRNStatus)
                                .Include(i => i.SRNStatusUpdates)
                                .FirstOrDefaultAsync(i => i.Id == srnId);

                            var recentSRNStatusUpdate = srn.SRNStatusUpdates.FirstOrDefault(i => i.ProcessInstanceId == task.ProcessInstanceId);

                            var srnUpdateResource = _mapper.Map<SRNUpdateResource>(srn);
                            var entityBlob = JsonConvert.SerializeObject(srnUpdateResource);

                            var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext);

                            var stagingChangeLog = new MemberStagingChangeLogResource();


                            if (confirmation.GoLiveDate != null && recentSRNStatusUpdate.FileType == SRNStatusFileTypes.DailyFile)
                            {
                                var stagingChange = new StagingChange
                                {
                                    Name = "Daily Go Live Date",
                                    OldValue = string.Format("{0:yyyy-MM-dd}", confirmation.GoLiveDate.Value),
                                    NewValue = (confirmation.GoLiveDate != null) ? confirmation.GoLiveDate.Value.ToString("yyyy-MM-dd") : ""
                                };

                                stagingChangeLog.Changes.Add(stagingChange);

                                recentSRNStatusUpdate.DailyFileGoLiveDate = confirmation.GoLiveDate;

                            }
                            else if (confirmation.GoLiveDate != null && recentSRNStatusUpdate.FileType == SRNStatusFileTypes.MonthlyFile)
                            {
                                var stagingChange = new StagingChange
                                {
                                    Name = "Monthly Go Live Date",
                                    OldValue = string.Format("{0:yyyy-MM-dd}", confirmation.GoLiveDate.Value),
                                    NewValue = (confirmation.GoLiveDate != null) ? confirmation.GoLiveDate.Value.ToString("yyyy-MM-dd") : ""
                                };

                                stagingChangeLog.Changes.Add(stagingChange);

                                recentSRNStatusUpdate.MonthlyFileGoLiveDate = confirmation.GoLiveDate;
                            }
                            if (confirmation.IsLiveComplete == ConfirmSRNGoLiveEnum.Yes)
                            {
                                var newSrnStatus = await _dbContext.SRNStatuses
                                    .FirstOrDefaultAsync(i => i.Name == "Live");

                                var stagingChange = new StagingChange
                                {
                                    Name = "SRN Status",
                                    OldValue = srn.SRNStatus.Name,
                                    NewValue = newSrnStatus.Name
                                };

                                stagingChangeLog.Changes.Add(stagingChange);

                                srn.SRNStatusId = newSrnStatus.Id;
                                srn.StatusLastUpdatedAt = DateTime.Now;
                            }

                            _dbContext.SaveChanges();


                            var changeBlob = JsonConvert.SerializeObject(stagingChangeLog);

                            Helpers.Helpers.CreateEventLog(_dbContext, user.Id, "SRN Merge", srn.TradingName, entityBlob, changeBlob, srn.Id, "SRN");
                        }
                    }
                }
            }
        }

        public void UpdateSRNDates(SRN srn, TaskBuyerSHMReviewSRNSale taskReview, MemberStagingChangeLogResource stagingChangeLog)
        {
            if (srn != null)
            {
                var recentSRNStatusUpdate = GetRecentSRNStatusUpdateHistory(srn.SRNStatusUpdates);

                #region Daily File Dates
                if (taskReview.DailyFileDevelopmentStartDate != null)
                {
                    srn.SRNStatusUpdates.OrderByDescending(i => i.DateCreated).First().DailyFileDevelopmentStartDate = taskReview.DailyFileDevelopmentStartDate;

                    var stagingChange = new StagingChange
                    {
                        Name = "Daily File Development Start Date",
                        OldValue = (recentSRNStatusUpdate.DailyFileDevelopmentStartDate != null) ? recentSRNStatusUpdate.DailyFileDevelopmentStartDate.ToString() : "",
                        NewValue = taskReview.DailyFileDevelopmentStartDate.ToString()
                    };

                    stagingChangeLog.Changes.Add(stagingChange);
                }
                if (taskReview.DailyFileDevelopmentEndDate != null)
                {
                    srn.SRNStatusUpdates.OrderByDescending(i => i.DateCreated).First().DailyFileDevelopmentEndDate = taskReview.DailyFileDevelopmentEndDate;

                    var stagingChange = new StagingChange
                    {
                        Name = "Daily File Development End Date",
                        OldValue = (recentSRNStatusUpdate.DailyFileDevelopmentEndDate != null) ? recentSRNStatusUpdate.DailyFileDevelopmentEndDate.ToString() : "",
                        NewValue = taskReview.DailyFileDevelopmentEndDate.ToString()
                    };

                    stagingChangeLog.Changes.Add(stagingChange);
                }

                if (taskReview.DailyFileTestStartDate != null)
                {
                    srn.SRNStatusUpdates.OrderByDescending(i => i.DateCreated).First().DailyFileTestStartDate = taskReview.DailyFileTestStartDate;

                    var stagingChange = new StagingChange
                    {
                        Name = "Daily File Test Start Date",
                        OldValue = (recentSRNStatusUpdate.DailyFileTestStartDate != null) ? recentSRNStatusUpdate.DailyFileTestStartDate.ToString() : "",
                        NewValue = taskReview.DailyFileTestStartDate.ToString()
                    };

                    stagingChangeLog.Changes.Add(stagingChange);
                }
                if (taskReview.DailyFileTestEndDate != null)
                {
                    srn.SRNStatusUpdates.OrderByDescending(i => i.DateCreated).First().DailyFileTestEndDate = taskReview.DailyFileTestEndDate;

                    var stagingChange = new StagingChange
                    {
                        Name = "Daily File Test End Date",
                        OldValue = (recentSRNStatusUpdate.DailyFileTestEndDate != null) ? recentSRNStatusUpdate.DailyFileTestEndDate.ToString() : "",
                        NewValue = taskReview.DailyFileTestEndDate.ToString()
                    };

                    stagingChangeLog.Changes.Add(stagingChange);
                }

                if (taskReview.DailyFileGoLiveDate != null)
                {
                    srn.SRNStatusUpdates.OrderByDescending(i => i.DateCreated).First().DailyFileGoLiveDate = taskReview.DailyFileGoLiveDate;

                    var stagingChange = new StagingChange
                    {
                        Name = "Daily File Go Live Date",
                        OldValue = (recentSRNStatusUpdate.DailyFileGoLiveDate != null) ? recentSRNStatusUpdate.DailyFileGoLiveDate.ToString() : "",
                        NewValue = taskReview.DailyFileGoLiveDate.ToString()
                    };

                    stagingChangeLog.Changes.Add(stagingChange);
                }

                #endregion

                #region Monthly File Dates
                if (taskReview.MonthlyFileDevelopmentStartDate != null)
                {
                    srn.SRNStatusUpdates.OrderByDescending(i => i.DateCreated).First().MonthlyFileDevelopmentStartDate = taskReview.MonthlyFileDevelopmentStartDate;

                    var stagingChange = new StagingChange
                    {
                        Name = "Monthly File Development Start Date",
                        OldValue = (recentSRNStatusUpdate.MonthlyFileDevelopmentStartDate != null) ? recentSRNStatusUpdate.MonthlyFileDevelopmentStartDate.ToString() : "",
                        NewValue = taskReview.MonthlyFileDevelopmentStartDate.ToString()
                    };

                    stagingChangeLog.Changes.Add(stagingChange);
                }
                if (taskReview.MonthlyFileDevelopmentEndDate != null)
                {
                    srn.SRNStatusUpdates.OrderByDescending(i => i.DateCreated).First().MonthlyFileDevelopmentEndDate = taskReview.MonthlyFileDevelopmentEndDate;

                    var stagingChange = new StagingChange
                    {
                        Name = "Monthly File Development End Date",
                        OldValue = (recentSRNStatusUpdate.MonthlyFileDevelopmentEndDate != null) ? recentSRNStatusUpdate.MonthlyFileDevelopmentEndDate.ToString() : "",
                        NewValue = taskReview.MonthlyFileDevelopmentEndDate.ToString()
                    };

                    stagingChangeLog.Changes.Add(stagingChange);
                }

                if (taskReview.MonthlyFileTestStartDate != null)
                {
                    srn.SRNStatusUpdates.OrderByDescending(i => i.DateCreated).First().MonthlyFileTestStartDate = taskReview.MonthlyFileTestStartDate;

                    var stagingChange = new StagingChange
                    {
                        Name = "Monthly File Test Start Date",
                        OldValue = (recentSRNStatusUpdate.MonthlyFileTestStartDate != null) ? recentSRNStatusUpdate.MonthlyFileTestStartDate.ToString() : "",
                        NewValue = taskReview.MonthlyFileTestStartDate.ToString()
                    };

                    stagingChangeLog.Changes.Add(stagingChange);
                }
                if (taskReview.MonthlyFileTestEndDate != null)
                {
                    srn.SRNStatusUpdates.OrderByDescending(i => i.DateCreated).First().MonthlyFileTestEndDate = taskReview.MonthlyFileTestEndDate;

                    var stagingChange = new StagingChange
                    {
                        Name = "Monthly File Test End Date",
                        OldValue = (recentSRNStatusUpdate.MonthlyFileTestEndDate != null) ? recentSRNStatusUpdate.MonthlyFileTestEndDate.ToString() : "",
                        NewValue = taskReview.MonthlyFileTestEndDate.ToString()
                    };

                    stagingChangeLog.Changes.Add(stagingChange);
                }

                if (taskReview.MonthlyFileGoLiveDate != null)
                {
                    srn.SRNStatusUpdates.OrderByDescending(i => i.DateCreated).First().MonthlyFileGoLiveDate = taskReview.MonthlyFileGoLiveDate;

                    var stagingChange = new StagingChange
                    {
                        Name = "Monthly File Go Live Date",
                        OldValue = (recentSRNStatusUpdate.MonthlyFileGoLiveDate != null) ? recentSRNStatusUpdate.MonthlyFileGoLiveDate.ToString() : "",
                        NewValue = taskReview.MonthlyFileGoLiveDate.ToString()
                    };

                    stagingChangeLog.Changes.Add(stagingChange);
                }
                #endregion
            }
        }

        public void CompleteStakeholderManagerConfirmSRNFileSubmission(string taskId)
        {
            using (var client = new HttpClient())
            {
                /* Update the SRN Status Reason */
                var task = GetTask(taskId);
                var currentTaskVariables = GetVariables(task.ProcessInstanceId);

                var srnStatusReasonVariable = currentTaskVariables.FirstOrDefault(i => i.Name == "SRNStatusReasonId");

                if (srnStatusReasonVariable != null)
                {
                    int srnStatusReasonId = 0;
                    if (!string.IsNullOrEmpty(srnStatusReasonVariable.Value))
                        srnStatusReasonId = Convert.ToInt32(srnStatusReasonVariable.Value);

                    if (srnStatusReasonId > 0)
                    {
                        var srnIDVariable = currentTaskVariables.FirstOrDefault(i => i.Name == "SRNId");

                        var srnId = 0;
                        if (srnIDVariable != null)
                            srnId = Convert.ToInt32(srnIDVariable.Value);

                        if (srnId > 0)
                        {
                            var srn = await _dbContext.Set<SRN>()
                                .AsNoTracking()
                                .FirstOrDefaultAsync(i => i.Id == srnId);

                            if (srn != null)
                            {
                                srn.SRNStatusReasonId = srnStatusReasonId;
                                _dbContext.Update(srn);
                                _dbContext.SaveChanges();
                            }
                        }
                    }
                }

                var newTaskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                            {
                                {
                                    "variables",
                                    new Dictionary<string, Dictionary<string, string>>{}
                                }
                            };

                var json = JsonConvert.SerializeObject(newTaskVariables);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });
                result.EnsureSuccessStatusCode();
            }
        }

        private SRNStatusUpdateHistory GetRecentSRNStatusUpdateHistory(
            ICollection<SRNStatusUpdateHistory> srnStatusUpdates)
        {
            if (srnStatusUpdates != null)
            {
                if (srnStatusUpdates.Count > 0)
                {
                    srnStatusUpdates = srnStatusUpdates.OrderByDescending(i => i.DateCreated).ToList();
                    return srnStatusUpdates.First();
                }
            }

            return new SRNStatusUpdateHistory();
        }
        public void CreateTaskForDWException(List<DWExceptionExternalGetResource> exceptions)
        {
            if (exceptions != null)
            {
                foreach (var exception in exceptions)
                {
                    string shmId = "";

                    //Here we get the SRN from the Membership DB so we can allocate the SHM based on the SRN number
                    if (!string.IsNullOrEmpty(exception.SRNNumber))
                    {
                        var srn = await _dbContext.Set<SRN>()
                            .Include(i => i.Member)
                            .FirstOrDefaultAsync(i => i.SRNNumber == exception.SRNNumber.Trim());

                        if (srn != null)
                        {
                            if (srn.Member.StakeholderManagerId > 0)
                            {
                                shmId = srn.Member.StakeholderManagerId.ToString();
                            }
                            else
                            {
                                shmId = GetSHMIDByBureaName(exception.BureauName);
                            }
                        }
                        else
                        {
                            shmId = GetSHMIDByBureaName(exception.BureauName);
                        }
                    }
                    else if (string.IsNullOrEmpty(exception.SRNNumber))
                    {
                        shmId = GetSHMIDByBureaName(exception.BureauName);
                    }

                    if (!string.IsNullOrEmpty(shmId))
                    {
                        var exceptionExists = _dWExceptionRepository.DWExceptionExists(exception.FctWarehouseExceptionID);

                        //Only create camunda task if the DW exception does not exist
                        if (!exceptionExists)
                        {
                            var task = _camundaClient.ProcessDefinitions.ByKey("New-DW-Exception").StartProcessInstance(new StartProcessInstance()
                            {
                                Variables = new Dictionary<string, VariableValue>()
                                    {
                                        { "FctWarehouseExceptionID", VariableValue.FromObject(exception.FctWarehouseExceptionID) },
                                        { "stakeHolderManagerAssignee", VariableValue.FromObject(shmId) }
                                    }
                            });

                            var dwException = _mapper.Map<DWExceptionCreateResource>(exception);
                            dwException.CamundaTaskId = task.Id;
                            dwException.IsSentToPortal = "YES";

                            _dWExceptionRepository.Create(dwException);

                            var statusId = "";
                            if (dwException.ExceptionStatus == "Open")
                                statusId = "1";
                            else if (dwException.ExceptionStatus == "Closed")
                                statusId = "2";

                            var updateResource = new DWAExternalPIExceptionUpdateResource
                            {
                                ColumnsKeyValuePair = new Dictionary<string, string>
                                        {
                                            { "FctWarehouseExceptionID", dwException.FctWarehouseExceptionID.ToString() },
                                            { "DimExceptionStatusID", statusId },
                                            { "CamundaID", task.Id },
                                            { "UpdatedBy", "Membership API" },
                                            { "DateUpdated", DateTime.Now.ToString() }
                                        }
                            };


                            var apiResult = UpdateDWExternalAPI(updateResource);
                        }
                    }
                }
            }
        }
        public void CompleteStakeHolderManagerDWException(string taskId, TaskCompleteDWExceptionResource taskReview)
        {
            using (var client = new HttpClient())
            {
                var task = GetTask(taskId);
                var currentTaskVariables = GetVariables(task.ProcessInstanceId);

                var newTaskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                            {
                                {
                                    "variables",
                                    new Dictionary<string, Dictionary<string, string>>{}
                                }
                            };

                var json = JsonConvert.SerializeObject(newTaskVariables);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });
                result.EnsureSuccessStatusCode();


                var fctWarehouseExceptionIDVariable = currentTaskVariables.FirstOrDefault(i => i.Name == "FctWarehouseExceptionID");
                long fctWarehouseExceptionID = 0;

                if (fctWarehouseExceptionIDVariable != null)
                    fctWarehouseExceptionID = Convert.ToInt64(fctWarehouseExceptionIDVariable.Value);

                if (fctWarehouseExceptionID > 0)
                {
                    var updatedDWException = _dWExceptionRepository.CloseException(fctWarehouseExceptionID, taskReview.Comments);

                    var statusId = "";
                    if (updatedDWException.ExceptionStatus == "Open")
                        statusId = "1";
                    else if (updatedDWException.ExceptionStatus == "Closed")
                        statusId = "2";

                    var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext);

                    var updateResource = new DWAExternalPIExceptionUpdateResource
                    {
                        ColumnsKeyValuePair = new Dictionary<string, string>
                                        {
                                            { "FctWarehouseExceptionID", updatedDWException.FctWarehouseExceptionID.ToString() },
                                            { "DimExceptionStatusID", statusId },
                                            { "UpdateComment", updatedDWException.Comments },
                                            { "CamundaID", task.Id },
                                            { "UpdatedBy", user.FullName },
                                            { "DateUpdated", DateTime.Now.ToString() }
                                        }
                    };

                    var apiResult = UpdateDWExternalAPI(updateResource);
                }
            }
        }

        private int UpdateDWExternalAPI(DWAExternalPIExceptionUpdateResource updateResouce)
        {
            string requestUrlString = $"Updates";
            string tableName = "API.FctWarehouseExceptionUpdate";

            var restClient = Helpers.Helpers.GetRestClient(_dwBaseApiUrl);

            var request = new RestRequest(requestUrlString, Method.Put);
            request.AddParameter("apiKey", _dwApiKey, ParameterType.QueryString);
            request.AddParameter("datasetName", _dwDataset, ParameterType.QueryString);
            request.AddParameter("tableName", tableName, ParameterType.QueryString);
            request.AddJsonBody(updateResouce);

            var restResponse = await restClient.ExecuteAsync<int>(request);
            Helpers.Helpers.CheckForAPIRequestError(restResponse);

            return restResponse.Data;
        }

        public void DeleteProcessInstance(string processInstanceId)
        {
            using (var client = new HttpClient())
            {
                var content = new StringContent("", Encoding.UTF8, "application/json");
                var uri = _configSettings.CamundaBaseAddress + "/process-instance/" + processInstanceId;
                var result = client.DeleteAsync(uri).Result;
                result.EnsureSuccessStatusCode();
            }
        }

        private string GetSHMIDByBureaName(string bureauName)
        {
            if (!string.IsNullOrWhiteSpace(bureauName))
            {
                var member = await _dbContext.Members
                    .Where(i => i.RegisteredName.Trim() == bureauName.Trim())
                    .Select(m => new Member
                    {
                        StakeholderManagerId = m.StakeholderManagerId
                    })
                    .FirstOrDefaultAsync();

                if (member != null)
                {
                    if (member.StakeholderManagerId > 0)
                    {
                        return member.StakeholderManagerId.ToString();
                    }
                }
            }

            return null;
        }

        public void ExecuteTimerEvents(List<string> processInstanceIds)
        {
            try
            {
                if (processInstanceIds != null)
                {
                    if (processInstanceIds.Count > 0)
                    {
                        foreach (var procInstanceId in processInstanceIds)
                        {
                            using (var client = new HttpClient())
                            {
                                var uri = _configSettings.CamundaBaseAddress + "/job?processInstanceId=" + procInstanceId;
                                var result = client.Send(new HttpRequestMessage(HttpMethod.Get, uri));
                                result.EnsureSuccessStatusCode();

                                if (result != null)
                                {
                                    var jobsArray = JArray.Parse(result.Content.ReadAsString());

                                    if (jobsArray.Count > 0)
                                    {
                                        JToken job = jobsArray.FirstOrDefault();
                                        var jobId = JObject.Parse(job.ToString())["id"].ToString();

                                        if (!string.IsNullOrWhiteSpace(jobId))
                                        {
                                            ExcecuteJob(jobId);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Helpers.Helpers.LogError(_dbContext, ex, "Failed to execute timer events.");
            }
        }

        private void ExcecuteJob(string jobId)
        {
            if (!string.IsNullOrWhiteSpace(jobId))
            {
                using (var client = new HttpClient())
                {
                    var content = new StringContent("", Encoding.UTF8, "application/json");

                    var uri = _configSettings.CamundaBaseAddress + "/job/" + jobId + "/execute";
                    var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });
                    result.EnsureSuccessStatusCode();
                }
            }
        }

        public async Task<List<UserCamundaGetResource>> GetUsersForCamunda()
        {
            List<User> users = await _dbContext.Users
                .Where(i => i.RoleId == UserRoles.FinancialAdministrator || i.RoleId == UserRoles.SACRRAAdministrator
                    || i.RoleId == UserRoles.StakeHolderAdministrator || i.RoleId == UserRoles.StakeHolderManager
                    || i.RoleId == UserRoles.SystemAdministrator)
                .Select(m => new User
                {
                    Id = m.Id,
                    FirstName = m.FirstName,
                    LastName = m.LastName,
                    Email = m.Email,
                    RoleId = m.RoleId
                })
                .ToListAsync();

            var camundaUsers = _mapper.Map<List<UserCamundaGetResource>>(users);
            return camundaUsers;
        }

        public List<string> Groups()
        {
            List<string> groups = new List<string>();
            var roles = Enum.GetValues<UserRoles>()
                .Where(i => i.Equals(UserRoles.FinancialAdministrator) || i.Equals(UserRoles.SACRRAAdministrator)
                    || i.Equals(UserRoles.StakeHolderAdministrator) || i.Equals(UserRoles.StakeHolderManager)
                    || i.Equals(UserRoles.SystemAdministrator))
                .ToList();

            foreach (var role in roles)
            {
                groups.Add(role.ToString());
            }

            return groups;
        }

        public List<CamundaErrorRecipient> GetCamundaErrorRecipients()
        {
            var recipients = _dbContext.CamundaErrorRecipients.ToList();
            return recipients;
        }

        private void CreateMemberStatusUpdateEventLog(Member member, MemberStagingChangeLogResource stagingChangeLog, bool isSystemUser = false)
        {
            var updateDetailsBlob = JsonConvert.SerializeObject(member, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore });

            var stagingDetailsBlob = JsonConvert.SerializeObject(stagingChangeLog);

            if (stagingChangeLog.Changes.Count > 0)
            {
                var userId = 0;
                var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext, isSystemUser);
                if (user != null)
                {
                    userId = user.Id;
                }

                await Helpers.Helpers
                    .CreateEventLog(_dbContext, userId, "Member Update", member.RegisteredName, updateDetailsBlob, stagingDetailsBlob, member.Id, "Member");
            }
        }

        public void SendEmail(string email, string recepientName, string subject, string messageBody)
        {
            _emailService.SendEmail(email, recepientName, subject, messageBody);
        }

        public void HandleExternalTaskFailure(string externalTaskId, string workerId, string errorMessage, int retries, int retryTimeout)
        {
            var requestBody = new
            {
                workerId = workerId,
                errorMessage = errorMessage,
                retries = retries,
                retryTimeout = retryTimeout
            };

            var restClient = new RestClient(_configSettings.CamundaBaseAddress);

            var request = new RestRequest($"/external-task/{externalTaskId}/failure", Method.Post);
            request.AddJsonBody(requestBody);

            var restResponse = restClient.ExecuteAsync(request);
        }
    }
}
