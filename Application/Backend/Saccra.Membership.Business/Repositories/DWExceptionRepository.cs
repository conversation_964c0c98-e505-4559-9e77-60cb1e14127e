using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Sacrra.Membership.Business.Resources;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Sacrra.Membership.Business.Repositories
{
    public class DWExceptionRepository
    {
        private readonly AppDbContext _dbContext;
        public IMapper _mapper { get; }

        public DWExceptionRepository(AppDbContext dbContext, IMapper mapper)
        {
            this._mapper = mapper;
            this._dbContext = dbContext;
        }

        public DWExceptionGetResource Get(int id)
        {
            var selectRecord = await _dbContext.Set<DWException>()
                    .AsNoTracking()
                .FirstOrDefaultAsync(s => s.Id == id);

            var returnRecord = _mapper.Map<DWExceptionGetResource>(selectRecord);

            return returnRecord;
        }
        public DWExceptionGetResource GetByDWExceptionId(long dwExceptionId)
        {
            var selectRecord = await _dbContext.Set<DWException>()
                    .AsNoTracking()
                .FirstOrDefaultAsync(s => s.FctWarehouseExceptionID == dwExceptionId);

            var returnRecord = _mapper.Map<DWExceptionGetResource>(selectRecord);

            return returnRecord;
        }

        public bool DWExceptionExists(long dwExceptionId)
        {
            var selectRecord = await _dbContext.Set<DWException>()
                .AsNoTracking()
                .Select(m => new DWException
                {
                    FctWarehouseExceptionID = m.FctWarehouseExceptionID
                })
                .FirstOrDefaultAsync(s => s.FctWarehouseExceptionID == dwExceptionId);

            if (selectRecord != null)
                return true;
            else
                return false;
        }

        public async Task<List<DWExceptionGetResource>> List(ExceptionFilterResource filter)
        {
            var query = _dbContext.DWExceptions.AsQueryable();

            if(filter != null)
            {
                if (!string.IsNullOrEmpty(filter.Exception))
                    query = query.Where(i => i.Exception == filter.Exception);

                if (!string.IsNullOrEmpty(filter.ExceptionCategory))
                    query = query.Where(i => i.ExceptionCategory == filter.ExceptionCategory);

                if (filter.ExceptionDateTime != DateTime.MinValue)
                    query = query.Where(i => i.ExceptionDateTime.Date == filter.ExceptionDateTime.Date);

                if (!string.IsNullOrEmpty(filter.ExceptionDesc))
                    query = query.Where(i => i.ExceptionDesc == filter.ExceptionDesc);

                if (!string.IsNullOrEmpty(filter.ExceptionStatus))
                    query = query.Where(i => i.ExceptionStatus == filter.ExceptionStatus);

                if (filter.FctWarehouseExceptionID > 0)
                    query = query.Where(i => i.FctWarehouseExceptionID == filter.FctWarehouseExceptionID);

                if (!string.IsNullOrEmpty(filter.IsSentToPortal))
                    query = query.Where(i => i.IsSentToPortal == filter.IsSentToPortal);

                if (!string.IsNullOrEmpty(filter.SRNNumber))
                    query = query.Where(i => i.SRNNumber == filter.SRNNumber);
            }

            var exceptions = _mapper.Map<List<DWExceptionGetResource>>(query.ToList());

            return exceptions;

        }
        public DWExceptionGetResource Update(DWExceptionUpdateResource modelForUpdate)
        {
            var model = _mapper.Map<DWException>(modelForUpdate);

            _dbContext.Set<DWException>().Update(model);

            _dbContext.SaveChanges();

            return Get(model.Id);
        }
        public DWExceptionGetResource CloseException(long fctWarehouseExceptionID, string comments)
        {
            var model = await _dbContext.Set<DWException>()
                .FirstOrDefaultAsync(i => i.FctWarehouseExceptionID == fctWarehouseExceptionID);

            model.Comments = comments;
            model.ExceptionStatus = "Closed";

            _dbContext.SaveChanges();

            return Get(model.Id);
        }

        public int Create(DWExceptionCreateResource modelForCreate)
        {
            var model = _mapper.Map<DWException>(modelForCreate);
            model.DatePulled = DateTime.Now;

            await _dbContext.Set<DWException>().AddAsync(model);

            _dbContext.SaveChanges();

            return model.Id;
        }

        public void Create(List<DWExceptionCreateResource> itemsToCreate)
        {
            foreach(var modelForCreate in itemsToCreate)
            {
                var model = _mapper.Map<DWException>(modelForCreate);
                model.DatePulled = DateTime.Now;

                _dbContext.Set<DWException>().Add(model);
            }
            
            _dbContext.SaveChanges();
        }
        public void Delete(int id)
        {
            var entity = await _dbContext.Set<DWException>().FindAsync(id);

            _dbContext.Set<DWException>().Remove(entity);
            _dbContext.SaveChanges();
        }
    }
}

