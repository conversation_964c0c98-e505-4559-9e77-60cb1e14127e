using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.ListParams;
using Sacrra.Membership.Business.Resources;
using Sacrra.Membership.Business.Resources.IdValuePair;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Models;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Sacrra.Membership.Business.Repositories
{
    public class TradingNameRepository
    {
        private readonly AppDbContext _dbContext;
        public IMapper _mapper { get; }

        public TradingNameRepository(AppDbContext dbContext, IMapper mapper)
        {
            this._mapper = mapper;
            this._dbContext = dbContext;
        }

        public TradingNameGetResource Get(int id)
        {
            var selectRecord = await _dbContext.Set<TradingName>()
                .AsNoTracking()
                .FirstOrDefaultAsync(s => s.Id == id);

            var returnRecord = _mapper.Map<TradingNameGetResource>(selectRecord);

            return returnRecord;
        }

        public async Task<PagedList<IdValuePairResource>> List(NameListParams listParams, ClaimsPrincipal currentUser)
        {
            var query = _dbContext.Set<TradingName>()
                    .AsQueryable();
            var user = Helpers.Helpers.GetLoggedOnUser(_dbContext, currentUser);
            var algLeaders = _dbContext.Set<ALG>().ToList();

            if (listParams != null)
            {
                if (listParams.Name != null)
                    query = query.Where(u => u.Name.ToLower().Contains(listParams.Name.ToLower()));
            }
            if (listParams.SortDirection == "asc")
            {
                switch (listParams.SortBy)
                {
                    default:
                        query = query.OrderBy(u => u.Name);
                        break;
                }
            }
            else
            {
                switch (listParams.SortBy)
                {
                    default:
                        query = query.OrderByDescending(u => u.Name);
                        break;
                }
            }

            var count = query.Count();
            var pageNumber = listParams.PageNumber;
            if (count / listParams.PageSize < listParams.PageNumber)
                pageNumber = (count / listParams.PageSize) + 1;

            var queryItems = query.Skip((pageNumber - 1) * listParams.PageSize).Take(listParams.PageSize).ToList();

            // This part is only for ALG Leaders. This is used to check if the trading name provided
            // is already linked to an SRN that belongs to another ALG Leader.
            if (user.RoleId == Database.Enums.UserRoles.ALGLeader)
            {
                var newQueryItems = new List<TradingName>();

                foreach (var algLeader in algLeaders)
                {
                    var foundItem = queryItems.Find(tradingName => tradingName.MemberId == algLeader.Id);

                    if (foundItem != null)
                    {
                        newQueryItems.Add(foundItem);
                    }
                }

                queryItems = newQueryItems;
            }

            var itemsToReturn = _mapper.Map<IEnumerable<IdValuePairResource>>(queryItems).ToList();

            return new PagedList<IdValuePairResource>(itemsToReturn, count, pageNumber, listParams.PageSize);
        }
        public TradingNameGetResource Update(TradingNameUpdateResource modelForUpdate)
        {
            var model = _mapper.Map<TradingName>(modelForUpdate);

            _dbContext.Set<TradingName>().Update(model);

            _dbContext.SaveChanges();

            return Get(model.Id);
        }

        public int Create(TradingNameCreateResource modelForCreate)
        {
            var model = _mapper.Map<TradingName>(modelForCreate);

            await _dbContext.Set<TradingName>().AddAsync(model);

            _dbContext.SaveChanges();

            return model.Id;
        }
        public void Delete(int id)
        {
            var entity = await _dbContext.Set<TradingName>().FindAsync(id);

            _dbContext.Set<TradingName>().Remove(entity);
            _dbContext.SaveChanges();
        }
    }
}

