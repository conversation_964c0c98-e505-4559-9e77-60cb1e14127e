using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using Sacrra.Membership.Business.Exceptions;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.Resources;
using Sacrra.Membership.Business.Resources.Camunda.Task;
using Sacrra.Membership.Business.Resources.MemberChangeRequest;
using Sacrra.Membership.Business.Resources.SRN;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace Sacrra.Membership.Business.Extensions
{
    public class SRNExtensions
    {
        private readonly AppDbContext _dbContext;
        private readonly ConfigSettings _configSettings;
        private readonly IMapper _mapper;

        public SRNExtensions(AppDbContext dbContext, IMapper mapper, IOptions<ConfigSettings> configSettings)
        {
            _dbContext = dbContext;
            _configSettings = configSettings.Value;
            _mapper = mapper;
        }

        public void RequestMergeSplitSellSRN(SRNMergeSplitSellRequestResource request)
        {
            if (request != null)
            {
                string requestType = "";
                string saleType = "";
                string shmId = "";
                string srnIdMergeList = "";
                string srnIdSplitList = "";

                if (request.SaleType == SRNSaleType.Full)
                    saleType = "full";
                else if (request.SaleType == SRNSaleType.Partial)
                    saleType = "partial";

                if (request.RequestType == SRNRequestType.Sale)
                {
                    requestType = "sale";

                    if (request.SRNIdToBeSold <= 0)
                        throw new InvalidSRNSaleException();

                    var srn = await _dbContext.SRNs
                        .Include(i => i.Member)
                            .ThenInclude(i => i.StakeholderManager)
                        .FirstOrDefaultAsync(i => i.Id == request.SRNIdToBeSold);

                    if (srn != null)
                    {
                        if (srn.Member != null)
                        {
                            if (srn.Member.StakeholderManager != null)
                            {
                                shmId = (srn.Member.StakeholderManager.Id > 0) ? srn.Member.StakeholderManager.Id.ToString() : "";
                            }
                        }
                    }

                    if (string.IsNullOrEmpty(shmId))
                        throw new InvalidSRNSaleNoSHMException();
                }

                else if (request.RequestType == SRNRequestType.Merge)
                {
                    requestType = "merge";
                    saleType = "full"; //A merge will always be a full merge

                    var srn = await _dbContext.SRNs
                        .Include(i => i.Member)
                            .ThenInclude(i => i.StakeholderManager)
                        .FirstOrDefaultAsync(i => i.Id == request.MergeToSRNId);

                    if (srn != null)
                    {
                        if (srn.Member != null)
                        {
                            if (srn.Member.StakeholderManager != null)
                            {
                                shmId = (srn.Member.StakeholderManager.Id > 0) ? srn.Member.StakeholderManager.Id.ToString() : "";
                            }
                        }
                    }

                    if (string.IsNullOrEmpty(shmId))
                        throw new InvalidSRNMergeNoSHMException();

                    if (request.SRNIdMergeFromList.Count == 0)
                        throw new InvalidSRNMergeNoMergeListException();

                    foreach (var srnId in request.SRNIdMergeFromList)
                    {
                        srnIdMergeList += srnId + ",";

                        var mergeRequest = new SRNMergeRequest
                        {
                            RequestDate = DateTime.Now,
                            FromSRNId = srnId,
                            ToSRNId = request.MergeToSRNId,
                            Status = SRNMergeStatus.Requested
                        };

                        await _dbContext.Set<SRNMergeRequest>().AddAsync(mergeRequest);
                    }

                    if (!string.IsNullOrEmpty(srnIdMergeList))
                        srnIdMergeList = srnIdMergeList.TrimEnd(',');
                }
                else if (request.RequestType == SRNRequestType.Split)
                {
                    requestType = "split";

                    var srn = await _dbContext.SRNs
                        .Include(i => i.Member)
                            .ThenInclude(i => i.StakeholderManager)
                        .FirstOrDefaultAsync(i => i.Id == request.SplitFromSRNId);

                    if (srn != null)
                    {
                        if (srn.Member != null)
                        {
                            if (srn.Member.StakeholderManager != null)
                            {
                                shmId = (srn.Member.StakeholderManager.Id > 0) ? srn.Member.StakeholderManager.Id.ToString() : "";
                            }
                        }
                    }

                    if (string.IsNullOrEmpty(shmId))
                        throw new InvalidSRNSplitNoSHMException();

                    if (request.SRNIdSplitList.Count == 0)
                        throw new InvalidSRNSplitNoSplitListException();
                    foreach (var srnId in request.SRNIdSplitList)
                    {
                        srnIdSplitList += srnId + ",";

                        var splitRequest = new SRNSplitRequest
                        {
                            RequestDate = DateTime.Now,
                            FromSRNId = request.SplitFromSRNId,
                            ToSRNId = srnId,
                            Status = SRNSplitStatus.Requested,
                            Type = (saleType == "partial") ? SRNSplitType.Partial : SRNSplitType.Full
                        };

                        await _dbContext.Set<SRNSplitRequest>().AddAsync(splitRequest);
                    }

                    if (!string.IsNullOrEmpty(srnIdSplitList))
                        srnIdSplitList = srnIdSplitList.TrimEnd(',');
                }

                _dbContext.SaveChanges();

                var taskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                        {
                            {
                                "variables",
                                new Dictionary<string, Dictionary<string, string>>
                                {
                                    {
                                        "RequestType",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", requestType },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "SaleType",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", saleType },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "SRNIdToBeSold",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", request.SRNIdToBeSold.ToString() },
                                            { "type", "Long" }
                                        }
                                    },
                                    {
                                        "BuyerRegisteredName",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", request.BuyerRegisteredName },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "BuyerRegisteredNumber",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", request.BuyerRegisteredNumber },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "SRNIdMergeList",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srnIdMergeList },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "SRNIdSplitList",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srnIdSplitList },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "SplitFromSRNId",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", request.SplitFromSRNId.ToString() },
                                            { "type", "Long" }
                                        }
                                    },
                                    {
                                        "MergeToSRNId",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", request.MergeToSRNId.ToString() },
                                            { "type", "Long" }
                                        }
                                    },
                                    {
                                        "stakeHolderManagerAssignee",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", shmId },
                                            { "type", "String" }
                                        }
                                    }
                                }
                            }
                        };

                using (var client = new HttpClient())
                {
                    var contractResolver = new DefaultContractResolver
                    {
                        NamingStrategy = new CamelCaseNamingStrategy()
                    };
                    var json = JsonConvert.SerializeObject(taskVariables, new JsonSerializerSettings
                    {
                        ContractResolver = contractResolver,
                        Formatting = Formatting.Indented
                    });
                    var content = new StringContent(json, Encoding.UTF8, "application/json");
                    var uri = _configSettings.CamundaBaseAddress + "/process-definition/key/SRN-Split-Merge-Sell/start";
                    var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });
                    result.EnsureSuccessStatusCode();
                }
            }
        }

        public void RequestSRNSale(SRNSaleRequestResource request)
        {
            if (request != null)
            {
                string requestType = "";
                string saleType = "";
                string shmId = "";
                string srnIdMergeList = "";
                string srnIdSplitList = "";
                string newSRNStatus = "";

                if (request.SaleType == SRNSaleType.Full)
                {
                    saleType = "full";
                    newSRNStatus = "Sale In Progress - Full";
                }
                else if (request.SaleType == SRNSaleType.Partial)
                {
                    saleType = "partial";
                    newSRNStatus = "Sale In Progress - Partial";
                }

                requestType = "sale";

                if (request.SRNIdToBeSold <= 0)
                    throw new InvalidSRNSaleException();

                var srn = await _dbContext.Set<SRN>()
                    .Include(i => i.SRNStatus)
                    .Include(i => i.Member)
                        .ThenInclude(i => i.StakeholderManager)
                    .Include(i => i.Member)
                        .ThenInclude(i => i.Users)
                    .FirstOrDefaultAsync(i => i.Id == request.SRNIdToBeSold);

                var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext);
                if (!Helpers.Helpers.IsInternalSACRRAUser(user))
                {
                    if (srn != null)
                    {
                        if (!srn.Member.Users.Any(x => x.UserId == user.Id))
                            throw new UnauthorizedException();
                    }
                }

                if (srn != null)
                {
                    if (srn.Member != null)
                    {
                        if (srn.Member.StakeholderManager != null)
                        {
                            shmId = (srn.Member.StakeholderManager.Id > 0) ? srn.Member.StakeholderManager.Id.ToString() : "";
                        }
                    }
                }

                var oldStatusName = srn.SRNStatus.Name;

                if (string.IsNullOrEmpty(shmId))
                    throw new InvalidSRNSaleNoSHMException();

                var srnSale = new SRNSaleRequest
                {
                    SRNId = request.SRNIdToBeSold,
                    RequestDate = DateTime.Now,
                    Status = SRNSaleStatus.Requested,
                    SellerMemberId = srn.MemberId,
                    Type = request.SaleType
                };

                _dbContext.Add(srnSale);
                _dbContext.SaveChanges();

                var taskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                        {
                            {
                                "variables",
                                new Dictionary<string, Dictionary<string, string>>
                                {
                                    {
                                        "RequestType",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", requestType },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "SaleType",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", saleType },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "SRNIdToBeSold",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", request.SRNIdToBeSold.ToString() },
                                            { "type", "Long" }
                                        }
                                    },
                                    {
                                        "BuyerRegisteredName",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", request.BuyerRegisteredName },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "BuyerRegisteredNumber",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", request.BuyerRegisteredNumber },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "SRNIdMergeList",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srnIdMergeList },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "SRNIdSplitList",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srnIdSplitList },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "SplitFromSRNId",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", "0" },
                                            { "type", "Long" }
                                        }
                                    },
                                    {
                                        "MergeToSRNId",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", "0" },
                                            { "type", "Long" }
                                        }
                                    },
                                    {
                                        "stakeHolderManagerAssignee",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", shmId },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "SRNSaleRequestId",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srnSale.Id.ToString() },
                                            { "type", "Long" }
                                        }
                                    },
                                    {
                                        "InitialStatusId",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srn.SRNStatusId.ToString() },
                                            { "type", "Long" }
                                        }
                                    }
                                }
                            }
                        };

                using (var client = new HttpClient())
                {
                    var contractResolver = new DefaultContractResolver
                    {
                        NamingStrategy = new CamelCaseNamingStrategy()
                    };
                    var json = JsonConvert.SerializeObject(taskVariables, new JsonSerializerSettings
                    {
                        ContractResolver = contractResolver,
                        Formatting = Formatting.Indented
                    });
                    var content = new StringContent(json, Encoding.UTF8, "application/json");
                    var uri = _configSettings.CamundaBaseAddress + "/process-definition/key/SRN-Split-Merge-Sell/start";
                    var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });
                    result.EnsureSuccessStatusCode();
                }

                UpdateSRNStatus(srn, newSRNStatus);

                Helpers.Helpers.CreateSRNStatusEventLog(_dbContext, _mapper, oldStatusName, newSRNStatus, "SRN Sale", srn, user);
            }
        }

        public void RequestSRNMerge(SRNMergeRequestResource request)
        {
            if (request != null)
            {
                string requestType = "";
                string saleType = "";
                string shmId = "";
                string srnIdMergeList = "";

                requestType = "merge";
                saleType = "full"; //A merge will always be a full merge
                var intialStatusIds = "";

                var srn = await _dbContext.SRNs
                    .Include(i => i.SRNStatus)
                    .Include(i => i.Member)
                        .ThenInclude(i => i.StakeholderManager)
                    .Include(i => i.Member)
                        .ThenInclude(i => i.Users)
                    .FirstOrDefaultAsync(i => i.Id == request.MergeToSRNId);

                var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext);
                if (!Helpers.Helpers.IsInternalSACRRAUser(user))
                {
                    if (srn != null)
                    {
                        if (!srn.Member.Users.Any(x => x.UserId == user.Id))
                            throw new UnauthorizedException();
                    }
                }

                if (srn != null)
                {
                    if (srn.Member != null)
                    {
                        if (srn.Member.StakeholderManager != null)
                        {
                            shmId = (srn.Member.StakeholderManager.Id > 0) ? srn.Member.StakeholderManager.Id.ToString() : "";
                        }
                    }
                }

                var oldStatusName = srn.SRNStatus.Name;

                if (string.IsNullOrEmpty(shmId))
                    throw new InvalidSRNMergeNoSHMException();

                if (request.SRNIdMergeFromList.Count == 0)
                    throw new InvalidSRNMergeNoMergeListException();

                foreach (var srnId in request.SRNIdMergeFromList)
                {
                    srnIdMergeList += srnId + ",";

                    var mergeRequest = new SRNMergeRequest
                    {
                        RequestDate = DateTime.Now,
                        FromSRNId = srnId,
                        ToSRNId = request.MergeToSRNId,
                        Status = SRNMergeStatus.Requested,
                        DailyFileDevelopmentStartDate = request.DailyFileDevelopmentStartDate,
                        DailyFileDevelopmentEndDate = request.DailyFileDevelopmentEndDate,
                        DailyFileTestStartDate = request.DailyFileTestStartDate,
                        DailyFileTestEndDate = request.DailyFileTestEndDate,
                        DailyFileGoLiveDate = request.DailyFileGoLiveDate,

                        MonthlyFileDevelopmentStartDate = request.MonthlyFileDevelopmentStartDate,
                        MonthlyFileDevelopmentEndDate = request.MonthlyFileDevelopmentEndDate,
                        MonthlyFileTestStartDate = request.MonthlyFileTestStartDate,
                        MonthlyFileTestEndDate = request.MonthlyFileTestEndDate,
                        MonthlyFileGoLiveDate = request.MonthlyFileGoLiveDate
                    };

                    await _dbContext.Set<SRNMergeRequest>().AddAsync(mergeRequest);
                }

                if (!string.IsNullOrEmpty(srnIdMergeList))
                    srnIdMergeList = srnIdMergeList.TrimEnd(',');

                _dbContext.SaveChanges();

                List<SRN> lstSRNsToBeMerged = new List<SRN>();

                foreach (var srnId in request.SRNIdMergeFromList)
                {
                    var srnToBeMerged = await _dbContext.Set<SRN>()
                        .Include(i => i.SRNStatus)
                        .FirstOrDefaultAsync(i => i.Id == srnId);

                    intialStatusIds += srnId + ":" + srnToBeMerged.SRNStatusId + ",";
                    lstSRNsToBeMerged.Add(srnToBeMerged);
                }

                if (!string.IsNullOrEmpty(intialStatusIds))
                    intialStatusIds = intialStatusIds.TrimEnd(',');

                var taskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                        {
                            {
                                "variables",
                                new Dictionary<string, Dictionary<string, string>>
                                {
                                    {
                                        "RequestType",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", requestType },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "SaleType",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", saleType },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "SRNIdToBeSold",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", "0" },
                                            { "type", "Long" }
                                        }
                                    },
                                    {
                                        "BuyerRegisteredName",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", "" },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "BuyerRegisteredNumber",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", "" },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "SRNIdMergeList",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srnIdMergeList },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "SRNIdSplitList",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", "" },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "SplitFromSRNId",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", "" },
                                            { "type", "Long" }
                                        }
                                    },
                                    {
                                        "MergeToSRNId",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", request.MergeToSRNId.ToString() },
                                            { "type", "Long" }
                                        }
                                    },
                                    {
                                        "stakeHolderManagerAssignee",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", shmId },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "MergeListInitialStatusIds",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", intialStatusIds },
                                            { "type", "String" }
                                        }
                                    }
                                }
                            }
                        };

                using (var client = new HttpClient())
                {
                    var contractResolver = new DefaultContractResolver
                    {
                        NamingStrategy = new CamelCaseNamingStrategy()
                    };
                    var json = JsonConvert.SerializeObject(taskVariables, new JsonSerializerSettings
                    {
                        ContractResolver = contractResolver,
                        Formatting = Formatting.Indented
                    });
                    var content = new StringContent(json, Encoding.UTF8, "application/json");
                    var uri = _configSettings.CamundaBaseAddress + "/process-definition/key/SRN-Split-Merge-Sell/start";
                    var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });
                    result.EnsureSuccessStatusCode();
                }

                foreach (var item in lstSRNsToBeMerged)
                {
                    oldStatusName = item.SRNStatus.Name;
                    UpdateSRNStatus(item, "Merge In Progress");

                    Helpers.Helpers.CreateSRNStatusEventLog(_dbContext, _mapper, oldStatusName, "Merge In Progress", "SRN Merge", item, user);
                }

                oldStatusName = srn.SRNStatus.Name;
                UpdateSRNStatus(srn, "Merge In Progress");

                Helpers.Helpers.CreateSRNStatusEventLog(_dbContext, _mapper, oldStatusName, "Merge In Progress", "SRN Merge", srn, user);
            }
        }

        public void RequestSRNSplit(SRNSplitRequestResource request)
        {
            if (request != null)
            {
                string requestType = "";
                string saleType = "";
                string shmId = "";
                string srnIdSplitList = "";
                string newSRNStatus = "";

                requestType = "split";

                if (request.SplitType == SRNSplitType.Full)
                {
                    saleType = "full";
                    newSRNStatus = "Split In Progress - Full";
                }
                else
                {
                    saleType = "partial";
                    newSRNStatus = "Split In Progress - Partial";
                }

                var srn = await _dbContext.SRNs
                    .Include(i => i.SRNStatus)
                    .Include(i => i.Member)
                        .ThenInclude(i => i.StakeholderManager)
                    .Include(i => i.Member)
                        .ThenInclude(i => i.Users)
                    .FirstOrDefaultAsync(i => i.Id == request.SplitFromSRNId);

                var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext);
                if (!Helpers.Helpers.IsInternalSACRRAUser(user))
                {
                    if (srn != null)
                    {
                        if (!srn.Member.Users.Any(x => x.UserId == user.Id))
                            throw new UnauthorizedException();
                    }
                }

                if (srn != null)
                {
                    if (srn.Member != null)
                    {
                        if (srn.Member.StakeholderManager != null)
                        {
                            shmId = (srn.Member.StakeholderManager.Id > 0) ? srn.Member.StakeholderManager.Id.ToString() : "";
                        }
                    }
                }

                if (string.IsNullOrEmpty(shmId))
                    throw new InvalidSRNSplitNoSHMException();

                if (request.SRNIdSplitList.Count == 0)
                    throw new InvalidSRNSplitNoSplitListException();
                foreach (var srnId in request.SRNIdSplitList)
                {
                    srnIdSplitList += srnId + ",";

                    var splitRequest = new SRNSplitRequest
                    {
                        RequestDate = DateTime.Now,
                        FromSRNId = request.SplitFromSRNId,
                        ToSRNId = srnId,
                        Status = SRNSplitStatus.Requested,
                        Type = request.SplitType,
                        DailyFileDevelopmentStartDate = request.DailyFileDevelopmentStartDate,
                        DailyFileDevelopmentEndDate = request.DailyFileDevelopmentEndDate,
                        DailyFileTestStartDate = request.DailyFileTestStartDate,
                        DailyFileTestEndDate = request.DailyFileTestEndDate,
                        DailyFileGoLiveDate = request.DailyFileGoLiveDate,

                        MonthlyFileDevelopmentStartDate = request.MonthlyFileDevelopmentStartDate,
                        MonthlyFileDevelopmentEndDate = request.MonthlyFileDevelopmentEndDate,
                        MonthlyFileTestStartDate = request.MonthlyFileTestStartDate,
                        MonthlyFileTestEndDate = request.MonthlyFileTestEndDate,
                        MonthlyFileGoLiveDate = request.MonthlyFileGoLiveDate
                    };

                    await _dbContext.Set<SRNSplitRequest>().AddAsync(splitRequest);
                }

                if (!string.IsNullOrEmpty(srnIdSplitList))
                    srnIdSplitList = srnIdSplitList.TrimEnd(',');

                _dbContext.SaveChanges();

                var taskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                        {
                            {
                                "variables",
                                new Dictionary<string, Dictionary<string, string>>
                                {
                                    {
                                        "RequestType",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", requestType },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "SaleType",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", saleType },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "SRNIdToBeSold",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", "0" },
                                            { "type", "Long" }
                                        }
                                    },
                                    {
                                        "BuyerRegisteredName",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", "" },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "BuyerRegisteredNumber",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", "" },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "SRNIdMergeList",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", "" },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "SRNIdSplitList",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srnIdSplitList },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "SplitFromSRNId",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", request.SplitFromSRNId.ToString() },
                                            { "type", "Long" }
                                        }
                                    },
                                    {
                                        "MergeToSRNId",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", "0" },
                                            { "type", "Long" }
                                        }
                                    },
                                    {
                                        "stakeHolderManagerAssignee",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", shmId },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "InitialStatusId",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srn.SRNStatusId.ToString() },
                                            { "type", "Long" }
                                        }
                                    }
                                }
                            }
                        };

                using (var client = new HttpClient())
                {
                    var contractResolver = new DefaultContractResolver
                    {
                        NamingStrategy = new CamelCaseNamingStrategy()
                    };
                    var json = JsonConvert.SerializeObject(taskVariables, new JsonSerializerSettings
                    {
                        ContractResolver = contractResolver,
                        Formatting = Formatting.Indented
                    });
                    var content = new StringContent(json, Encoding.UTF8, "application/json");
                    var uri = _configSettings.CamundaBaseAddress + "/process-definition/key/SRN-Split-Merge-Sell/start";
                    var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });
                    result.EnsureSuccessStatusCode();
                }

                var oldStatusName = srn.SRNStatus.Name;
                UpdateSRNStatus(srn, newSRNStatus);

                Helpers.Helpers.CreateSRNStatusEventLog(_dbContext, _mapper, oldStatusName, newSRNStatus, "SRN Split", srn, user);
            }
        }

        private void UpdateSRNStatus(SRN srn, string status)
        {
            if (srn != null && !string.IsNullOrEmpty(status))
            {
                var newStatus = await _dbContext.SRNStatuses
                    .AsNoTracking()
                    .FirstOrDefaultAsync(i => i.Name == status);

                if (newStatus != null)
                {
                    srn.SRNStatusId = newStatus.Id;
                    srn.StatusLastUpdatedAt = DateTime.Now;
                    _dbContext.Update(srn);
                    _dbContext.SaveChanges();
                }
            }
        }

        public void RequestSRNStatusUpdate(List<SRNStatusUpdateRequestResource> requests)
        {
            if (requests != null)
            {
                var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext);

                foreach (var request in requests)
                {
                    var newStatus = await _dbContext.SRNStatuses
                        .FirstOrDefaultAsync(i => i.Id == request.SRNStatusId);

                    SRN srn = null;

                    if (newStatus != null)
                    {
                        srn = await _dbContext.Set<SRN>()
                                    .Include(i => i.SRNStatusReason)
                                    .Include(i => i.SRNStatus)
                                    .Include(i => i.SRNStatusUpdates)
                                    .FirstOrDefaultAsync(i => i.Id == request.SRNId);

                        var sacrraAdmin = await _dbContext.Users
                                .FirstOrDefaultAsync(i => i.RoleId == UserRoles.SACRRAAdministrator);

                        var sacrraAdminAssinee = (sacrraAdmin != null) ? sacrraAdmin.Id.ToString() : "";

                        var stakeholderManager = await _dbContext.Members
                                    .Select(m => new Member { StakeholderManagerId = m.StakeholderManagerId, Id = m.Id })
                                    .FirstOrDefaultAsync(i => i.Id == srn.MemberId);

                        var shmId = "";
                        if (stakeholderManager != null)
                            shmId = (stakeholderManager.StakeholderManagerId > 0) ? stakeholderManager.StakeholderManagerId.ToString() : "";

                        //Kick off the workflow if the status is "Closed" or "Closure Pending"
                        if (newStatus.Name == "Closed" || newStatus.Name == "Closure Pending")
                        {
                            if (request.UpdateType == null)
                            {
                                throw new InvalidSRNStatusUpdateException();
                            }
                            string updateType = "";
                            bool isDateInThePast = false;
                            string dateString = "";

                            if (srn.SRNStatus.Name == "Closure Pending" || HasActiveStatusUpdate(srn, request))
                                throw new SRNStatusUpdateInProgressException();

                            if (request.UpdateType == SRNStatusTypes.LastSubmission && request.LastSubmissionDate == null)
                                throw new InvalidSRNStatusUpdateException();

                            if (request.UpdateType == SRNStatusTypes.BureauInstruction && request.StatusDate == null)
                                throw new InvalidSRNStatusUpdateException();


                            if (request.UpdateType == SRNStatusTypes.LastSubmission)
                            {
                                updateType = "lastSubmission";
                                isDateInThePast = Helpers.Helpers.IsInThePast(request.LastSubmissionDate.ToString());
                            }
                            else if (request.UpdateType == SRNStatusTypes.BureauInstruction)
                            {
                                updateType = "bureauInstruction";
                                isDateInThePast = Helpers.Helpers.IsInThePast(request.StatusDate);
                            }

                            if (isDateInThePast)
                                dateString = "inThePast";
                            else
                                dateString = "inTheFuture";

                            
                            if (!string.IsNullOrEmpty(dateString) && request.SRNId > 0)
                            {
                                
                                var taskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                                {
                                    {
                                        "variables",
                                        new Dictionary<string, Dictionary<string, string>>
                                        {
                                            {
                                                "updateType",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", updateType },
                                                    { "type", "String" }
                                                }
                                            },
                                            {
                                                "lastSubmissionDate",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", dateString },
                                                    { "type", "String" }
                                                }
                                            },
                                            {
                                                "statusDate",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", dateString },
                                                    { "type", "String" }
                                                }
                                            },
                                            {
                                                "SRNId",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", request.SRNId.ToString() },
                                                    { "type", "Long" }
                                                }
                                            },
                                            {
                                                "stakeHolderManagerAssignee",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", shmId },
                                                    { "type", "String" }
                                                }
                                            },
                                            {
                                                "SACRRAAdminAssignee",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", sacrraAdminAssinee },
                                                    { "type", "String" }
                                                }
                                            },
                                            {
                                                "SRNUpdateType",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", newStatus.Id.ToString() },
                                                    { "type", "Long" }
                                                }
                                            },
                                            {
                                                "SRNStatusReasonId",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", (request.SRNStatusReasonId > 0)? request.SRNStatusReasonId.ToString() : null },
                                                    { "type", "Long" }
                                                }
                                            }
                                        }
                                    }
                                };

                                using (var client = new HttpClient())
                                {
                                    var contractResolver = new DefaultContractResolver
                                    {
                                        NamingStrategy = new CamelCaseNamingStrategy()
                                    };
                                    var json = JsonConvert.SerializeObject(taskVariables, new JsonSerializerSettings
                                    {
                                        ContractResolver = contractResolver,
                                        Formatting = Formatting.Indented
                                    });
                                    var content = new StringContent(json, Encoding.UTF8, "application/json");
                                    var uri = _configSettings.CamundaBaseAddress + "/process-definition/key/SRN-Status-Update/start";
                                    var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });
                                    result.EnsureSuccessStatusCode();
                                }
                            }
                        }
                        //Kick off workflow for non-cancellation statuses
                        else if(newStatus.Name == "Live" || newStatus.Name == "Running Down" || newStatus.Name == "Dormant")
                        {
                            if (HasActiveStatusUpdate(srn, request))
                                throw new SRNStatusUpdateInProgressException();

                            CreateSRNStatusUpdateTask(newStatus, request, sacrraAdminAssinee, user.Id, "");
                        }
                        else if (newStatus.Name == "Test")
                        {
                            if (HasActiveStatusUpdate(srn, request))
                                throw new SRNStatusUpdateInProgressException();

                            CreateStatusUpdateHistory(srn, request, newStatus, sacrraAdminAssinee, shmId, user.Id);
                        }

                        if (srn != null)
                        {
                            var stagingChangeLog = CreateSRNStatusStagingChangeLog(request, srn, user);

                            var entityBlob = JsonConvert.SerializeObject(srn, Formatting.None, new JsonSerializerSettings
                            {
                                ReferenceLoopHandling = ReferenceLoopHandling.Ignore
                            });

                            var stagingBlob = JsonConvert.SerializeObject(stagingChangeLog, Formatting.None, new JsonSerializerSettings
                            {
                                ReferenceLoopHandling = ReferenceLoopHandling.Ignore
                            });

                            
                            if(request.UpdateType == SRNStatusTypes.BureauInstruction)
                            {
                                srn.AccountStatusDate = (request.StatusDate != null) ? Helpers.Helpers.ConvertStringToDate(request.StatusDate) : DateTime.Now;
                            }
                            
                            srn.LastSubmissionDate = (request.LastSubmissionDate != null) ? Helpers.Helpers.ConvertStringToDate(request.LastSubmissionDate.ToString()) : null;
                            srn.BureauInstruction = request.BureauInstruction;
                            srn.FileType = (request.FileType > 0) ? request.FileType : srn.FileType;
                            srn.Comments = request.Comments;
                            //NOT DOT update the status at this point, it will be updated by the topic "update-srn-status-to-pending-closure"
                            //srn.SRNStatusId = (request.SRNStatusId > 0) ? request.SRNStatusId : srn.SRNStatusId;

                            _dbContext.SaveChanges();

                            Helpers.Helpers.CreateEventLog(_dbContext, user.Id, "SRN Update", srn.TradingName, entityBlob, stagingBlob, srn.Id, "SRN");

                        }
                    }
                    else
                    {
                        throw new InvalidSRNStatusUpdateException();
                    }
                }
            }
        }

        private MemberStagingChangeLogResource CreateSRNStatusStagingChangeLog(SRNStatusUpdateRequestResource updateRequest, SRN srn, User user)
        {
            var stagingChangeLog = new MemberStagingChangeLogResource();

            //if (!string.IsNullOrEmpty(updateRequest.BureauInstruction))
            //{
            //    stagingChangeLog.Changes.Add(new StagingChange
            //    {
            //        Name = "Bureau Instruction",
            //        OldValue = (!string.IsNullOrEmpty(srn.BureauInstruction)) ? srn.BureauInstruction : "",
            //        NewValue = updateRequest.BureauInstruction
            //    });
            //}
            //if (!string.IsNullOrEmpty(updateRequest.Comments))
            //{
            //    stagingChangeLog.Changes.Add(new StagingChange
            //    {
            //        Name = "Status Comment",
            //        OldValue = (!string.IsNullOrEmpty(srn.StatusComment)) ? srn.StatusComment : "",
            //        NewValue = updateRequest.Comments
            //    });
            //}
            //if (updateRequest.FileType > 0)
            //{
            //    stagingChangeLog.Changes.Add(new StagingChange
            //    {
            //        Name = "File Type",
            //        OldValue = (srn.FileTypeId > 0) ? EnumHelper.GetEnumIdValuePair<SRNStatusFileTypes>((int)srn.FileTypeId).Value : "",
            //        NewValue = EnumHelper.GetEnumIdValuePair<SRNStatusFileTypes>((int)updateRequest.FileType).Value
            //    });
            //}
            if (updateRequest.StatusDate != null)
            {
                stagingChangeLog.Changes.Add(new StagingChange
                {
                    Name = "SRN Status Date",
                    OldValue = (srn.AccountStatusDate != null) ? srn.AccountStatusDate.ToString() : "",
                    NewValue = (Helpers.Helpers.ConvertStringToDate(updateRequest.StatusDate) != null) ? Helpers.Helpers.ConvertStringToDate(updateRequest.StatusDate).Value.ToString() : ""
                });
            }
            if (updateRequest.LastSubmissionDate != null)
            {
                stagingChangeLog.Changes.Add(new StagingChange
                {
                    Name = "Last Submission Date",
                    OldValue = (srn.LastSubmissionDate != null) ? srn.LastSubmissionDate.ToString() : "",
                    NewValue = (Helpers.Helpers.ConvertStringToDate(updateRequest.LastSubmissionDate.ToString()) != null) ? Helpers.Helpers.ConvertStringToDate(updateRequest.LastSubmissionDate.ToString()).Value.ToString() : ""
                }); 
            }
            //if (updateRequest.UpdateType > 0)
            //{
            //    stagingChangeLog.Changes.Add(new StagingChange
            //    {
            //        Name = "Update Type",
            //        OldValue = (srn.StatusTypeId > 0) ? EnumHelper.GetEnumIdValuePair<SRNStatusTypes>((int)srn.StatusTypeId).Value : "",
            //        NewValue = EnumHelper.GetEnumIdValuePair<SRNStatusTypes>((int)updateRequest.UpdateType).Value
            //    });
            //}
            if (updateRequest.SRNStatusReasonId > 0)
            {
                var newValue = await _dbContext.SRNStatusReasons
                    .FirstOrDefaultAsync(i => i.Id == updateRequest.SRNStatusReasonId);

                stagingChangeLog.Changes.Add(new StagingChange
                {
                    Name = "SRN Status Reason",
                    OldValue = (srn.SRNStatusReason != null) ? srn.SRNStatusReason.Name : "",
                    NewValue = newValue.Name
                });
            }

            return stagingChangeLog;
        }

        private void CreateStatusUpdateHistory(SRN srn, SRNStatusUpdateRequestResource request, SRNStatus newStatus, string sacrraAdminAssinee, string shmId, int userId)
        {
            if(srn != null && request != null)
            {
                if (HasActiveStatusUpdate(srn, request))
                    throw new SRNStatusUpdateInProgressException();

                var updateHistoryModel = _mapper.Map<SRNStatusUpdateHistory>(request);
                updateHistoryModel.DateCreated = DateTime.Now;
                updateHistoryModel.Comments = request.Comments;

                var updateNumber = Guid.NewGuid().ToString();

                if (request.FileType == SRNStatusFileTypes.MonthlyAndDailyFile)
                {
                    ProcessInstanceInfoResource dailyFileTask = null;

                    if(newStatus.Name == "Test")
                    {
                        var testEndDate = (request.DailyFileTestEndDate != null) ? string.Format("{0:yyyy-MM-dd}", request.DailyFileTestEndDate) : "1900-01-01";
                        DateTime testDate = (request.DailyFileTestEndDate != null) ? (DateTime)request.DailyFileTestEndDate : Convert.ToDateTime("1900-01-01");

                        //If test date is the future
                        if(testDate.Date > DateTime.Now.Date)
                        {
                            testDate = testDate.AddDays(-3);
                            testEndDate = string.Format("{0:yyyy-MM-dd}", testDate);
                        }
                        
                        dailyFileTask = CreateSRNStatusUpdateToTestTask(newStatus, testEndDate, request, sacrraAdminAssinee, shmId, userId, "DailyFile");
                    }
                    else
                    {
                        dailyFileTask = CreateSRNStatusUpdateTask(newStatus, request, sacrraAdminAssinee, userId, "DailyFile");
                    }

                    var dailyFileUpdate = _mapper.Map<SRNStatusUpdateHistory>(request);
                    dailyFileUpdate.FileType = SRNStatusFileTypes.DailyFile;
                    dailyFileUpdate.DateCreated = DateTime.Now;
                    dailyFileUpdate.UpdateNumber = updateNumber;

                    var rolloutStatus = await _dbContext.RolloutStatuses
                        .FirstOrDefaultAsync(i => i.Name == newStatus.Name);

                    if (rolloutStatus != null)
                        dailyFileUpdate.RolloutStatusId = rolloutStatus.Id;

                    if (dailyFileTask != null)
                        dailyFileUpdate.ProcessInstanceId = dailyFileTask.Id;

                    ProcessInstanceInfoResource monthlyFileTask = null;
                    if (newStatus.Name == "Test")
                    {
                        var testEndDate = (request.MonthlyFileTestEndDate != null) ? string.Format("{0:yyyy-MM-dd}", request.MonthlyFileTestEndDate) : "1900-01-01";
                        DateTime testDate = (request.MonthlyFileTestEndDate != null) ? (DateTime)request.MonthlyFileTestEndDate : Convert.ToDateTime("1900-01-01");

                        //If test date is the future
                        if (testDate.Date > DateTime.Now.Date)
                        {
                            testDate = testDate.AddDays(-3);
                            testEndDate = string.Format("{0:yyyy-MM-dd}", testDate);
                        }

                        monthlyFileTask = CreateSRNStatusUpdateToTestTask(newStatus, testEndDate, request, sacrraAdminAssinee, shmId, userId, "MonthlyFile");
                    }
                    else
                    {
                        monthlyFileTask = CreateSRNStatusUpdateTask(newStatus, request, sacrraAdminAssinee, userId, "MonthlyFile");
                    }

                    var monthlyFileUpdate = _mapper.Map<SRNStatusUpdateHistory>(request);
                    monthlyFileUpdate.FileType = SRNStatusFileTypes.MonthlyFile;
                    monthlyFileUpdate.DateCreated = DateTime.Now;
                    monthlyFileUpdate.UpdateNumber = updateNumber;

                    if (monthlyFileTask != null)
                        monthlyFileUpdate.ProcessInstanceId = monthlyFileTask.Id;

                    if (rolloutStatus != null)
                        monthlyFileUpdate.RolloutStatusId = rolloutStatus.Id;

                    srn.SRNStatusUpdates.Add(dailyFileUpdate);
                    srn.SRNStatusUpdates.Add(monthlyFileUpdate);
                }
                else if(request.FileType == SRNStatusFileTypes.DailyFile || request.FileType == SRNStatusFileTypes.MonthlyFile)
                {
                    ProcessInstanceInfoResource updateTask = null;
                    if (newStatus.Name == "Test")
                    {
                        var testEndDate = "1900-01-01";
                        DateTime testDate = Convert.ToDateTime("1900-01-01");

                        if (request.FileType == SRNStatusFileTypes.DailyFile)
                        {
                            testDate = (request.DailyFileTestEndDate != null) ? (DateTime)request.DailyFileTestEndDate : Convert.ToDateTime("1900-01-01");
                        }
                        else if (request.FileType == SRNStatusFileTypes.MonthlyFile)
                        {
                            testDate = (request.MonthlyFileTestEndDate != null) ? (DateTime)request.MonthlyFileTestEndDate : Convert.ToDateTime("1900-01-01");
                        }

                        //If test date is the future
                        if (testDate.Date > DateTime.Now.Date)
                        {
                            testDate = testDate.AddDays(-3);
                            testEndDate = string.Format("{0:yyyy-MM-dd}", testDate);
                        }

                        updateTask = CreateSRNStatusUpdateToTestTask(newStatus, testEndDate, request, sacrraAdminAssinee, shmId, userId, request.FileType.ToString());
                    }
                    else
                    {
                        updateTask = CreateSRNStatusUpdateTask(newStatus, request, sacrraAdminAssinee, userId, request.FileType.ToString());
                    }

                    if (updateTask != null)
                        updateHistoryModel.ProcessInstanceId = updateTask.Id;

                    var rolloutStatus = await _dbContext.RolloutStatuses
                        .FirstOrDefaultAsync(i => i.Name == newStatus.Name);

                    if (rolloutStatus != null)
                        updateHistoryModel.RolloutStatusId = rolloutStatus.Id;

                    updateHistoryModel.UpdateNumber = updateNumber;
                    srn.SRNStatusUpdates.Add(updateHistoryModel);
                }
                
            }
        }

        private ProcessInstanceInfoResource CreateSRNStatusUpdateTask(SRNStatus newStatus, SRNStatusUpdateRequestResource request, string sacrraAdminAssinee, int userId, string fileType)
        {
            var isLiveOrTest = (newStatus.Name == "Live" || newStatus.Name == "Test") ? "yes" : "no";

            var taskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                                {
                                    {
                                        "variables",
                                        new Dictionary<string, Dictionary<string, string>>
                                        {
                                            {
                                                "isLiveOrTest",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", isLiveOrTest },
                                                    { "type", "String" }
                                                }
                                            },
                                            {
                                                "SRNId",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", request.SRNId.ToString() },
                                                    { "type", "Long" }
                                                }
                                            },
                                            {
                                                "SRNStatusName",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", newStatus.Name },
                                                    { "type", "String" }
                                                }
                                            },
                                            {
                                                "UpdatedByUserId",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", userId.ToString() },
                                                    { "type", "Long" }
                                                }
                                            },
                                            {
                                                "SRNStatusId",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", request.SRNStatusId.ToString() },
                                                    { "type", "Long" }
                                                }
                                            },
                                            {
                                                "SACRRAAdminAssignee",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", sacrraAdminAssinee },
                                                    { "type", "String" }
                                                }
                                            },
                                            {
                                                "isLiveFileSubmissionsSuspended",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", request.IsLiveFileSubmissionsSuspended.ToString() },
                                                    { "type", "Boolean" }
                                                }
                                            },
                                            {
                                                "FileType",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", fileType },
                                                    { "type", "String" }
                                                }
                                            },
                                            {
                                                "SRNUpdateType",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", newStatus.Id.ToString() },
                                                    { "type", "Long" }
                                                }
                                            }
                                        }
                                    }
                                };

            using (var client = new HttpClient())
            {
                var contractResolver = new DefaultContractResolver
                {
                    NamingStrategy = new CamelCaseNamingStrategy()
                };
                var json = JsonConvert.SerializeObject(taskVariables, new JsonSerializerSettings
                {
                    ContractResolver = contractResolver,
                    Formatting = Formatting.Indented
                });
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var uri = _configSettings.CamundaBaseAddress + "/process-definition/key/SRN-Status-Update-Non-Cancellations/start";
                var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });
                result.EnsureSuccessStatusCode();

                var jsonResult = result.Content.ReadAsString();
                var processInfo = JsonConvert.DeserializeObject<ProcessInstanceInfoResource>(jsonResult);

                return processInfo;
            }
        }

        private ProcessInstanceInfoResource CreateSRNStatusUpdateToTestTask(SRNStatus newStatus, string testEndDate, SRNStatusUpdateRequestResource request, string sacrraAdminAssinee, string stakeHolderManagerAssignee, int userId, string fileType)
        {

            var taskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                                {
                                    {
                                        "variables",
                                        new Dictionary<string, Dictionary<string, string>>
                                        {
                                            {
                                                "testEndDate",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", testEndDate },
                                                    { "type", "string" }
                                                }
                                            },
                                            {
                                                "SRNId",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", request.SRNId.ToString() },
                                                    { "type", "Long" }
                                                }
                                            },
                                            {
                                                "SRNStatusName",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", newStatus.Name },
                                                    { "type", "String" }
                                                }
                                            },
                                            {
                                                "UpdatedByUserId",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", userId.ToString() },
                                                    { "type", "Long" }
                                                }
                                            },
                                            {
                                                "SRNStatusId",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", request.SRNStatusId.ToString() },
                                                    { "type", "Long" }
                                                }
                                            },
                                            {
                                                "SACRRAAdminAssignee",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", sacrraAdminAssinee },
                                                    { "type", "String" }
                                                }
                                            },
                                            {
                                                "stakeHolderManagerAssignee",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", stakeHolderManagerAssignee },
                                                    { "type", "String" }
                                                }
                                            },
                                            {
                                                "isLiveFileSubmissionsSuspended",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", request.IsLiveFileSubmissionsSuspended.ToString() },
                                                    { "type", "Boolean" }
                                                }
                                            },
                                            {
                                                "FileType",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", fileType },
                                                    { "type", "String" }
                                                }
                                            },
                                            {
                                                "SRNUpdateType",
                                                new Dictionary<string, string>()
                                                {
                                                    { "value", newStatus.Id.ToString() },
                                                    { "type", "Long" }
                                                }
                                            }
                                        }
                                    }
                                };

            using (var client = new HttpClient())
            {
                var contractResolver = new DefaultContractResolver
                {
                    NamingStrategy = new CamelCaseNamingStrategy()
                };
                var json = JsonConvert.SerializeObject(taskVariables, new JsonSerializerSettings
                {
                    ContractResolver = contractResolver,
                    Formatting = Formatting.Indented
                });
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var uri = _configSettings.CamundaBaseAddress + "/process-definition/key/SRN-Status-Update-To-Test/start";
                var result = client.Send(new HttpRequestMessage(HttpMethod.Post, uri) { Content = content });
                result.EnsureSuccessStatusCode();

                var jsonResult = result.Content.ReadAsString();
                var processInfo = JsonConvert.DeserializeObject<ProcessInstanceInfoResource>(jsonResult);

                return processInfo;
            }
        }

        private bool HasActiveStatusUpdate(SRN srn, SRNStatusUpdateRequestResource request)
        {
            var hasActiveStatusUpdate = false;
            if (request.FileType == SRNStatusFileTypes.MonthlyAndDailyFile)
            {
                hasActiveStatusUpdate = srn.SRNStatusUpdates.Any(i => !i.IsComple
                    && (i.FileType == SRNStatusFileTypes.DailyFile || i.FileType == SRNStatusFileTypes.MonthlyFile));
            }
            else if (request.FileType == SRNStatusFileTypes.DailyFile)
            {
                hasActiveStatusUpdate = srn.SRNStatusUpdates.Any(i => !i.IsComple
                    && i.FileType == SRNStatusFileTypes.DailyFile);
            }
            else if (request.FileType == SRNStatusFileTypes.MonthlyFile)
            {
                hasActiveStatusUpdate = srn.SRNStatusUpdates.Any(i => !i.IsComple
                    && i.FileType == SRNStatusFileTypes.MonthlyFile);
            }
            else if(request.FileType == null)
            {
                hasActiveStatusUpdate = srn.SRNStatusUpdates.Any(i => !i.IsComple);
            }

            return hasActiveStatusUpdate;
        }
    }
}

