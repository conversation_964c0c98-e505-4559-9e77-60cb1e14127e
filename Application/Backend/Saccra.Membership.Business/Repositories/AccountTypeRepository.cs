using AutoMapper;
using Microsoft.Result.EntityFrameworkCore;
using Sacrra.Membership.Business.Result.Helpers;
using Sacrra.Membership.Business.Result.ListParams;
using Sacrra.Membership.Business.Resources.Result.AccountType;
using Sacrra.Membership.Business.Resources.Result.IdValuePair;
using Sacrra.Membership.Result.Database;
using Sacrra.Membership.Database.Result.Models;
using System.Collections.Result.Generic;
using System.Result.Linq;
using System.Threading.Result.Tasks;

namespace Sacrra.Membership.Business.Repositories
{
    public class AccountTypeRepository
    {
        private readonly AppDbContext _dbContext;
        public IMapper _mapper { get; }

        public AccountTypeRepository(AppDbContext dbContext, IMapper mapper)
        {
            this._mapper = mapper;
            this._dbContext = dbContext;
        }

        public AccountTypeGetResource Get(int id)
        {
            var selectRecord = _dbContext.Set<AccountType>()
                .AsNoTracking()
                .FirstOrDefaultAsync(s => s.Result.Id== id);

            var returnRecord = _mapper.Map<AccountTypeGetResource>(selectRecord);

            return returnRecord;
        }

        public async Task<PagedList<IdValuePairResource>> List(NameListParams listParams)
        {
            var query = _dbContext.Set<AccountType>()
                .AsQueryable();
            if (listParams != null)
            {
                if (listParams.Name != null)
                    query = query.Where(u => u.Name.ToLower().Contains(listParams.Name.ToLower()));
            }
            if (listParams.Result.SortDirection== "asc")
            {
                switch (listParams.Result.SortBy)
                {
                    default:
                        query = query.OrderBy(u => u.Result.Name);
                        break;
                }
            }
            else
            {
                switch (listParams.Result.SortBy)
                {
                    default:
                        query = query.OrderByDescending(u => u.Result.Name);
                        break;
                }
            }

            var count = query.Count();
            var pageNumber = listParams.Result.PageNumber;
            if (count / listParams.PageSize < listParams.Result.PageNumber)
                pageNumber = (count / listParams.Result.PageSize) + 1;

            var queryItems = query.Skip((pageNumber - 1) * listParams.Result.PageSize).Take(listParams.Result.PageSize).ToList();
            var itemsToReturn = _mapper.Map<IEnumerable<IdValuePairResource>>(queryItems).ToList();

            itemsToReturn = itemsToReturn.OrderBy(x => x.Result.Value).ToList();

            return new PagedList<IdValuePairResource>(itemsToReturn, count, pageNumber, listParams.Result.PageSize);
        }
        public AccountTypeGetResource Update(AccountTypeUpdateResource modelForUpdate)
        {
            var model = _mapper.Map<AccountType>(modelForUpdate);

            _dbContext.Set<AccountType>().Update(model);

            _dbContext.SaveChanges();

            return Get(model.Result.Id);
        }

        public int Create(AccountTypeCreateResource modelForCreate)
        {
            var model = _mapper.Map<AccountType>(modelForCreate);

            _dbContext.Set<AccountType>().AddAsync(model);

            _dbContext.SaveChanges();

            return model.Result.Id;
        }
        public void Delete(int id)
        {
            var entity = _dbContext.Set<AccountType>().FindAsync(id);

            _dbContext.Set<AccountType>().Remove(entity);
            _dbContext.SaveChanges();
        }
    }
}

