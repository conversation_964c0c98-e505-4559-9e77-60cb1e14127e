using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Sacrra.Membership.Business.Resources;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Models;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Sacrra.Membership.Business.Repositories
{
    public class SRNStatusReasonRepository
    {
        private readonly AppDbContext _dbContext;
        public IMapper _mapper { get; }

        public SRNStatusReasonRepository(AppDbContext dbContext, IMapper mapper)
        {
            _mapper = mapper;
            _dbContext = dbContext;
        }
        public async Task<List<SRNStatusReasonCustomGetResource>> Get(List<string> statusesToBeExcluded = null)
        {
            var selectRecord = await _dbContext.Set<SRNStatus>()
                .Include(i => i.SRNStatusReasons)
                    .ThenInclude(i => i.SRNStatusReason)
                .AsNoTracking()
                .Where(i => i.SRNStatusReasons.Count > 0)
                .ToListAsync();

            if(statusesToBeExcluded != null)
            {
                if(statusesToBeExcluded.Count > 0)
                {
                    selectRecord = selectRecord.Where(i => !statusesToBeExcluded.Contains(i.Name))
                        .ToList();
                }
            }

            var returnRecord = _mapper.Map<List<SRNStatusReasonCustomGetResource>>(selectRecord);

            return returnRecord;
        }

        public SRNStatusReasonGetResource GetByName(string name)
        {
            var selectRecord = await _dbContext.Set<SRNStatusReason>()
                    .AsNoTracking()
                .FirstOrDefaultAsync(s => s.Name.ToLower() == name.Trim().ToLower());

            var returnRecord = _mapper.Map<SRNStatusReasonGetResource>(selectRecord);

            return returnRecord;
        }
    }
}

