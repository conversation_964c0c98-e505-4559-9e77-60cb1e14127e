using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sacrra.Membership.Business.DTOs;
using Sacrra.Membership.Business.ListParams;
using Sacrra.Membership.Business.Resources;
using Sacrra.Membership.Business.Resources.IdValuePair;
using Sacrra.Membership.Business.Resources.Member;
using Sacrra.Membership.Business.Resources.User;
using Sacrra.Membership.Database.Enums;
using System.Collections.Generic;
using System.Threading.Tasks;
using Sacrra.Membership.Business.Repositories;

namespace Sacrra.Membership.Api.Controllers
{
    [ApiController]
    [Route("api/v{v:apiVersion}/[controller]")]
    public class UsersController: Controller
    {
        private readonly UserRepository _repository;

        public UsersController(UserRepository userRepository)

        { _repository = userRepository; }

        [Authorize(Roles = "Group Stakeholder Manager")]
        [HttpDelete("{id}")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult Delete(int id)
        {
            _repository.Delete(id);
            return Ok();
        }

        [Authorize(Roles = "Financial Administrator,User,Member,Stakeholder Manager,Group Stakeholder Manager,SACRRA Administrator,ALG Leader")]
        [HttpGet]
        [ProducesResponseType(type: typeof(List<IdValuePairResource>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult List([FromQuery]NameListParams listParams)
        {
            var auth0Id = HttpContext.User.Identity.Name;

            return Ok(_repository.ListAllUsers(auth0Id));
        }

        [Authorize(Roles = "Financial Administrator,Member,User,Stakeholder Manager,Group Stakeholder Manager,SACRRA Administrator,ALG Leader, Bureau")]
        [HttpGet("{id}", Name = "GetUser")]
        [ProducesResponseType(typeof(UserGetResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult Get(int id)
        {
            var auth0Id = this.HttpContext.User.Identity.Name;

            var entity = _repository.GetSignedInUser(auth0Id);
            if (entity == null)
                return NotFound();

            return Ok(entity);
        }

        [HttpGet("confirm-email")]
        [ProducesResponseType(typeof(bool), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult ConfirmEmail(string email, string token)
        {
            return Ok(_repository.ConfirmEmail(email, token));
        }

        [Authorize(Roles = "Financial Administrator,User,Member,Stakeholder Manager,Group Stakeholder Manager,SACRRA Administrator,ALG Leader")]
        [HttpGet("{id}/members")]
        [ProducesResponseType(type: typeof(List<MemberGetCustomResource>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult ListMembers(int id)
        {
            var auth0Id = this.HttpContext.User.Identity.Name;

            return Ok(_repository.ListMembers(auth0Id));
        }

        [HttpPost("reset-password")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult ResetPassword([FromBody]PasswordResetResource passwordResetResource)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            _repository.ResetPassword(passwordResetResource);
            return Ok();
        }

        [Authorize(Roles = "Financial Administrator,Stakeholder Manager,Group Stakeholder Manager,SACRRA Administrator")]
        [HttpGet("by-role/{roleId}")]
        [ProducesResponseType(type: typeof(List<IdValuePairResource>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult ListUsersByRole(UserRoles roleId)
        {
            return Ok(_repository.ListUsersByRole(roleId));
        }

        [Authorize(Roles = "Financial Administrator,Stakeholder Manager,Group Stakeholder Manager,SACRRA Administrator")]
        [HttpGet("roles")]
        [ProducesResponseType(type: typeof(List<IdValuePairResource>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult ListAllRoles()
        {
            return Ok(_repository.ListAllRoles());
        }

        [HttpPut("resend-email-confirmation/{email}")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult ResendEmailConfirmation(string email)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            _repository.ResendEmailConfirmation(email);
            return Ok();
        }

        [Authorize(Roles = "Group Stakeholder Manager")]
        [HttpDelete("{id}/roles/{roleId}")]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult RemoveUserRole(string id, string roleId)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            _repository.RemoveAuth0RoleFromUser(id, roleId);
            return NoContent();
        }

        [Authorize(Roles = "Financial Administrator,Member,User,Stakeholder Manager,Group Stakeholder Manager,SACRRA Administrator,ALG Leader,Bureau")]
        [HttpGet("auth0")]
        [ProducesResponseType(typeof(UserGetResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult GetByAuth0Id()
        {
            var auth0Id = HttpContext.User.Identity.Name;

            var entity = _repository.GetSignedInUser(auth0Id);
            if (entity == null)
                return NotFound();

            return Ok(entity);
        }

        [Authorize(Roles = "Financial Administrator,Member,User,Stakeholder Manager,Group Stakeholder Manager,SACRRA Administrator,ALG Leader,Bureau")]
        [HttpGet("update-lastread-tcs-and-cs")]
        [ProducesResponseType(typeof(UserGetResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult UpdateLastReadTcsAndCs()
        {
            _repository.UpdateLastReadTcsAndCs();
            return Ok();
        }

        [HttpPut("update-profile")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 400)]
        [ProducesResponseType(typeof(void), 500)]
        public IActionResult UpdateUserProfile(UserUpdateProfileDTO profileDTO)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            _repository.UpdateUserProfile(profileDTO);

            return Ok();
        }
    }
}

