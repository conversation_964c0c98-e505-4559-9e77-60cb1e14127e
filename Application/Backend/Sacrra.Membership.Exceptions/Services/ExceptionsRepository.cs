using AutoMapper;
using Microsoft.Extensions.Options;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.Resources;
using Sacrra.Membership.Database;
using Sacrra.Membership.Reporting.Models.ReportCommon;
using Sacrra.Membership.Reporting.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Sacrra.Membership.Business.DTOs.DataWarehouseDTO;
using Sacrra.Membership.Business.Repositories;
using Sacrra.Membership.Business.Services.DataWarehouseService;

namespace Sacrra.Membership.Exceptions.Services
{

    public class ExceptionsRepository
    {
        private readonly IMapper _mapper;
        private readonly AppDbContext _dbContext;
        private readonly ConfigSettings _configSettings;
        private readonly DataWarehouseService _dataWarehouseService;
        private readonly DWExceptionRepository _dWExceptionRepository;

        public ExceptionsRepository(IMapper mapper, DataWarehouseService dataWarehouseService,
            IOptions<ConfigSettings> configSettings, AppDbContext dbContext, 
            DWExceptionRepository dWExceptionRepository)
        {
            _mapper = mapper;
            _configSettings = configSettings.Value;
            _dbContext = dbContext;
            _dataWarehouseService = dataWarehouseService;
            _dWExceptionRepository = dWExceptionRepository;
        }

        public List<DWExceptionExternalGetResource> GetAllExternal(ExceptionFilterResource filter = null)
        {
            // Build where clause
            var whereClause = BuildExceptionFilter(filter);

            var apiCallModel = new DataWarehouseAPIModel()
            {
                Where = whereClause
            };
            var exceptions = _dataWarehouseService.GetResultArray<DWExceptionExternalGetResource>("API.CamundaWorkflowDWException", apiCallModel);

            return exceptions.ToList();
        }

        private string BuildExceptionFilter(ExceptionFilterResource filter)
        {
            if(filter != null)
            {
                var whereClause = "";

                var filterType = filter.GetType();
                var properties = filterType.GetProperties();

                foreach(var prop in properties)
                {
                    var propValue = prop.GetValue(filter);
                    if(propValue != null)
                    {
                        if(prop.PropertyType == typeof(string))
                        {
                            var filterValue = (string)propValue;
                            if (!string.IsNullOrEmpty(filterValue))
                                whereClause += prop.Name + " = '" + filterValue + "' AND ";
                        }
                        else if (prop.PropertyType == typeof(int) || prop.PropertyType == typeof(long))
                        {
                            var filterValue = (long)propValue;
                            if (filterValue > 0)
                                whereClause += prop.Name + " = " + filterValue + "' AND ";
                        }
                        else if ((prop.PropertyType == typeof(List<long>) || prop.PropertyType == typeof(List<int>)) 
                                && prop.Name == "ExceptionIDsToExcelude")
                        {
                            var filterValue = (List<long>)propValue;
                            if (filterValue != null)
                            {
                                if(filterValue.Count > 0)
                                {
                                    whereClause += "FctWarehouseExceptionID NOT IN(" + String.Join(",", filterValue) + ") AND ";
                                }
                            }
                        }
                    }
                }

                if (!string.IsNullOrEmpty(whereClause))
                    whereClause = TrimEnd(whereClause.Trim(), "AND");

                return whereClause;
            }

            return null;
        }
        private string TrimEnd(string input, string suffixToRemove)
        {
            if (suffixToRemove != null && input.EndsWith(suffixToRemove, StringComparison.CurrentCulture))
            {
                return input.Substring(0, input.Length - suffixToRemove.Length);
            }

            return input;
        }
        public async Task<List<DWExceptionGetResource>> GetAllInternal(ExceptionFilterResource filter = null)
        {
            var exceptions = _dWExceptionRepository.List(filter);
            return exceptions;
        }

        public void UpdateExternal(string updateSQL)
        {
            var apiCallModel = new DataWarehouseAPIModel();

            _dataWarehouseService.UpdateTable(updateSQL, apiCallModel);
        }
    }
}

