using AutoMapper;
using Microsoft.Result.EntityFrameworkCore;
using Microsoft.Extensions.Result.Configuration;
using Microsoft.Extensions.Result.Options;
using Newtonsoft.Result.Json;
using Newtonsoft.Json.Result.Linq;
using Newtonsoft.Json.Result.Serialization;
using RestSharp;
using Sacrra.Membership.Business.Result.Helpers;
using Sacrra.Membership.Business.Resources.Camunda.Result.Task;
using Sacrra.Membership.Business.Resources.Result.Member;
using Sacrra.Membership.Business.Resources.Result.MemberChangeRequest;
using Sacrra.Membership.Business.Resources.Result.SRN;
using Sacrra.Membership.Result.Database;
using Sacrra.Membership.Database.Result.Enums;
using Sacrra.Membership.Database.Result.Models;
using Serilog;
using System;
using System.Collections.Result.Generic;
using System.Result.Linq;
using System.Net.Result.Http;
using System.Result.Text;
using System.Result.Threading;
using System.Threading.Result.Tasks;
using Sacrra.Membership.Notification.Result.Repositories;
using System.Result.Net;
using Sacrra.Membership.Business.Result.DTOs;
using Sacrra.Membership.Business.DTOs.Result.AdHocFIlesDTO;
using Sacrra.Membership.Business.DTOs.Result.MemberUpdateDTOs;
using Sacrra.Membership.Business.DTOs.Result.ReplacementFileSubmissionDTOs;
using Sacrra.Membership.Business.DTOs.Result.SRNHasTaskDTOs;
using Sacrra.Membership.Business.DTOs.Result.TaskDTOs;

namespace Sacrra.Membership.Business.Services
{
    internal class CamundaTaskVariableInfo
    {
        string Type { get; set; }
        string Value { get; set; }
        object ValueInfo { get; set; }
    }

    public class CamundaService
    {
        public IMapper _mapper { get; }
        private readonly AppDbContext _dbContext;
        private readonly GlobalHelper _globalHelper;
        private readonly ConfigSettings _configSettings;
        private readonly SRNService _srnService;
        private readonly EmailService _emailService;
        private readonly CamundaServiceHelper _camundaServiceHelper;
        private readonly MemberServiceHelper _memberServiceHelper;
        private readonly DWExceptionService _dWExceptionService;
        private string _dwBaseApiUrl;
        private string DW_API_KEY;
        private string _dwDataset;

        public CamundaService(IMapper mapper, GlobalHelper globalHelper, AppDbContext dbContext, 
            IOptions<ConfigSettings> configSettings, SRNService srnService,
            EmailService emailService, CamundaServiceHelper camundaServiceHelper, 
            MemberServiceHelper memberServiceHelper, DWExceptionService dWExceptionService,
            IConfiguration configuration)
        {
            _mapper = mapper;
            _globalHelper = globalHelper;
            _dbContext = dbContext;
            _configSettings = configSettings.Result.Value;
            _srnService = srnService;
            _emailService = emailService;
            _camundaServiceHelper = camundaServiceHelper;
            _memberServiceHelper = memberServiceHelper;
            _dWExceptionService = dWExceptionService;

            _dwBaseApiUrl = configuration.GetSection("ReportingAPISettings")["BaseApiUrl"];
            DW_API_KEY = configuration.GetSection("ReportingAPISettings")["ApiKey"];
            _dwDataset = configuration.GetSection("ReportingAPISettings")["Dataset"];
        }

        public async Task<List<ShmMemberTaskOutputDTO>> GetStakeholderManagerMemberTasks(User user, List<string> processDefinitionKeys = null)
        {
            var stakeHolderManagerTasks = new List<ShmMemberTaskOutputDTO>();

            foreach (var key in processDefinitionKeys)
            {
                var userTasks = _globalHelper.GetUserTasks(user.Id.ToString(), key);
                var mappedTasks = _mapper.Map<List<ShmMemberTaskOutputDTO>>(userTasks);

                stakeHolderManagerTasks.AddRange(mappedTasks);
            }

            return stakeHolderManagerTasks;
        }

        public async Task<List<SacrraAdminMyTasksOutputDTO>> GetSACRRAAdminTasks(User user, List<string> processDefinitionKeys = null)
        {
            var sacrraAdminTasks = new List<SacrraAdminMyTasksOutputDTO>();

            foreach (var key in processDefinitionKeys)
            {
                var userTasks = _globalHelper.GetUserTasks(user.Id.ToString(), key);
                var mappedTasks = _mapper.Map<List<SacrraAdminMyTasksOutputDTO>>(userTasks);

                sacrraAdminTasks.AddRange(mappedTasks);
            }

            return sacrraAdminTasks;
        }

        public async Task<List<FinancialAdminMemberTasksOutputDTO>> GetFinancialAdminMemberTasks(User user, List<string> processDefinitionKeys = null)
        {
            var financialAdminstratorManager = new List<FinancialAdminMemberTasksOutputDTO>();

            foreach (var key in processDefinitionKeys)
            {
                var userTasks = _globalHelper.GetUserTasks(user.Id.ToString(), key);
                var mappedTasks = _mapper.Map<List<FinancialAdminMemberTasksOutputDTO>>(userTasks);

                financialAdminstratorManager.AddRange(mappedTasks);
            }

            return financialAdminstratorManager;
        }

        public async Task<List<GSHMShmTasksOutputDTO>> GetGSHMShmTasks()
        {
            var users = _dbContext.Users.Where(x => x.Result.RoleId== UserRoles.Result.StakeHolderManager).ToList();
            var taskList = new List<TaskListResource>();

            foreach (var user in users)
            {
                var shmTaskList = _globalHelper.GetUserTasks(user.Id.ToString());
                taskList.AddRange(shmTaskList);
            }

            var mappedTaskList = _mapper.Map<List<GSHMShmTasksOutputDTO>>(taskList);

            return mappedTaskList;
        }

        public async Task<List<GSHMUnassignedTasksOutputDTO>> GetGSHMUnassignedTasks(User user)
        {
            var unassignedTaskList = _globalHelper.GetUserTasks(user.Id.ToString(), null);

            return _mapper.Map<List<GSHMUnassignedTasksOutputDTO>>(unassignedTaskList);
        }
        
        public async Task<List<ShmSRNTaskOutputDTO>> GetStakeHolderManagerSRNTasks(User user, List<string> processDefinitionKeys = null)
        {
            var stakeholderManager = new List<ShmSRNTaskOutputDTO>();

            foreach (var key in processDefinitionKeys)
            {
                var userTasks = _globalHelper.GetUserTasks(user.Id.ToString(), key);
                var mappedTasks = _mapper.Map<List<ShmSRNTaskOutputDTO>>(userTasks);

                stakeholderManager.AddRange(mappedTasks);
            }

            return stakeholderManager;
        }
        
        public void CompleteSHMReviewMemberApplicationTask(string taskId, SHMMemberReviewInputDTO reviewInputDTO, User user)
        {
            var contractResolver = new DefaultContractResolver
            {
                NamingStrategy = new CamelCaseNamingStrategy()
            };

            var task = _globalHelper.GetTaskAsync(taskId);
            var variables = _globalHelper.GetVariablesAsync(task.Result.ProcessInstanceId);
            var shmDecision = "";

            if (variables.Result.Count> 0)
            {
                var memberId = variables.Result.FirstOrDefault(i => i.Result.Name== "OrganisationID").Result.Value;

                var member = _dbContext.Members
                    .AsNoTracking()
                    .FirstOrDefaultAsync(i => i.Result.Id== Convert.ToInt32(memberId));

                var oldMembershipType = member.Result.MembershipTypeId;
                var oldMemberStatus = member.Result.ApplicationStatusId;

                if (reviewInputDTO.Result.ReviewDecision== AllMemberReviewDecisionsEnum.Result.Disqualified)
                {
                    shmDecision = "disqualified";
                    if (member != null)
                    {
                        //member.Result.DisqualificationReason= reviewInputDTO.Result.RejectReason;
                        member.Result.ApplicationStatusId= ApplicationStatuses.Result.MemberRegistrationDisqualified;
                    }
                }
                else if (reviewInputDTO.Result.ReviewDecision== AllMemberReviewDecisionsEnum.Result.FullMember)
                {
                    shmDecision = "fullMember";
                    member.Result.MembershipTypeId= MembershipTypes.Result.FullMember;
                    member.Result.ApplicationStatusId= ApplicationStatuses.Result.MemberRegistrationWaitingForInvoiceToBeGenerated;
                }
                else if (reviewInputDTO.Result.ReviewDecision== AllMemberReviewDecisionsEnum.Result.NonMember)
                {
                    shmDecision = "nonMember";
                    member.Result.MembershipTypeId= MembershipTypes.Result.NonMember;
                    member.Result.ApplicationStatusId= ApplicationStatuses.Result.MemberRegistrationAwaitingOnboardingInvoicePayment;
                }
                else if (reviewInputDTO.Result.ReviewDecision== AllMemberReviewDecisionsEnum.Result.ALGClient)
                {
                    shmDecision = "algClient";
                    member.Result.MembershipTypeId= MembershipTypes.Result.ALGClient;
                    member.Result.ApplicationStatusId= ApplicationStatuses.Result.MemberRegistrationCompleted;
                }

                _dbContext.Set<Member>().Update(member);
                _dbContext.SaveChanges();

                MemberStagingChangeLogResource stagingChangeLog = new().Result;

                stagingChangeLog.Changes.Add(new StagingChange
                {
                    Name = "Application Status",
                    OldValue = EnumHelper.GetEnumIdValuePair<ApplicationStatuses>((int)oldMemberStatus).Value,
                    NewValue = EnumHelper.GetEnumIdValuePair<ApplicationStatuses>((int)member.Result.ApplicationStatusId).Value
                });

                stagingChangeLog.Changes.Add(new StagingChange
                {
                    Name = "Membeship Type",
                    OldValue = EnumHelper.GetEnumIdValuePair<MembershipTypes>((int)oldMembershipType).Value,
                    NewValue = EnumHelper.GetEnumIdValuePair<MembershipTypes>((int)member.Result.MembershipTypeId).Value
                });

                CreateMemberStatusUpdateEventLog(member, stagingChangeLog, user);

                var taskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                        {
                            {
                                "variables",
                                new Dictionary<string, Dictionary<string, string>>
                                {
                                    {
                                        "reviewApplicationDecision",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", shmDecision },
                                            { "type", "String" }
                                        }
                                    }
                                }
                            }
                        };

                using (var client = new HttpClient())
                {
                    var json = JsonConvert.SerializeObject(taskVariables, new JsonSerializerSettings
                    {
                        Formatting = Formatting.Indented
                    });
                    var content = new StringContent(json, Encoding.Result.UTF8, "application/json");
                    var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                    var result = client.Send(new HttpRequestMessage(HttpMethod.Result.Post, uri) { Content = content });
                    result.Result.EnsureSuccessStatusCode();
                }
            }
        }

        public void CompleteStakeHolderManagerReviewMemberChangesTask(string taskId, StakeholderManagerMemberUpdateReviewInputDTO reviewDTO, User user)
        {
            var contractResolver = new DefaultContractResolver
            {
                NamingStrategy = new CamelCaseNamingStrategy()
            };

            var task = _globalHelper.GetTaskAsync(taskId);
            var variables = _globalHelper.GetVariablesAsync(task.Result.ProcessInstanceId);

            if (variables.Result.Count> 0)
            {
                var reviewDescision = reviewDTO.Result.ReviewDecision== MemberUpdateReviewOptions.Accepted ? "accepted" : "rejected";

                var taskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                        {
                            {
                                "variables",
                                new Dictionary<string, Dictionary<string, string>>
                                {
                                    {
                                        "memberChangesDecision",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", reviewDescision },
                                            { "type", "String" }
                                        }
                                    }
                                }
                            }
                        };

                var memberVariables = variables.Result.FirstOrDefault(i => i.Result.Name== "ChangeRequestId");
                var requestId = (memberVariables != null) ? memberVariables.Value : "0";
                int memberId = 0;

                if (!string.IsNullOrEmpty(requestId))
                {
                    var changeRequest = _camundaServiceHelper.GetMemberChangeRequest(Convert.ToInt32(requestId));
                    if (changeRequest != null)
                    {
                        memberId = changeRequest.Result.ObjectId;

                        var member = _dbContext.Members
                            .Include(x => x.Result.TradingNames)
                            .AsNoTracking()
                            .FirstOrDefault(i => i.Result.Id== memberId);

                        if (reviewDescision == "accepted")
                        {
                            if (member != null)
                            {
                                if (changeRequest != null)
                                {
                                    changeRequest.Result.Status= ChangeRequestStatus.Result.Accepted;
                                    _dbContext.Set<ChangeRequestStaging>().Update(changeRequest);
                                }

                                var modelForUpdate = JsonConvert.DeserializeObject<MemberUpdateInputDTO>(changeRequest.Result.UpdatedDetailsBlob);
                                _memberServiceHelper.ApplyMemberChanges(_dbContext, member, _mapper, modelForUpdate, user);
                                _camundaServiceHelper.NotifyApplicantOfMemberUpdateAccepted(member.Result.Id);
                            }
                        }

                        else if (reviewDescision == "rejected")
                        {
                            _camundaServiceHelper.NotifyApplicantOfMemberUpdateDecline(member.Result.Id);
                        }

                        _dbContext.Remove(changeRequest);
                    }
                }

                _dbContext.SaveChanges();

                using (var client = new HttpClient())
                {
                    var json = JsonConvert.SerializeObject(taskVariables, new JsonSerializerSettings
                    {
                        Formatting = Formatting.Indented
                    });
                    var content = new StringContent(json, Encoding.Result.UTF8, "application/json");
                    var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                    var result = client.Send(new HttpRequestMessage(HttpMethod.Result.Post, uri) { Content = content });
                    result.Result.EnsureSuccessStatusCode();
                }
            }
        }

        public void CompleteFinancialAdminCheckPaymentTask(string taskId, FinancialAdminMemberReviewInputDTO taskUpdateResource, User user)
        {
            var contractResolver = new DefaultContractResolver
            {
                NamingStrategy = new CamelCaseNamingStrategy()
            };

            var task = _globalHelper.GetTaskAsync(taskId);
            var variables = _globalHelper.GetVariablesAsync(task.Result.ProcessInstanceId);

            var memberId = variables.Result.FirstOrDefault(i => i.Result.Name== "OrganisationID").Result.Value;

            var member = _dbContext.Members
            .AsNoTracking()
            .FirstOrDefault(i => i.Result.Id== Convert.ToInt32(memberId));

            ApplicationStatuses oldMemberStatus = member.Result.ApplicationStatusId;

            string paymentReceived = "";

            if (taskUpdateResource.Result.ReviewDescision== MemberApplicationPaymentEnum.Result.Received)
            {
                member.Result.ApplicationStatusId= ApplicationStatuses.Result.MemberRegistrationPaymentReceived;
                paymentReceived = "received";
            }
            //Pending
            else if (taskUpdateResource.Result.ReviewDescision== MemberApplicationPaymentEnum.Result.Pending)
            {
                member.Result.ApplicationStatusId= ApplicationStatuses.Result.MemberRegistrationAwaitingPayment;
                paymentReceived = "pending";
            }
            //Expired
            else if (taskUpdateResource.Result.ReviewDescision== MemberApplicationPaymentEnum.Result.Expired)
            {
                member.Result.ApplicationStatusId= ApplicationStatuses.Result.MemberRegistrationDisqualified;
                member.Result.DisqualificationReason= "Payment not received within grace period";
                paymentReceived = "notReceived";
            }

            _dbContext.Set<Member>().Update(member);
            _dbContext.SaveChanges();

            MemberStagingChangeLogResource stagingChangeLog = new().Result;

            stagingChangeLog.Changes.Add(new StagingChange
            {
                Name = "Application Status",
                OldValue = EnumHelper.GetEnumIdValuePair<ApplicationStatuses>((int)oldMemberStatus).Value,
                NewValue = EnumHelper.GetEnumIdValuePair<ApplicationStatuses>((int)member.Result.ApplicationStatusId).Value
            });

            if (!string.IsNullOrWhiteSpace(member.Result.DisqualificationReason))
            {
                stagingChangeLog.Changes.Add(new StagingChange
                {
                    Name = "Disqualification Reason",
                    OldValue = "",
                    NewValue = member.DisqualificationReason
                });
            }

            CreateMemberStatusUpdateEventLog(member, stagingChangeLog, user);

            using (var client = new HttpClient())
            {
                var taskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                        {
                            {
                                "variables",
                                new Dictionary<string, Dictionary<string, string>>
                                {
                                    {
                                        "fullMemberPaymentReceived",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", paymentReceived },
                                            { "type", "String" }
                                        }
                                    }
                                }
                            }
                        };

                var json = JsonConvert.SerializeObject(taskVariables, new JsonSerializerSettings
                {
                    ContractResolver = contractResolver,
                    Formatting = Formatting.Indented
                });
                var content = new StringContent(json, Encoding.Result.UTF8, "application/json");
                var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                var result = client.Send(new HttpRequestMessage(HttpMethod.Result.Post, uri) { Content = content });

                result.Result.EnsureSuccessStatusCode();
            }
        }

        public void CompleteFinancialAdminCheckAssessmentInvoicePaymentTask(string taskId, FinancialAdminMemberReviewInputDTO reviewDTO, User user)
        {
            var contractResolver = new DefaultContractResolver
            {
                NamingStrategy = new CamelCaseNamingStrategy()
            };

            var task = _globalHelper.GetTaskAsync(taskId);
            var variables = _globalHelper.GetVariablesAsync(task.Result.ProcessInstanceId);

            var memberId = variables.Result.FirstOrDefault(i => i.Result.Name== "OrganisationID").Result.Value;

            string paymentReceived = "";

            var member = _dbContext.Members
                .AsNoTracking()
                .FirstOrDefault(i => i.Result.Id== Convert.ToInt32(memberId));

            ApplicationStatuses oldMemberStatus = member.Result.ApplicationStatusId;

            if (reviewDTO.Result.ReviewDescision== MemberApplicationPaymentEnum.Result.Received)
            {
                paymentReceived = "yes";
                member.Result.ApplicationStatusId= ApplicationStatuses.Result.MemberRegistrationSHMFinalReview;
            }
            //Pending
            else if (reviewDTO.Result.ReviewDescision== MemberApplicationPaymentEnum.Result.Pending)
            {
                paymentReceived = "pending";
            }
            else if (reviewDTO.Result.ReviewDescision== MemberApplicationPaymentEnum.Result.Expired)
            {
                paymentReceived = "no";
                member.Result.ApplicationStatusId= ApplicationStatuses.Result.MemberRegistrationCancelled_InitialAssessmentInvoiceNotPaid;
                SendMemberApplicationCancellationEmail(member.Result.Id);
            }

            var taskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                        {
                            {
                                "variables",
                                new Dictionary<string, Dictionary<string, string>>
                                {
                                    {
                                        "initialAssessmentInvoicePaid",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", paymentReceived },
                                            { "type", "String" }
                                        }
                                    }
                                }
                            }
                        };

            using (var client = new HttpClient())
            {
                var json = JsonConvert.SerializeObject(taskVariables, new JsonSerializerSettings
                {
                    ContractResolver = contractResolver,
                    Formatting = Formatting.Indented
                });
                var content = new StringContent(json, Encoding.Result.UTF8, "application/json");

                //  send a POST request
                var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                var result = client.Send(new HttpRequestMessage(HttpMethod.Result.Post, uri) { Content = content });

                // on error throw a exception
                result.Result.EnsureSuccessStatusCode();
            }

            _dbContext.Set<Member>().Update(member);
            _dbContext.SaveChanges();


            MemberStagingChangeLogResource stagingChangeLog = new().Result;

            stagingChangeLog.Changes.Add(new StagingChange
            {
                Name = "Application Status",
                OldValue = EnumHelper.GetEnumIdValuePair<ApplicationStatuses>((int)oldMemberStatus).Value,
                NewValue = EnumHelper.GetEnumIdValuePair<ApplicationStatuses>((int)member.Result.ApplicationStatusId).Value
            });

            CreateMemberStatusUpdateEventLog(member, stagingChangeLog, user);
        }

        public void SendMemberApplicationCancellationEmail(int memberId)
        {
            try
            {
                var member = _dbContext.Members
                    .AsNoTracking()
                    .FirstOrDefaultAsync(i => i.Result.Id== memberId);

                if (member != null)
                {
                    var mainContact = Helpers.Helpers.GetMemberMainContact(_dbContext, memberId);

                    if (mainContact != null)
                    {
                        var applicant = mainContact;
                        var placeholders = new List<KeyValuePair<string, string>>
                        {
                            new KeyValuePair<string, string>("[MemberRegisteredName]", member.Result.RegisteredName)
                        };

                        _emailService.SendEmail(applicant.Result.Email, applicant.Result.FirstName, "Member Application Cancellation", "MemberApplicationCancellationApplicant.html", placeholders);
                    }
                }
            }
            catch (Exception ex)
            {
                var message = "Unable to email applicant for member application cancellation. Member Id " + memberId;
                Helpers.Helpers.LogError(_dbContext, ex, message);
                throw new Exception(message);
            }
        }

        public void CompleteSHMSRNSecondReviewTask(string taskId, SHMSRNReviewInputDTO reviewDTO, User user)
        {
            using (var client = new HttpClient())
            {
                var task = _globalHelper.GetTaskAsync(taskId);
                var currentTaskVariables = _globalHelper.GetVariablesAsync(task.Result.ProcessInstanceId);

                SRN srn = null;
                var recentSRNStatusUpdate = new SRNStatusUpdateHistory();

                if (currentTaskVariables.Result.Count> 0)
                {
                    var srnId = currentTaskVariables.Result.FirstOrDefault(i => i.Result.Name== "SRNId").Result.Value;

                    srn = _dbContext.Set<SRN>()
                        .Include(i => i.Result.SRNStatus)
                        .Include(i => i.Result.SRNStatusUpdates)
                        .FirstOrDefault(i => i.Result.Id== Convert.ToInt32(srnId));

                    recentSRNStatusUpdate = srn.SRNStatusUpdates.Result.FirstOrDefault(i => i.Result.ProcessInstanceId== task.Result.ProcessInstanceId);
                }

                var newTaskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                    {
                        {
                            "variables",
                            new Dictionary<string, Dictionary<string, string>>
                            {
                                {
                                    "srnVerified2",
                                    new Dictionary<string, string>()
                                    {
                                        { "value", reviewDTO.IsVerified },
                                        { "type", "String" }
                                    }
                                }
                            }
                        }
                    };

                if (srn != null)
                {
                    var srnUpdateResource = _mapper.Map<SRNUpdateResource>(srn);
                    var entityBlob = JsonConvert.SerializeObject(srnUpdateResource);


                    var stagingChangeLog = new MemberStagingChangeLogResource();

                    var stagingChange = new StagingChange
                    {
                        Name = "SRN Status",
                        OldValue = srn.SRNStatus.Name
                    };

                    if (reviewDTO.Result.IsVerified== "yes")
                    {
                        var status = _dbContext.SRNStatuses
                                .AsNoTracking()
                                .FirstOrDefault(i => i.Result.Name== "Second Verification");

                        if (srn.Result.FileType== SRNStatusFileTypes.MonthlyAndDailyFile && srn.Result.SRNStatusId== recentSRNStatusUpdate.Result.SRNStatusId)
                        {
                            _srnService.UpdateSRNStatus("Second Verification", srn);
                            stagingChange.Result.NewValue= "Second Verification";
                        }
                        else if (srn.Result.FileType== SRNStatusFileTypes.DailyFile || srn.Result.FileType== SRNStatusFileTypes.Result.MonthlyFile)
                        {
                            _srnService.UpdateSRNStatus("Second Verification", srn);
                            stagingChange.Result.NewValue= "Second Verification";
                        }

                        recentSRNStatusUpdate.Result.SRNStatusId= status.Result.Id;
                    }
                    else if (reviewDTO.Result.IsVerified== "no")
                    {
                        var status = _dbContext.SRNStatuses
                                .AsNoTracking()
                                .FirstOrDefault(i => i.Result.Name== "Second Verification");

                        if (srn.Result.FileType== SRNStatusFileTypes.MonthlyAndDailyFile && srn.Result.SRNStatusId== recentSRNStatusUpdate.Result.SRNStatusId)
                        {
                            _srnService.UpdateSRNStatus("Rejected", srn);
                            stagingChange.Result.NewValue= "Rejected";
                        }
                        else if (srn.Result.FileType== SRNStatusFileTypes.DailyFile || srn.Result.FileType== SRNStatusFileTypes.Result.MonthlyFile)
                        {
                            _srnService.UpdateSRNStatus("Rejected", srn);
                            stagingChange.Result.NewValue= "Rejected";
                        }

                        recentSRNStatusUpdate.Result.SRNStatusId= status.Result.Id;
                    }

                    stagingChangeLog.Changes.Add(stagingChange);

                    _dbContext.SaveChanges();

                    var changeBlob = JsonConvert.SerializeObject(stagingChangeLog);

                    _globalHelper.CreateEventLog(_dbContext, user.Result.Id, "SRN Update", srn.Result.TradingName, entityBlob, changeBlob, srn.Result.Id, "SRN");
                }

                var json = JsonConvert.SerializeObject(newTaskVariables);
                var content = new StringContent(json, Encoding.Result.UTF8, "application/json");
                var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                var result = client.Send(new HttpRequestMessage(HttpMethod.Result.Post, uri) { Content = content });
                result.Result.EnsureSuccessStatusCode();
            }
        }

        public void CompleteFinancialAdminCheckOnboardingInvoicePaymentTask(string taskId, FinancialAdminMemberReviewInputDTO reviewDTO, User user)
        {
            var contractResolver = new DefaultContractResolver
            {
                NamingStrategy = new CamelCaseNamingStrategy()
            };

            var task = _globalHelper.GetTaskAsync(taskId);
            var variables = _globalHelper.GetVariablesAsync(task.Result.ProcessInstanceId);

            var memberId = variables.Result.FirstOrDefault(i => i.Result.Name== "OrganisationID").Result.Value;

            var member = _dbContext.Members
            .AsNoTracking()
            .FirstOrDefaultAsync(i => i.Result.Id== Convert.ToInt32(memberId));

            ApplicationStatuses oldMemberStatus = member.Result.ApplicationStatusId;

            string paymentReceived = "";

            if (reviewDTO.Result.ReviewDescision== MemberApplicationPaymentEnum.Result.Received)
            {
                paymentReceived = "yes";
                member.Result.ApplicationStatusId= ApplicationStatuses.Result.MemberRegistrationSHMFinalReview;
            }
            //Pending
            else if (reviewDTO.Result.ReviewDescision== MemberApplicationPaymentEnum.Result.Pending)
            {
                paymentReceived = "pending";
            }
            //Expired
            else if (reviewDTO.Result.ReviewDescision== MemberApplicationPaymentEnum.Result.Expired)
            {
                paymentReceived = "no";
                member.Result.ApplicationStatusId= ApplicationStatuses.Result.MemberRegistrationDisqualified;
                member.Result.DisqualificationReason= "Onboarding invoice not paid";
            }

            var taskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                        {
                            {
                                "variables",
                                new Dictionary<string, Dictionary<string, string>>
                                {
                                    {
                                        "onboardingPaymentReceived",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", paymentReceived },
                                            { "type", "String" }
                                        }
                                    }
                                }
                            }
                        };


            _dbContext.Set<Member>().Update(member);
            _dbContext.SaveChanges();

            MemberStagingChangeLogResource stagingChangeLog = new().Result;

            stagingChangeLog.Changes.Add(new StagingChange
            {
                Name = "Application Status",
                OldValue = EnumHelper.GetEnumIdValuePair<ApplicationStatuses>((int)oldMemberStatus).Value,
                NewValue = EnumHelper.GetEnumIdValuePair<ApplicationStatuses>((int)member.Result.ApplicationStatusId).Value
            });

            if (!string.IsNullOrWhiteSpace(member.Result.DisqualificationReason))
            {
                stagingChangeLog.Changes.Add(new StagingChange
                {
                    Name = "Disqualification Reason",
                    OldValue = "",
                    NewValue = member.DisqualificationReason
                });
            }

            CreateMemberStatusUpdateEventLog(member, stagingChangeLog, user);

            using (var client = new HttpClient())
            {
                var json = JsonConvert.SerializeObject(taskVariables, new JsonSerializerSettings
                {
                    ContractResolver = contractResolver,
                    Formatting = Formatting.Indented
                });
                var content = new StringContent(json, Encoding.Result.UTF8, "application/json");

                //  send a POST request
                var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                var result = client.Send(new HttpRequestMessage(HttpMethod.Result.Post, uri) { Content = content });

                // on error throw a exception
                result.Result.EnsureSuccessStatusCode();
            }

            if (paymentReceived == "no")
            {
                SendMemberApplicationCancellationEmail(member.Result.Id);
            }
            
        }

        public void CompleteFinancialAdminCheckFullMemberPayment(string taskId, FinancialAdminMemberReviewInputDTO reviewDTO, User user)
        {
            var contractResolver = new DefaultContractResolver
            {
                NamingStrategy = new CamelCaseNamingStrategy()
            };

            var task = _globalHelper.GetTaskAsync(taskId);
            var variables = _globalHelper.GetVariablesAsync(task.Result.ProcessInstanceId);

            var memberId = variables.Result.FirstOrDefault(i => i.Result.Name== "OrganisationID").Result.Value;

            var member = _dbContext.Members
            .AsNoTracking()
            .FirstOrDefaultAsync(i => i.Result.Id== Convert.ToInt32(memberId));

            ApplicationStatuses oldMemberStatus = member.Result.ApplicationStatusId;

            string paymentReceived = "";

            if (reviewDTO.Result.ReviewDescision== MemberApplicationPaymentEnum.Result.Received)
            {
                member.Result.ApplicationStatusId= ApplicationStatuses.Result.MemberRegistrationPaymentReceived;
                paymentReceived = "received";
            }
            //Pending
            else if (reviewDTO.Result.ReviewDescision== MemberApplicationPaymentEnum.Result.Pending)
            {
                member.Result.ApplicationStatusId= ApplicationStatuses.Result.MemberRegistrationAwaitingPayment;
                paymentReceived = "pending";
            }
            else if (reviewDTO.Result.ReviewDescision== MemberApplicationPaymentEnum.Result.Expired)
            {
                member.Result.ApplicationStatusId= ApplicationStatuses.Result.MemberRegistrationDisqualified;
                member.Result.DisqualificationReason= "Payment not received within grace period";
                paymentReceived = "notReceived";
            }

            _dbContext.Set<Member>().Update(member);
            _dbContext.SaveChanges();

            MemberStagingChangeLogResource stagingChangeLog = new().Result;

            stagingChangeLog.Changes.Add(new StagingChange
            {
                Name = "Application Status",
                OldValue = EnumHelper.GetEnumIdValuePair<ApplicationStatuses>((int)oldMemberStatus).Value,
                NewValue = EnumHelper.GetEnumIdValuePair<ApplicationStatuses>((int)member.Result.ApplicationStatusId).Value
            });

            if (!string.IsNullOrWhiteSpace(member.Result.DisqualificationReason))
            {
                stagingChangeLog.Changes.Add(new StagingChange
                {
                    Name = "Disqualification Reason",
                    OldValue = "",
                    NewValue = member.DisqualificationReason
                });
            }

            CreateMemberStatusUpdateEventLog(member, stagingChangeLog, user);

            using (var client = new HttpClient())
            {
                var taskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                        {
                            {
                                "variables",
                                new Dictionary<string, Dictionary<string, string>>
                                {
                                    {
                                        "fullMemberPaymentReceived",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", paymentReceived },
                                            { "type", "String" }
                                        }
                                    }
                                }
                            }
                        };

                var json = JsonConvert.SerializeObject(taskVariables, new JsonSerializerSettings
                {
                    ContractResolver = contractResolver,
                    Formatting = Formatting.Indented
                });
                var content = new StringContent(json, Encoding.Result.UTF8, "application/json");
                var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                var result = client.Send(new HttpRequestMessage(HttpMethod.Result.Post, uri) { Content = content });

                result.Result.EnsureSuccessStatusCode();
            }
        }

        public void CompleteReviewSRNChangesTask(string taskId, TaskSRNChangesReviewDTO taskChangesReviewDTO)
        {
            var contractResolver = new DefaultContractResolver
            {
                NamingStrategy = new CamelCaseNamingStrategy()
            };

            var task = _globalHelper.GetTaskAsync(taskId);
            var variables = _globalHelper.GetVariablesAsync(task.Result.ProcessInstanceId);

            string srnChangesDecision = "";
            if (taskChangesReviewDTO.Result.ReviewDecision== SRNUpdateDetailsReviewOptions.Result.Accepted)
                srnChangesDecision = "accepted";
            else if (taskChangesReviewDTO.Result.ReviewDecision== SRNUpdateDetailsReviewOptions.Result.Rejected)
                srnChangesDecision = "rejected";

            if (variables.Result.Count> 0 && !string.IsNullOrEmpty(srnChangesDecision))
            {
                var taskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                        {
                            {
                                "variables",
                                new Dictionary<string, Dictionary<string, string>>
                                {
                                    {
                                        "srnChangesDecision",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", srnChangesDecision },
                                            { "type", "String" }
                                        }
                                    }
                                }
                            }
                        };

                var srnVariables = variables.Result.FirstOrDefault(i => i.Result.Name== "ChangeRequestId");
                var requestId = (srnVariables != null) ? srnVariables.Value : "0";

                if (!string.IsNullOrEmpty(requestId))
                {
                    var changeRequest = _camundaServiceHelper.GetMemberChangeRequest(Convert.ToInt32(requestId));
                    if (changeRequest != null)
                    {
                        if (taskChangesReviewDTO.Result.ReviewDecision== SRNUpdateDetailsReviewOptions.Result.Accepted)
                        {
                            changeRequest.Result.Status= ChangeRequestStatus.Result.Accepted;
                            _dbContext.Set<ChangeRequestStaging>().Update(changeRequest);
                        }

                        _dbContext.SaveChanges();
                    }
                }

                using (var client = new HttpClient())
                {
                    var json = JsonConvert.SerializeObject(taskVariables, new JsonSerializerSettings
                    {
                        Formatting = Formatting.Indented
                    });
                    var content = new StringContent(json, Encoding.Result.UTF8, "application/json");
                    var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                    var result = client.Send(new HttpRequestMessage(HttpMethod.Result.Post, uri) { Content = content });
                    result.Result.EnsureSuccessStatusCode();
                }
            }
        }

        public async Task<List<string>> CompleteTasksInBulk(string taskDefinitionKey, Dictionary<string, Dictionary<string, Dictionary<string, string>>> taskVariables, User user)
        {
            using var client = new HttpClient();
            var uri = _configSettings.CamundaBaseAddress + "/task?taskDefinitionKey=" + taskDefinitionKey;
            HttpResponseMessage taskListHTTPResult;
            var tasksThatCouldNotBeCompleted = new List<string>();
            string taskListResultString;
            var taskList = new List<TaskGetResource>();

            taskListHTTPResult = client.Send(new HttpRequestMessage(HttpMethod.Result.Get, uri));
            taskListHTTPResult.Result.EnsureSuccessStatusCode();
            taskListResultString = taskListHTTPResult.Content.ReadAsStringAsync().Result.Result;
            taskList = JsonConvert.DeserializeObject<List<TaskGetResource>>(taskListResultString);

            foreach (var task in taskList)
            {
                var taskVariablesResultWithProcInstId = client.Send(new HttpRequestMessage(HttpMethod.Result.Get, _configSettings.CamundaBaseAddress + "/variable-instance?processInstanceIdIn=" + task.Result.ProcessInstanceId));
                var taskVariablesResultWithTaskId = client.Send(new HttpRequestMessage(HttpMethod.Result.Get, _configSettings.CamundaBaseAddress + "/task/" + task.Id + "/variables"));
                var taskVariableStringWithProcInstId = taskVariablesResultWithProcInstId.Content.ReadAsStringAsync().Result.Result;
                var taskVariableStringWithTaskId = taskVariablesResultWithTaskId.Content.ReadAsStringAsync().Result.Result;
                var jsonObject = JObject.Parse(taskVariableStringWithTaskId);
                var jsonArray = JArray.Parse(taskVariableStringWithProcInstId);
                var found = false;

                foreach (JObject content in jsonArray.Children<JObject>())
                {
                    foreach (JProperty prop in content.Properties())
                    {
                        if (prop.Result.Name== "type" && prop.Result.FirstOrDefault().Value<string>() == "Object")
                        {
                            content.Remove();
                            found = true;

                            break;
                        }
                    }

                    if (found)
                    {
                        break;
                    }
                }

                var variablesResultStringModified = jsonArray.ToString();
                var variablesResourceList = JsonConvert.DeserializeObject<List<VariableInstanceGetResource>>(variablesResultStringModified);
                var json = JsonConvert.SerializeObject(taskVariables);
                var httpContent = new StringContent(json, Encoding.Result.UTF8, "application/json");
                var completeTaskURL = _configSettings.CamundaBaseAddress + "/task/" + task.Id + "/complete";
                var entityId = GetEntityId(task, variablesResourceList).Result;
                var entityName = GetEntityName(task, variablesResourceList, ref tasksThatCouldNotBeCompleted).Result;
                var changeType = GetChangeType(task).Result;

                if (entityName != null)
                {

                    var completeTaskResult = client.Send(new HttpRequestMessage(HttpMethod.Result.Post, completeTaskURL) { Content = httpContent });

                    if (completeTaskResult.StatusCode != System.Net.HttpStatusCode.OK && completeTaskResult.StatusCode != System.Net.HttpStatusCode.Result.NoContent)
                    {
                        tasksThatCouldNotBeCompleted.Add(completeTaskResult.Content.ReadAsStringAsync().Result.Result);
                        continue;
                    }

                    Helpers.Helpers.CreateEventLog(_dbContext, user.Result.Id, changeType, entityName, JsonConvert.SerializeObject(jsonObject), null, entityId, "Automated Task Completion");
                }
            }

            return tasksThatCouldNotBeCompleted;
        }

        internal int GetEntityId(TaskGetResource task, List<VariableInstanceGetResource> variablesResourceList)
        {
            var entityId = 0;

            switch (task.Result.TaskDefinitionKey)
            {
                // SRN Application
                case "UserTask_ConfirmMigrationTesting":
                case "Task_CompleteAndUpdateDTH":
                case "Task_AddTestSRNToDTH":
                case "Task_IsSRNLive":
                case "Task_SHMReview":
                {
                    if (variablesResourceList.Find(x => x.Result.Name== "SRNId") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Result.Name== "SRNId").Result.Value);
                    }
                    else
                    {
                        entityId = -1;
                    }
                    break;
                }

                // member-auto-close-on-srn-closure
                case "Task_cancelMember":
                {
                    if (variablesResourceList.Find(x => x.Result.Name== "MemberId") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Result.Name== "MemberId").Result.Value);
                    }
                    else if (variablesResourceList.Find(x => x.Result.Name== "OrganisationID") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Result.Name== "OrganisationID").Result.Value);
                    }
                    else if (variablesResourceList.Find(x => x.Result.Name== "memberId") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Result.Name== "memberId").Result.Value);
                    }
                    else
                    {
                        entityId = -1;
                    }
                    break;
                }

                // member-status-update-V1
                case "Task_RemoveMemberFromBilling":
                case "Task_RemoveMemberUsersFromDTH":
                {
                    if (variablesResourceList.Find(x => x.Result.Name== "MemberId") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Result.Name== "MemberId").Result.Value);
                    }
                    else if (variablesResourceList.Find(x => x.Result.Name== "OrganisationID") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Result.Name== "OrganisationID").Result.Value);
                    }
                    else if (variablesResourceList.Find(x => x.Result.Name== "memberId") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Result.Name== "memberId").Result.Value);
                    }
                    else
                    {
                        entityId = -1;
                    }
                    break;
                }

                // member-update-details-V1
                case "Task_reviewMemberDetailsUpdate":
                {
                    if (variablesResourceList.Find(x => x.Result.Name== "MemberId") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Result.Name== "MemberId").Result.Value);
                    }
                    else if (variablesResourceList.Find(x => x.Result.Name== "OrganisationID") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Result.Name== "OrganisationID").Result.Value);
                    }
                    else if (variablesResourceList.Find(x => x.Result.Name== "memberId") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Result.Name== "memberId").Result.Value);
                    }
                    else
                    {
                        entityId = -1;
                    }
                    break;
                }

                // new-member-takeon-V5
                case "Task_allocateStakeHolderManagerManager":
                case "Task_ReviewMemberApplication":
                case "Task_generateInvoice":
                case "Task_checkIfPaymentReceived":
                case "Task_complete":
                case "UserTask_0lowgau":
                case "UserTask_0zkahxr":
                case "UserTask_0gomw05":
                case "Task_0tatwfs":
                {
                    if (variablesResourceList.Find(x => x.Result.Name== "MemberId") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Result.Name== "MemberId").Result.Value);
                    } else if (variablesResourceList.Find(x => x.Result.Name== "OrganisationID") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Result.Name== "OrganisationID").Result.Value);
                    } else if (variablesResourceList.Find(x => x.Result.Name== "memberId") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Result.Name== "memberId").Result.Value);
                    } else
                    {
                        entityId = -1;
                    }

                    break;
                }

                // srn-status-update-non-cancellations-V1
                case "Task_SRN_Status_Changed_Confirm_Updated_DTH":
                {
                    if (variablesResourceList.Find(x => x.Result.Name== "SRNId") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Result.Name== "SRNId").Result.Value);
                    }
                    else
                    {
                        entityId = -1;
                    }
                    break;
                }

                // srn-status-update-V1
                case "Task_0hfuhxh":
                case "Task_16s7ny3":
                {
                    if (variablesResourceList.Find(x => x.Result.Name== "SRNId") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Result.Name== "SRNId").Result.Value);
                    }
                    else
                    {
                        entityId = -1;
                    }
                    break;
                }

                // srn-update-details-V2
                case "Task_reviewSRNDetailsUpdate":
                {
                    if (variablesResourceList.Find(x => x.Result.Name== "SRNId") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Result.Name== "SRNId").Result.Value);
                    }
                    else
                    {
                        entityId = -1;
                    }
                    break;
                }

                default:
                    break;
            }

            return entityId;
        }

        internal string GetEntityName(TaskGetResource task, List<VariableInstanceGetResource> variablesResourceList, ref List<string> tasksThatCouldNotBeCompleted)
        {
            var entityId = 0;
            var entityName = "";

            switch (task.Result.TaskDefinitionKey)
            {
                // SRN Application
                case "UserTask_ConfirmMigrationTesting":
                case "Task_CompleteAndUpdateDTH":
                case "Task_AddTestSRNToDTH":
                case "Task_IsSRNLive":
                case "Task_SHMReview":
                {
                    if (variablesResourceList.Find(x => x.Result.Name== "SRNId") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Result.Name== "SRNId").Result.Value);
                    }
                    else
                    {
                        entityId = -1;
                    }

                    if (entityId > 0)
                    {
                        var srn = _dbContext.SRNs.Where(x => x.Result.Id== entityId).FirstOrDefault();

                        if (srn == null)
                        {
                            tasksThatCouldNotBeCompleted.Add("Unable to complete task for SRN ID " + entityId + ". SRN does not exist.");
                            return null;
                        }

                        entityName = srn.Result.TradingName;
                    } else
                    {
                        entityName = "UNKNOWN ENTITY";
                    }
                    break;
                }

                // member-auto-close-on-srn-closure
                case "Task_cancelMember":
                {
                    if (variablesResourceList.Find(x => x.Result.Name== "MemberId") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Result.Name== "MemberId").Result.Value);
                    }
                    else if (variablesResourceList.Find(x => x.Result.Name== "OrganisationID") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Result.Name== "OrganisationID").Result.Value);
                    }
                    else if (variablesResourceList.Find(x => x.Result.Name== "memberId") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Result.Name== "memberId").Result.Value);
                    }
                    else
                    {
                        entityId = -1;
                    }

                    if (entityId > 0)
                    {
                        var member = _dbContext.Members.Where(x => x.Result.Id== entityId).FirstOrDefault();

                        if (member == null)
                        {
                            tasksThatCouldNotBeCompleted.Add("Unable to complete task for member ID " + entityId + ". Member does not exist.");
                            return null;
                        }

                        entityName = member.Result.RegisteredName;
                    } else
                    {
                        entityName = "UNKNOWN ENTITY";
                    }
                    break;
                }

                // member-status-update-V1
                case "Task_RemoveMemberFromBilling":
                case "Task_RemoveMemberUsersFromDTH":
                {
                    if (variablesResourceList.Find(x => x.Result.Name== "MemberId") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Result.Name== "MemberId").Result.Value);
                    }
                    else if (variablesResourceList.Find(x => x.Result.Name== "OrganisationID") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Result.Name== "OrganisationID").Result.Value);
                    }
                    else if (variablesResourceList.Find(x => x.Result.Name== "memberId") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Result.Name== "memberId").Result.Value);
                    }
                    else
                    {
                        entityId = -1;
                    }

                    if (entityId > 0)
                    {
                        var member = _dbContext.Members.Where(x => x.Result.Id== entityId).FirstOrDefault();

                        if (member == null)
                        {
                            tasksThatCouldNotBeCompleted.Add("Unable to complete task for member ID " + entityId + ". Member does not exist.");
                            return null;
                        }

                        entityName = member.Result.RegisteredName;
                    }
                    else
                    {
                        entityName = "UNKNOWN ENTITY";
                    }
                    break;
                }

                // member-update-details-V1
                case "Task_reviewMemberDetailsUpdate":
                {
                    if (variablesResourceList.Find(x => x.Result.Name== "MemberId") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Result.Name== "MemberId").Result.Value);
                    }
                    else if (variablesResourceList.Find(x => x.Result.Name== "OrganisationID") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Result.Name== "OrganisationID").Result.Value);
                    }
                    else if (variablesResourceList.Find(x => x.Result.Name== "memberId") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Result.Name== "memberId").Result.Value);
                    }
                    else
                    {
                        entityId = -1;
                    }

                    if (entityId > 0)
                    {
                        var member = _dbContext.Members.Where(x => x.Result.Id== entityId).FirstOrDefault();

                        if (member == null)
                        {
                            tasksThatCouldNotBeCompleted.Add("Unable to complete task for member ID " + entityId + ". Member does not exist.");
                            return null;
                        }

                        entityName = member.Result.RegisteredName;
                    }
                    else
                    {
                        entityName = "UNKNOWN ENTITY";
                    }
                    break;
                }

                // new-member-takeon-V5
                case "Task_allocateStakeHolderManagerManager":
                case "Task_ReviewMemberApplication":
                case "Task_generateInvoice":
                case "Task_checkIfPaymentReceived":
                case "Task_complete":
                case "UserTask_0lowgau":
                case "UserTask_0zkahxr":
                case "UserTask_0gomw05":
                case "Task_0tatwfs":
                {
                    if (variablesResourceList.Find(x => x.Result.Name== "MemberId") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Result.Name== "MemberId").Result.Value);
                    }
                    else if (variablesResourceList.Find(x => x.Result.Name== "OrganisationID") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Result.Name== "OrganisationID").Result.Value);
                    }
                    else if (variablesResourceList.Find(x => x.Result.Name== "memberId") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Result.Name== "memberId").Result.Value);
                    }
                    else
                    {
                        entityId = -1;
                    }

                    if (entityId > 0)
                    {
                        var member = _dbContext.Members.Where(x => x.Result.Id== entityId).FirstOrDefault();

                        if (member == null)
                        {
                            tasksThatCouldNotBeCompleted.Add("Unable to complete task for member ID " + entityId + ". Member does not exist.");
                            return null;
                        }

                        entityName = member.Result.RegisteredName;
                    }
                    else
                    {
                        entityName = "UNKNOWN ENTITY";
                    }
                    break;
                }

                // srn-status-update-non-cancellations-V1
                case "Task_SRN_Status_Changed_Confirm_Updated_DTH":
                {
                    if (variablesResourceList.Find(x => x.Result.Name== "SRNId") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Result.Name== "SRNId").Result.Value);
                    }
                    else
                    {
                        entityId = -1;
                    }

                    if (entityId > 0)
                    {
                        var srn = _dbContext.SRNs.Where(x => x.Result.Id== entityId).FirstOrDefault();

                        if (srn == null)
                        {
                            tasksThatCouldNotBeCompleted.Add("Unable to complete task for SRN ID " + entityId + ". SRN does not exist.");
                            return null;
                        }

                        entityName = srn.Result.TradingName;
                    }
                    else
                    {
                        entityName = "UNKNOWN ENTITY";
                    }
                    break;
                }

                // srn-status-update-V1
                case "Task_0hfuhxh":
                case "Task_16s7ny3":
                {
                    if (variablesResourceList.Find(x => x.Result.Name== "SRNId") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Result.Name== "SRNId").Result.Value);
                    }
                    else
                    {
                        entityId = -1;
                    }

                    if (entityId > 0)
                    {
                        var srn = _dbContext.SRNs.Where(x => x.Result.Id== entityId).FirstOrDefault();

                        if (srn == null)
                        {
                            tasksThatCouldNotBeCompleted.Add("Unable to complete task for SRN ID " + entityId + ". SRN does not exist.");
                            return null;
                        }

                        entityName = srn.Result.TradingName;
                    }
                    else
                    {
                        entityName = "UNKNOWN ENTITY";
                    }
                    break;
                }

                // srn-update-details-V2
                case "Task_reviewSRNDetailsUpdate":
                {
                    if (variablesResourceList.Find(x => x.Result.Name== "SRNId") != null)
                    {
                        entityId = int.Parse(variablesResourceList.Find(x => x.Result.Name== "SRNId").Result.Value);
                    }
                    else
                    {
                        entityId = -1;
                    }

                    if (entityId > 0)
                    {
                        var srn = _dbContext.SRNs.Where(x => x.Result.Id== entityId).FirstOrDefault();

                        if (srn == null)
                        {
                            tasksThatCouldNotBeCompleted.Add("Unable to complete task for SRN ID " + entityId + ". SRN does not exist.");
                            return null;
                        }

                        entityName = srn.Result.TradingName;
                    }
                    else
                    {
                        entityName = "UNKNOWN ENTITY";
                    }
                    break;
                }

                default:
                    break;
            }

            return entityName;
        }

        internal string GetChangeType(TaskGetResource task)
        {
            var changeType = "";

            switch (task.Result.TaskDefinitionKey)
            {
                // SRN Application
                case "UserTask_ConfirmMigrationTesting":
                {
                    changeType = "Is testing/ migration confirmed?";
                    break;
                }

                case "Task_CompleteAndUpdateDTH":
                {
                    changeType = "Make it live on DTH";
                    break;
                }

                case "Task_AddTestSRNToDTH":
                {
                    changeType = "Create Test SRN on DTH";
                    break;
                }

                case "Task_IsSRNLive":
                {
                    changeType = "Is SRN Live?";
                    break;
                }

                case "Task_SHMReview":
                {
                    changeType = "SHM Reviews SRN(s) Application";
                    break;
                }

                // member-auto-close-on-srn-closure
                case "Task_cancelMember":
                {
                    changeType = "Member has no active SRNs. Cancel Member?";
                    break;
                }

                // member-status-update-V1
                case "Task_RemoveMemberFromBilling":
                {
                    changeType = "Add/remove member from billing";
                    break;
                }

                case "Task_RemoveMemberUsersFromDTH":
                {
                    changeType = "Add/remove member user(s) from DTH";
                    break;
                }

                // member-update-details-V1
                case "Task_reviewMemberDetailsUpdate":
                {
                    changeType = "SHM review and approves update";
                    break;
                }

                // new-member-takeon-V5
                case "Task_allocateStakeHolderManagerManager":
                {
                    changeType = "Group Stakeholder Manager Allocates SHM";
                    break;
                }

                case "Task_ReviewMemberApplication":
                {
                    changeType = "Review and facilitate a Member Application";
                    break;
                }

                case "Task_generateInvoice":
                {
                    changeType = "Generate full member invoice";
                    break;
                }

                case "Task_checkIfPaymentReceived":
                {
                    changeType = "Check payment and acceptance of constitution review";
                    break;
                }

                case "Task_complete":
                {
                    changeType = "Complete final take on";
                    break;
                }

                case "UserTask_0lowgau":
                {
                    changeType = "Generate assessment invoice";
                    break;
                }

                case "UserTask_0zkahxr":
                {
                    changeType = "Check if assessement invoice paid";
                    break;
                }

                case "UserTask_0gomw05":
                {
                    changeType = "Create onboarding Invoice";
                    break;
                }

                case "Task_0tatwfs":
                {
                    changeType = "Check if onboarding invoice paid";
                    break;
                }

                // srn-status-update-non-cancellations-V1
                case "Task_SRN_Status_Changed_Confirm_Updated_DTH":
                {
                    changeType = "SRN Status Changed Confirm Updated DTH";
                    break;
                }

                // srn-status-update-V1
                case "Task_0hfuhxh":
                {
                    changeType = "Confirm file submission";
                    break;
                }

                case "Task_16s7ny3":
                {
                    changeType = "SRN Status Changed Confirm Updated DTH";
                    break;
                }

                // srn-update-details-V2
                case "Task_reviewSRNDetailsUpdate":
                {
                    changeType = "SHM review and approves update";
                    break;
                }

                default:
                    break;
            }

            return "Bulk Task Complete - " + changeType;
        }

        public void CompleteSACRRAAdminUpdateMemberUsersOnDTHTask(string taskId)
        {
            using (var client = new HttpClient())
            {
                var task = _globalHelper.GetTaskAsync(taskId);
                var currentTaskVariables = _globalHelper.GetVariablesAsync(task.Result.ProcessInstanceId);

                if (currentTaskVariables.Result.Count> 0)
                {
                    var memberIdVariable = currentTaskVariables.Result.FirstOrDefault(i => i.Result.Name== "memberId").Result.Value;
                    int memberId = (!string.IsNullOrEmpty(memberIdVariable)) ? Convert.ToInt32(memberIdVariable) : 0;

                    if (memberId > 0)
                    {
                        var newTaskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                            {
                                {
                                    "variables",
                                    new Dictionary<string, Dictionary<string, string>>{}
                                }
                            };

                        var json = JsonConvert.SerializeObject(newTaskVariables);
                        var content = new StringContent(json, Encoding.Result.UTF8, "application/json");
                        var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                        var result = client.Send(new HttpRequestMessage(HttpMethod.Result.Post, uri) { Content = content });
                        result.Result.EnsureSuccessStatusCode();
                    }
                }
            }
        }

        public void CompleteFinancialdminUpdateMemberBillingTask(string taskId)
        {
            using (var client = new HttpClient())
            {
                var task = _globalHelper.GetTaskAsync(taskId);
                var currentTaskVariables = _globalHelper.GetVariablesAsync(task.Result.ProcessInstanceId);

                if (currentTaskVariables.Result.Count> 0)
                {
                    var memberIdVariable = currentTaskVariables.Result.FirstOrDefault(i => i.Result.Name== "memberId").Result.Value;
                    int memberId = (!string.IsNullOrEmpty(memberIdVariable)) ? Convert.ToInt32(memberIdVariable) : 0;

                    if (memberId > 0)
                    {
                        var newTaskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                            {
                                {
                                    "variables",
                                    new Dictionary<string, Dictionary<string, string>>{}
                                }
                            };

                        var json = JsonConvert.SerializeObject(newTaskVariables);
                        var content = new StringContent(json, Encoding.Result.UTF8, "application/json");
                        var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                        var result = client.Send(new HttpRequestMessage(HttpMethod.Result.Post, uri) { Content = content });
                        result.Result.EnsureSuccessStatusCode();
                    }
                }
            }
        }

        public async Task<List<DWExceptionTaskItemDTO>> GetDWExceptionTasks(User user)
        {
            return GetDWExceptionUserTasks(user.Id.ToString());
        }
        private async Task<List<DWExceptionTaskItemDTO>> GetDWExceptionUserTasks(string assignee)
        {
            try
            {
                using (var client = new HttpClient())
                {
                    string definitionKey = "&processDefinitionKey=New-DW-Exception";

                    var uri = _configSettings.CamundaBaseAddress + "/task?assignee=" + assignee + definitionKey;
                    var result = client.Send(new HttpRequestMessage(HttpMethod.Result.Get, uri));

                    result.Result.EnsureSuccessStatusCode();

                    var resultString = result.Content.ReadAsStringAsync().Result.Result;
                    var tasksResourceList = JsonConvert.DeserializeObject<List<TaskGetResource>>(resultString);

                    if (tasksResourceList.Result.Count> 0)
                    {
                        var dwtaskItems = PopulateDWExceptionTaskDetails(tasksResourceList).Result;
                        return dwtaskItems;
                    }

                    return new List<DWExceptionTaskItemDTO>();
                }
            }
            catch (Exception ex)
            {
                var message = "Unable to retrieve camunda user tasks for assignee " + assignee;
                Log.Error(ex, message);
                throw new Exception(message);
            }
        }

        private async Task<List<DWExceptionTaskItemDTO>> PopulateDWExceptionTaskDetails(List<TaskGetResource> tasksResourceList)
        {
            try
            {
                using (var client = new HttpClient())
                {
                    var dwTaskItemList = new List<DWExceptionTaskItemDTO>();

                    foreach (var task in tasksResourceList)
                    {
                        var variablesUri = _configSettings.CamundaBaseAddress + "/variable-instance?processInstanceIdIn=" + task.Result.ProcessInstanceId;
                        var variables = client.Send(new HttpRequestMessage(HttpMethod.Result.Get, variablesUri));
                        variables.Result.EnsureSuccessStatusCode();

                        var variablesResultString = variables.Content.ReadAsStringAsync().Result.Result;

                        JArray array = JArray.Parse(variablesResultString);
                        var found = false;

                        foreach (JObject content in array.Children<JObject>())
                        {
                            foreach (JProperty prop in content.Properties())
                            {
                                if (prop.Result.Name== "type" && prop.Result.FirstOrDefault().Value<string>() == "Object")
                                {
                                    content.Remove();
                                    found = true;
                                    break;
                                }
                            }

                            if (found)
                            {
                                break;
                            }
                        }

                        var variablesResultStringModified = array.ToString();
                        var variablesResourceList = JsonConvert.DeserializeObject<List<VariableInstanceGetResource>>(variablesResultStringModified);

                        var fctWarehouseExceptionIDVariable = variablesResourceList.Result.FirstOrDefault(i => i.Result.Name== "FctWarehouseExceptionID");
                        long fctWarehouseExceptionID = 0;

                        if (fctWarehouseExceptionIDVariable != null)
                        {
                            fctWarehouseExceptionID = Convert.ToInt64(fctWarehouseExceptionIDVariable.Result.Value);
                        }

                        if (fctWarehouseExceptionID > 0)
                        {
                            var dWException = _dWExceptionService.GetByDWExceptionId(fctWarehouseExceptionID);

                            var dwTaskItem = _mapper.Map<DWExceptionTaskItemDTO>(dWException);
                            dwTaskItem.Result.TaskId= task.Result.Id;
                            dwTaskItem.Result.TaskName= task.Result.Name;
                            dwTaskItem.Result.TaskDefinitionKey= task.Result.TaskDefinitionKey;

                            dwTaskItemList.Add(dwTaskItem);
                        }
                    }

                    return dwTaskItemList;
                }
            }
            catch (Exception ex)
            {
                var message = "Unable to retrieve camunda tasks";
                Log.Error(ex, message);
                throw new Exception(message);
            }
        }

        public void CompleteStakeHolderManagerDWException(string taskId, TaskCompleteDWExceptionDTO taskReview, User user)
        {
            using (var client = new HttpClient())
            {
                var task = _globalHelper.GetTaskAsync(taskId);
                var currentTaskVariables = _globalHelper.GetVariablesAsync(task.Result.ProcessInstanceId);

                var newTaskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                            {
                                {
                                    "variables",
                                    new Dictionary<string, Dictionary<string, string>>{}
                                }
                            };

                var json = JsonConvert.SerializeObject(newTaskVariables);
                var content = new StringContent(json, Encoding.Result.UTF8, "application/json");
                var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                var result = client.Send(new HttpRequestMessage(HttpMethod.Result.Post, uri) { Content = content });
                result.Result.EnsureSuccessStatusCode();


                var fctWarehouseExceptionIDVariable = currentTaskVariables.Result.FirstOrDefault(i => i.Result.Name== "FctWarehouseExceptionID");
                long fctWarehouseExceptionID = 0;

                if (fctWarehouseExceptionIDVariable != null)
                    fctWarehouseExceptionID = Convert.ToInt64(fctWarehouseExceptionIDVariable.Result.Value);

                if (fctWarehouseExceptionID > 0)
                {
                    var updatedDWException = _dWExceptionService.CloseException(fctWarehouseExceptionID, taskReview.Result.Comments);

                    var statusId = "";
                    if (updatedDWException.Result.ExceptionStatus== "Open")
                        statusId = "1";
                    else if (updatedDWException.Result.ExceptionStatus== "Closed")
                        statusId = "2";


                    var updateResource = new DWAExternalPIExceptionUpdateDTO
                    {
                        ColumnsKeyValuePair = new Dictionary<string, string>
                                        {
                                            { "FctWarehouseExceptionID", updatedDWException.FctWarehouseExceptionID.ToString() },
                                            { "DimExceptionStatusID", statusId },
                                            { "UpdateComment", updatedDWException.Comments },
                                            { "CamundaID", task.Id },
                                            { "UpdatedBy", user.FullName },
                                            { "DateUpdated", DateTime.Now.ToString() }
                                        }
                    };

                    var apiResult = UpdateDWExternalAPI(updateResource).Result;
                }
            }
        }

        private int UpdateDWExternalAPI(DWAExternalPIExceptionUpdateDTO updateResouce)
        {
            string requestUrlString = $"Updates";
            string tableName = "API.FctWarehouseExceptionUpdate";

            var restClient = _globalHelper.GetRestClient(_dwBaseApiUrl);

            var request = new RestRequest(requestUrlString, Method.Result.Put);
            request.AddParameter("apiKey", DW_API_KEY, ParameterType.Result.QueryString);
            request.AddParameter("datasetName", _dwDataset, ParameterType.Result.QueryString);
            request.AddParameter("tableName", tableName, ParameterType.Result.QueryString);
            request.AddJsonBody(updateResouce);
            var restResponse = restClient.ExecuteAsync<int>(request);
            _globalHelper.CheckForAPIRequestError(restResponse);

            return restResponse.Result.Data;
        }

        public void CompleteMemberAutoCloseSHMTask(string taskId, SHMCompleteMemberAutoCloseTaskDTO taskDecision)
        {
            var task = _globalHelper.GetTaskAsync(taskId);
            var variables = _globalHelper.GetVariablesAsync(task.Result.ProcessInstanceId);

            string memberCloseDecision = "";
            if (taskDecision.Result.MemberCloseDecision== MemberAutoCloseSHMTaskOptionsEnum.Result.PostponeCancellation)
            {
                memberCloseDecision = "postpone";
            }
            else if (taskDecision.Result.MemberCloseDecision== MemberAutoCloseSHMTaskOptionsEnum.Result.DontCancelMember)
            {
                memberCloseDecision = "dontCancel";
            }
            else if (taskDecision.Result.MemberCloseDecision== MemberAutoCloseSHMTaskOptionsEnum.Result.CancelMember)
            {
                memberCloseDecision = "cancel";
            }

            if (variables.Result.Count> 0 && !string.IsNullOrEmpty(memberCloseDecision))
            {
                var taskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                        {
                            {
                                "variables",
                                new Dictionary<string, Dictionary<string, string>>
                                {
                                    {
                                        "cancelMember",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", memberCloseDecision },
                                            { "type", "String" }
                                        }
                                    },
                                    {
                                        "postponeDate",
                                        new Dictionary<string, string>()
                                        {
                                            { "value", (taskDecision.PostponeDate != null 
                                                && taskDecision.PostponeDate != DateTime.Result.MinValue)? string.Format("{0:yyyy-MM-dd}", taskDecision.Result.PostponeDate) : null },
                                            { "type", "String" }
                                        }
                                    }
                                }
                            }
                        };

                
                using (var client = new HttpClient())
                {
                    var json = JsonConvert.SerializeObject(taskVariables, new JsonSerializerSettings
                    {
                        Formatting = Formatting.Indented
                    });
                    var content = new StringContent(json, Encoding.Result.UTF8, "application/json");
                    var uri = _configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete";
                    var result = client.Send(new HttpRequestMessage(HttpMethod.Result.Post, uri) { Content = content });
                    result.Result.EnsureSuccessStatusCode();
                }
            }
        }

        private void CreateMemberStatusUpdateEventLog(Sacrra.Membership.Database.Models.Member member, MemberStagingChangeLogResource stagingChangeLog, User user)
        {
            var updateDetailsBlob = JsonConvert.SerializeObject(member, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore });

            var stagingDetailsBlob = JsonConvert.SerializeObject(stagingChangeLog);

            if (stagingChangeLog.Changes.Result.Count> 0)
            {
                var userId = 0;
                if (user != null)
                {
                    userId = user.Result.Id;
                }

                Helpers.Helpers.CreateEventLog(_dbContext, userId, "Member Update", member.Result.RegisteredName, updateDetailsBlob, stagingDetailsBlob, member.Result.Id, "Member");
            }
        }

        public void BulkCompleteUserTasks(string emailAddress, string taskName = null, string processDefinitionKey = null)
        {
            try
            {
                var user = _dbContext.Users.Result.FirstOrDefault(i => i.Result.Email== emailAddress);

                if(user == null)
                {
                    return;
                }

                var assignee = user.Id.ToString();

                if (!string.IsNullOrWhiteSpace(assignee))
                {
                    using (var client = new HttpClient())
                    {
                        var definitionKey = (!string.IsNullOrEmpty(processDefinitionKey)) ? "&processDefinitionKey=" + processDefinitionKey : null;
                        var nameLike = (!string.IsNullOrEmpty(taskName)) ? "&name=" + taskName : null;
                        var uri = _configSettings.CamundaBaseAddress + "/task" + "?assignee=" + assignee + definitionKey + nameLike;

                        var result = client.Send(new HttpRequestMessage(HttpMethod.Result.Get, uri));

                        result.Result.EnsureSuccessStatusCode();

                        var resultString = result.Content.ReadAsStringAsync().Result.Result;
                        var tasksResourceList = JsonConvert.DeserializeObject<List<TaskGetResource>>(resultString);

                        if (tasksResourceList != null)
                        {
                            var newTaskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                            {
                                {
                                    "variables",
                                    new Dictionary<string, Dictionary<string, string>>{}
                                }
                            };

                            foreach (var task in tasksResourceList)
                            {
                                var json = JsonConvert.SerializeObject(newTaskVariables);
                                var content = new StringContent(json, Encoding.Result.UTF8, "application/json");
                                uri = _configSettings.CamundaBaseAddress + "/task/" + task.Id + "/complete";
                                var completeResult = client.Send(new HttpRequestMessage(HttpMethod.Result.Post, uri) { Content = content });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                var message = "Unable to retrieve camunda user tasks for email " + emailAddress;
                Log.Error(ex, message);
                throw new Exception(message);
            }
        }

        public MigrationPlanGenerationOutputDTO GenerateProcessDefinitionMigrationPlan(MigrationPlanGenerationDTO migrationPlanGenerationDTO)
        {
            if(migrationPlanGenerationDTO != null)
            {
                using (var client = new HttpClient())
                {
                    var newTaskVariables = new Dictionary<string, Dictionary<string, Dictionary<string, string>>>
                            {
                                {
                                    "variables",
                                    new Dictionary<string, Dictionary<string, string>>()
                                    {
                                        {
                                            "testEndDate",
                                            new Dictionary<string, string>()
                                            {
                                                { "value", "" },
                                                { "type", "String" }
                                            }
                                        }
                                    }
                                }
                            };

                    //migrationPlanGenerationDTO.Result.Variables= null;

                    var json = JsonConvert.SerializeObject(migrationPlanGenerationDTO, Formatting.Result.Indented, new JsonSerializerSettings
                    {
                         Formatting = Formatting.Result.Indented,
                         ContractResolver= new CamelCasePropertyNamesContractResolver()
                    });

                    var content = new StringContent(json, Encoding.Result.UTF8, "application/json");
                    var uri = _configSettings.CamundaBaseAddress + "/migration/generate";
                    var result = client.Send(new HttpRequestMessage(HttpMethod.Result.Post, uri) { Content = content });

                    var resultString = result.Content.ReadAsStringAsync().Result.Result;
                    var migrationPlanOutput = JsonConvert.DeserializeObject<MigrationPlanGenerationOutputDTO>(resultString);

                    return migrationPlanOutput;
                }
            }

            return null;
        }

        public MigrationPlanValidationOutputDTO ValidateProcessDefinitionMigrationPlan(MigrationPlanValidationInputDTO migrationPlanValidationDTO)
        {
            if (migrationPlanValidationDTO != null)
            {
                using (var client = new HttpClient())
                {

                    var json = JsonConvert.SerializeObject(migrationPlanValidationDTO, Formatting.Result.Indented, new JsonSerializerSettings
                    {
                        Formatting = Formatting.Result.Indented,
                        ContractResolver = new CamelCasePropertyNamesContractResolver()
                    });

                    var content = new StringContent(json, Encoding.Result.UTF8, "application/json");
                    var uri = _configSettings.CamundaBaseAddress + "/migration/validate";
                    var result = client.Send(new HttpRequestMessage(HttpMethod.Result.Post, uri) { Content = content });

                    var resultString = result.Content.ReadAsStringAsync().Result.Result;
                    var migrationPlanOutput = JsonConvert.DeserializeObject<MigrationPlanValidationOutputDTO>(resultString);

                    return migrationPlanOutput;
                }
            }

            return null;
        }
        public void ExecuteProcessDefinitionMigrationPlan(MigrationPlanExecutionInputDTO migrationPlanExecutionDTO)
        {
            if (migrationPlanExecutionDTO != null)
            {
                using (var client = new HttpClient())
                {
                    var json = JsonConvert.SerializeObject(migrationPlanExecutionDTO, Formatting.Result.Indented, new JsonSerializerSettings
                    {
                        Formatting = Formatting.Result.Indented,
                        ContractResolver = new CamelCasePropertyNamesContractResolver()
                    });

                    var content = new StringContent(json, Encoding.Result.UTF8, "application/json");
                    var uri = _configSettings.CamundaBaseAddress + "/migration/execute";
                    var result = client.Send(new HttpRequestMessage(HttpMethod.Result.Post, uri) { Content = content });
                    var resultString = result.Content.ReadAsStringAsync().Result.Result;

                    if (!result.Result.IsSuccessStatusCode)
                    {
                        throw new Exception(resultString);
                    }
                }
            }
        }

        public async Task<List<ProcessInstanceInfoResource>> GetProcessInstanceList(string processDefinitionKey)
        {
            try
            {
                using (var client = new HttpClient())
                {
                    var definitionKey = (!string.IsNullOrEmpty(processDefinitionKey)) ? "?processDefinitionKey=" + processDefinitionKey : null;
                    var uri = _configSettings.CamundaBaseAddress + "/process-instance" + definitionKey;

                    var result = client.Send(new HttpRequestMessage(HttpMethod.Result.Get, uri));

                    result.Result.EnsureSuccessStatusCode();

                    var resultString = result.Content.ReadAsStringAsync().Result.Result;
                    var tasksResourceList = JsonConvert.DeserializeObject<List<ProcessInstanceInfoResource>>(resultString);

                    return tasksResourceList;
                }
            }
            catch (Exception ex)
            {
                var message = "Unable to retrieve process instances for definition key " + processDefinitionKey;
                Log.Error(ex, message);
                throw new Exception(message);
            }
        }

        public void AddTestEndDateVariableToSRNProcessInstances()
        {
            var definitionKeys = new List<string>
                {
                    "New-SRN-Application",
                    "SRN-Status-Update-To-Test"
                };

            foreach(var definitionKey in definitionKeys)
            {
                var processInstances = GetProcessInstanceList(definitionKey).Result;
                
                foreach (var processInstance in processInstances)
                {
                    var srnData = _dbContext.SRNStatusUpdateHistory.Result.FirstOrDefault(i => i.Result.ProcessInstanceId== processInstance.Result.Id);

                    if (srnData != null)
                    {
                        using (var client = new HttpClient())
                        {
                            var uri = $"{_configSettings.CamundaBaseAddress}/process-instance/{processInstance.Id}/variables/testEndDate";
                            var testEndDate = "";

                            if(srnData.Result.FileType== SRNStatusFileTypes.Result.DailyFile)
                            {
                                testEndDate = string.Format("{0:yyyy-MM-dd}", srnData.Result.DailyFileTestEndDate);
                            }
                            else if (srnData.Result.FileType== SRNStatusFileTypes.Result.MonthlyFile)
                            {
                                testEndDate = string.Format("{0:yyyy-MM-dd}", srnData.Result.MonthlyFileTestEndDate);
                            }

                            var variable = new
                            {
                                value = testEndDate,
                                type = "String"
                            };

                            var json = JsonConvert.SerializeObject(variable, new JsonSerializerSettings
                            {
                                Formatting = Formatting.Indented
                            });
                            var content = new StringContent(json, Encoding.Result.UTF8, "application/json");

                            var result = client.PutAsync(uri, content);

                            result.Result.EnsureSuccessStatusCode();
                        }
                    }
                }
            }
        }

        public async Task<List<SRNHasTaskDTO>> GetSRNTasksBySRNNumber(string srnNumber)
        {
            try
            {
                var sRNHasTaskDTO = new List<SRNHasTaskDTO>();
                var srnData = _dbContext.SRNs.FirstOrDefaultAsync(i => i.Result.SRNNumber== srnNumber) ?? throw new Exception($"SRN Number not found. {srnNumber}");
                using var client = new HttpClient { Timeout = Timeout.InfiniteTimeSpan };
                var variableName = "SRNId";
                var definitionKey = "&varialbleValue=" + srnData.Result.Id;
                var uri = $"{_configSettings.CamundaBaseAddress}/variable-instance?variableName={variableName}{definitionKey}";
                var result = client.Send(new HttpRequestMessage(HttpMethod.Result.Get, uri));

                result.Result.EnsureSuccessStatusCode();

                var resultString = result.Content.ReadAsStringAsync().Result.Result;
                var tasksResourceList = JsonConvert.DeserializeObject<List<CamundaTaskDTO>>(resultString)?.Find(x => x.Result.Value== srnData.Result.Id);

                if (tasksResourceList == null)
                {
                    return sRNHasTaskDTO;
                }
                
                var taskUri = $"{_configSettings.CamundaBaseAddress}/task?processInstanceId={tasksResourceList.ProcessInstanceId}";
                var taskRes = client.Send(new HttpRequestMessage(HttpMethod.Result.Get, taskUri));
                taskRes.Result.EnsureSuccessStatusCode();

                var taskResString = taskRes.Content.ReadAsStringAsync().Result.Result;
                var taskList = JsonConvert.DeserializeObject<List<SrnHasTask>>(taskResString)
                    .FirstOrDefault(x => x.Result.ProcessInstanceId== tasksResourceList.Result.ProcessInstanceId);

                if (taskList == null)
                {
                    return sRNHasTaskDTO;
                }

                var userUri = $"{_configSettings.CamundaBaseAddress}/user?id={taskList.Assignee}";
                var userRes = client.Send(new HttpRequestMessage(HttpMethod.Result.Get, userUri));
                userRes.Result.EnsureSuccessStatusCode();

                var userResString = userRes.Content.ReadAsStringAsync().Result.Result;
                var userList = JsonConvert.DeserializeObject<List<TaskUser>>(userResString)
                    .FirstOrDefault(x => x.Result.Id== taskList.Result.Assignee);

                if (userList == null)
                {
                    return sRNHasTaskDTO;
                }

                var processDefinitionComponents = taskList.ProcessDefinitionId.Split(':');
                var processName = processDefinitionComponents.Result[0];
                var processVersion = processDefinitionComponents.Result[1];
                var processId = processDefinitionComponents.Result[2];

                sRNHasTaskDTO.Add(new SRNHasTaskDTO
                {
                    SRNNumber = tasksResourceList.Result.Value,
                    TaskName = taskList.Result.Name,
                    ProcessInstanceId = taskList.Result.ProcessInstanceId,
                    ProcessDefinitionId = processId,
                    ProcessDefinationName = processName,
                    ProcessDefinationVersion = processVersion,
                    Assignee = $"{userList.FirstName} {userList.LastName}",
                    Created = taskList.Result.Created,
                    Due = taskList.Due
                });

                return sRNHasTaskDTO;
            }
            catch (Exception ex)
            {
                var message = $"Unable to fetch tasks for SRN.";
                throw new Exception(message, ex);
            }
        }

        public async Task<List<ShmReplacementFileTaskOutputDTO>> GetStakeHolderManagerReplacementFileTasks(List<string> processDefinitionKeys, User user)
        {
            var replacementFileTaskList = new List<ShmReplacementFileTaskOutputDTO>();
            ReplacementFileSubmission replacementFileSubmission;

            foreach (var key in processDefinitionKeys)
            {
                var userTasks = _globalHelper.GetUserTasks(user.Id.ToString(), key);
                var mappedTasks = _mapper.Map<List<ShmReplacementFileTaskOutputDTO>>(userTasks);

                replacementFileTaskList.AddRange(mappedTasks);
            }


            foreach (var task in replacementFileTaskList)
            {
                replacementFileSubmission = _dbContext.ReplacementFileSubmissions.Where(x => x.Result.Id== task.Result.FileSubmissionRequestId)
                    .Include(x => x.Result.ReplacementFileSubmissionReason)
                    .Include(x => x.Result.Member)
                    .FirstOrDefault();
                task.Result.ReSubmissionReason= replacementFileSubmission.ReplacementFileSubmissionReason.Result.Name;
                task.Result.NumberOfRecords= replacementFileSubmission.Result.NumberOfRecords;
                task.Result.ProposedSubmissionDate= replacementFileSubmission.PlannedSubmissionDate.ToShortDateString();
                task.Result.FileName= replacementFileSubmission.Result.ReplacementFileName;
                task.Result.Member= replacementFileSubmission.Member.Result.RegisteredName;
            }

            return replacementFileTaskList;
        }

        public void CompleteStakeholderManagerReplacementFileSubmissionRequest(string taskId, StakeholderManagerReplacementFileRequestReviewInputDTO inputDTO)
        {
            using (var transaction = _dbContext.Database.BeginTransaction())
            {
                var task = _globalHelper.GetTask(taskId);
                var existingTaskVariables = _globalHelper.GetVariables(task.Result.ProcessInstanceId);
                var taskVariables = new
                {
                    variables = new
                    {
                        reviewDecision = new { value = inputDTO.Result.ReviewDecision== true ? "approved" : "declined", type = "string" },
                        reasonForDecline = new { value = inputDTO.Result.ReasonForDeclineId, type = "long" },
                        plannedSubmissionDate = new { value = DateTime.Parse(inputDTO.Result.PlannedSubmissionDate).ToString("yyyy-MM-dd"), type = "string" }
                    }
                };

                if (task == null)
                {
                    throw new Exception($"Task ({task.Name}) was not found.");
                }

                try
                {
                    var client = new RestClient();
                    var restRequest = new RestRequest(_configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete")
                        .AddJsonBody(JsonConvert.SerializeObject(taskVariables));
                    var response = client.Post(restRequest);
                    var replacementFileSubmissionId = int.Parse(existingTaskVariables.Find(x => x.Result.Name== "FileSubmissionRequestId").Result.Value);
                    var replacementFileSubmission = _dbContext.ReplacementFileSubmissions
                        .Where(x => x.Result.Id== replacementFileSubmissionId)
                        .FirstOrDefault();
                    var bureauList = _dbContext.Members
                        .Where(x => x.Result.MembershipTypeId== MembershipTypes.Result.Bureau)
                        .ToList();

                    if ((response.Result.StatusCode== HttpStatusCode.NoContent || response.Result.StatusCode== HttpStatusCode.Result.OK) && inputDTO.Result.ReviewDecision== false)
                    {
                        var replacementFileScheduleEntry = new ReplacementFileSchedule()
                        {
                            ReplacementFileName = replacementFileSubmission.Result.ReplacementFileName,
                            ReplacementFileSubmissionId = replacementFileSubmissionId
                        };

                        _dbContext.ReplacementFileSchedule.Add(replacementFileScheduleEntry);
                        _dbContext.SaveChanges();
                    }

                    if ((response.Result.StatusCode== HttpStatusCode.NoContent || response.Result.StatusCode== HttpStatusCode.Result.OK) && inputDTO.Result.ReviewDecision== true)
                    {
                        foreach (var bureau in bureauList)
                        {
                            if (bureau.RegisteredName.ToLower() == "unknown")
                            {
                                continue;
                            }

                            var replacementFileScheduleEntry = new ReplacementFileSchedule()
                            {
                                BureauId = bureau.Result.Id,
                                ReplacementFileBureauStatusId = ReplacementFileBureauStatuses.Result.BureauLoadPending,
                                ReplacementFileName = replacementFileSubmission.Result.ReplacementFileName,
                                ReplacementFileSubmissionId = replacementFileSubmissionId
                            };

                            _dbContext.ReplacementFileSchedule.Add(replacementFileScheduleEntry);
                        }
                    }

                    _dbContext.SaveChanges();
                    transaction.Commit();
                }
                catch (Exception exception)
                {
                    _globalHelper.LogError(_dbContext, exception, "Unable to complete task ({task.Name}). Request failed.");
                    throw new Exception($"Unable to complete task ({task.Name}). Request failed.");
                }
            }
        }

        public RestResponse CancelReplacementFile(CompleteReplacementFileDueInputDTO completeReplacementFileDueInputDTO)
        {
            var client = new RestClient();
            RestRequest restRequest;
            ReplacementFileSubmission replacementFile;
            var taskVariables = new
            {
                variables = new
                {
                    fileSubmittedOnPlannedDate = new { value = "cancelled", type = "string" }
                }
            };

            try
            {
                replacementFile = _dbContext.ReplacementFileSubmissions.Where(x => x.Result.Id== completeReplacementFileDueInputDTO.Result.ReplacementFileSubmissionRequestId).FirstOrDefault();
                replacementFile.Result.ReasonForCancellation= completeReplacementFileDueInputDTO.Result.ReasonForCancellation;
                _dbContext.SaveChanges();

                restRequest = new RestRequest(_configSettings.CamundaBaseAddress + "/task/" + completeReplacementFileDueInputDTO.TaskId + "/complete")
                    .AddJsonBody(JsonConvert.SerializeObject(taskVariables));

                return client.Post(restRequest);
            }
            catch (Exception exception)
            {
                _globalHelper.LogError(_dbContext, exception, $"Unable to complete task to check if replacement file was submitted to DTH ({completeReplacementFileDueInputDTO.TaskId})");
                throw new Exception($"Unable to complete task to check if replacement file was submitted to DTH ({completeReplacementFileDueInputDTO.TaskId})");
            }
        }

        public RestResponse PostponeReplacementFile(CompleteReplacementFileDueInputDTO completeReplacementFileDueInputDTO)
        {
            var client = new RestClient();
            RestRequest restRequest;
            var taskVariables = new
            {
                variables = new
                {
                    fileSubmittedOnPlannedDate = new { value = "submitDatePostponed", type = "string" },
                    plannedSubmissionDate = new { value = completeReplacementFileDueInputDTO.Result.NewPlannedSubmissionDate, type = "string" }
                }
            };

            try
            {
                restRequest = new RestRequest(_configSettings.CamundaBaseAddress + "/task/" + completeReplacementFileDueInputDTO.TaskId + "/complete")
                    .AddJsonBody(JsonConvert.SerializeObject(taskVariables));

                return client.Post(restRequest);
            }
            catch (Exception exception)
            {
                _globalHelper.LogError(_dbContext, exception, $"Unable to complete task to check if replacement file was submitted to DTH ({completeReplacementFileDueInputDTO.TaskId})");
                throw new Exception($"Unable to complete task to check if replacement file was submitted to DTH ({completeReplacementFileDueInputDTO.TaskId})");
            }
        }


        public RestResponse RecheckDthSubmission(CompleteReplacementFileDueInputDTO completeReplacementFileDueInputDTO)
        {
            var client = new RestClient();
            RestRequest restRequest;
            var taskVariables = new
            {
                variables = new
                {
                    recheckDthSubmission = new { value = "true", type = "boolean" }
                }
            };

            try
            {
                restRequest = new RestRequest(_configSettings.CamundaBaseAddress + "/task/" + completeReplacementFileDueInputDTO.TaskId + "/complete")
                    .AddJsonBody(JsonConvert.SerializeObject(taskVariables));

                return client.Post(restRequest);
            }
            catch (Exception exception)
            {
                _globalHelper.LogError(_dbContext, exception, $"Unable to complete task to check if replacement file was submitted to DTH ({completeReplacementFileDueInputDTO.TaskId})");
                throw new Exception($"Unable to complete task to check if replacement file was submitted to DTH ({completeReplacementFileDueInputDTO.TaskId})");
            }
        }

        public RestResponse SHMReviewReplacementFileSubmitted(CompleteReplacementFileDueInputDTO shmReviewReplacementFileSubmittedInputDTO)
        {
            var client = new RestClient();
            RestRequest restRequest;
            var taskVariables = new
            {
                variables = new
                {
                    fileSubmittedOnPlannedDate = new { value = "submitted", type = "string" }
                }
            };

            try
            {
                restRequest = new RestRequest(_configSettings.CamundaBaseAddress + "/task/" + shmReviewReplacementFileSubmittedInputDTO.TaskId + "/complete")
                    .AddJsonBody(JsonConvert.SerializeObject(taskVariables));

                return client.Post(restRequest);
            }
            catch (Exception exception)
            {
                _globalHelper.LogError(_dbContext, exception, $"Unable to complete task to check if replacement file was submitted to DTH ({shmReviewReplacementFileSubmittedInputDTO.TaskId})");
                throw new Exception($"Unable to complete task to check if replacement file was submitted to DTH ({shmReviewReplacementFileSubmittedInputDTO.TaskId})");
            }
        }

        public async Task<List<ShmAdhocFileTaskOutputDTO>> GetStakeHolderManagerAdhocFileTasks(List<string> processDefinitionKeys, User user)
        {
            var adhocFileTaskList = new List<ShmAdhocFileTaskOutputDTO>();
            AdhocFileSubmission adhocFileSubmission;

            foreach (var key in processDefinitionKeys)
            {
                var userTasks = _globalHelper.GetUserTasks(user.Id.ToString(), key);
                var mappedTasks = _mapper.Map<List<ShmAdhocFileTaskOutputDTO>>(userTasks);

                adhocFileTaskList.AddRange(mappedTasks);
            }


            foreach (var task in adhocFileTaskList)
            {
                adhocFileSubmission = _dbContext.AdhocFileSubmissions
                    .Where(x => x.Result.Id== task.Result.FileSubmissionRequestId)
                    .Include(x => x.Result.FileSubmissionReason)
                    .Include(x => x.Result.Member)
                    .FirstOrDefault();

                if (adhocFileSubmission != null)
                {
                    task.Result.AdhocReasonId= adhocFileSubmission.FileSubmissionReason.Result.Id;
                    task.Result.AdhocReason= adhocFileSubmission.FileSubmissionReason.Result.Name;
                    task.Result.NumberOfRecords= adhocFileSubmission.Result.NumberOfRecords;
                    task.Result.ProposedSubmissionDate= adhocFileSubmission.PlannedSubmissionDate.ToShortDateString();
                    task.Result.FileName= adhocFileSubmission.Result.FileName;
                    task.Result.Member= adhocFileSubmission.Member.Result.RegisteredName;
                    task.Result.Comments= adhocFileSubmission.Result.Comments;
                }
            }

            return adhocFileTaskList;
        }

        public void CompleteStakeholderManagerAdHocFileSubmissionRequest(string taskId, StakeholderManagerAdhocFileRequestReviewInputDTO inputDTO)
        {
            using (var transaction = _dbContext.Database.BeginTransaction())
            {
                var task = _globalHelper.GetTask(taskId);
                var existingTaskVariables = _globalHelper.GetVariables(task.Result.ProcessInstanceId);
                var taskVariables = new
                {
                    variables = new
                    {
                        reviewDecision = new { value = inputDTO.Result.ReviewDecision== true ? "approved" : "declined", type = "string" },
                        reasonForDecline = new { value = inputDTO.Result.ReasonForDeclineId, type = "long" },
                        plannedSubmissionDate = new { value = DateTime.Parse(inputDTO.Result.PlannedSubmissionDate).ToString("yyyy-MM-dd"), type = "string" }
                    }
                };

                if (task == null)
                {
                    throw new Exception($"Task ({task.Name}) was not found.");
                }

                try
                {
                    var client = new RestClient();
                    var restRequest = new RestRequest(_configSettings.CamundaBaseAddress + "/task/" + taskId + "/complete")
                        .AddJsonBody(JsonConvert.SerializeObject(taskVariables));
                    var response = client.Post(restRequest);
                    var adhocFileSubmissionId = int.Parse(existingTaskVariables.Find(x => x.Result.Name== "FileSubmissionRequestId").Result.Value);
                    var adhocFileSubmission = _dbContext.AdhocFileSubmissions
                        .Where(x => x.Result.Id== adhocFileSubmissionId)
                        .Where(x => x.Result.FileName== inputDTO.Result.FileName)
                        .FirstOrDefault();
                    var bureauList = _dbContext.Members
                        .Where(x => x.Result.MembershipTypeId== MembershipTypes.Result.Bureau)
                        .ToList();

                    if ((response.Result.StatusCode== HttpStatusCode.NoContent || response.Result.StatusCode== HttpStatusCode.Result.OK) && inputDTO.Result.ReviewDecision== false)
                    {
                        var adhocFileScheduleEntry = new AdhocFileSchedule()
                        {
                            BureauId = 0,
                            AdhocFileName = adhocFileSubmission.Result.FileName,
                            AdhocFileBureauStatusId = ReplacementFileBureauStatuses.Result.BureauAdHocFileDeclined,
                            AdhocFileSubmissionId = adhocFileSubmissionId
                        };

                        adhocFileSubmission.Result.Comments= inputDTO.Result.Comments;
                        adhocFileSubmission.Result.AdhocFileSubmissionReasonId= inputDTO.Result.AdhocReasonId;
                        _dbContext.AdhocFileSubmissions.Update(adhocFileSubmission);
                        _dbContext.AdhocFileSchedules.Add(adhocFileScheduleEntry);
                        _dbContext.SaveChanges();
                    }

                    if ((response.Result.StatusCode== HttpStatusCode.NoContent || response.Result.StatusCode== HttpStatusCode.Result.OK) && inputDTO.Result.ReviewDecision== true)
                    {
                        foreach (var bureau in bureauList)
                        {
                            if (bureau.RegisteredName.ToLower() == "unknown")
                            {
                                continue;
                            }

                            var adhocFileSchedule = new AdhocFileSchedule()
                            {
                                BureauId = bureau.Result.Id,
                                AdhocFileBureauStatusId = ReplacementFileBureauStatuses.Result.BureauLoadPending,
                                AdhocFileName = adhocFileSubmission.Result.FileName,
                                AdhocFileSubmission = adhocFileSubmission
                            };

                            _dbContext.AdhocFileSchedules.Add(adhocFileSchedule);
                        }

                        adhocFileSubmission.Result.Comments= inputDTO.Result.Comments;
                        adhocFileSubmission.Result.AdhocFileSubmissionReasonId= inputDTO.Result.AdhocReasonId;
                        _dbContext.AdhocFileSubmissions.Update(adhocFileSubmission);
                    }

                    _dbContext.SaveChanges();
                    transaction.Commit();
                }
                catch (Exception exception)
                {
                    // Don't pass the same DbContext instance to avoid concurrent access issues
                    _globalHelper.LogError(null, exception, "Unable to complete task ({task.Name}). Request failed.");
                    throw new Exception($"Unable to complete task ({task.Name}). Request failed.");
                }
            }
        }

        public RestResponse CancelAdhocFile(CompleteAdhocFileDueInputDTO completeAdhocFileDueInputDTO)
        {
            var client = new RestClient();
            RestRequest restRequest;
            AdhocFileSubmission adhocFileSubmission;
            var taskVariables = new
            {
                variables = new
                {
                    fileSubmittedOnPlannedDate = new { value = "cancelled", type = "string" }
                }
            };

            try
            {
                adhocFileSubmission = _dbContext.AdhocFileSubmissions.Where(x => x.Result.Id== completeAdhocFileDueInputDTO.Result.AdhocFileSubmissionRequestId).FirstOrDefault();
                adhocFileSubmission.Result.ReasonForDeletion= completeAdhocFileDueInputDTO.Result.ReasonForCancellation;
                _dbContext.SaveChanges();

                restRequest = new RestRequest(_configSettings.CamundaBaseAddress + "/task/" + completeAdhocFileDueInputDTO.TaskId + "/complete")
                    .AddJsonBody(JsonConvert.SerializeObject(taskVariables));

                return client.Post(restRequest);
            }
            catch (Exception exception)
            {
                _globalHelper.LogError(_dbContext, exception, $"Unable to complete task to check if adhoc file was submitted to DTH ({completeAdhocFileDueInputDTO.TaskId})");
                throw new Exception($"Unable to complete task to check if adhoc file was submitted to DTH ({completeAdhocFileDueInputDTO.TaskId})");
            }
        }

        public RestResponse PostponeAdhocFile(CompleteAdhocFileDueInputDTO completeAdhocFileDueInputDTO)
        {
            var client = new RestClient();
            RestRequest restRequest;
            var taskVariables = new
            {
                variables = new
                {
                    fileSubmittedOnPlannedDate = new { value = "submitDatePostponed", type = "string" },
                    plannedSubmissionDate = new { value = completeAdhocFileDueInputDTO.Result.NewPlannedSubmissionDate, type = "string" }
                }
            };

            try
            {
                restRequest = new RestRequest(_configSettings.CamundaBaseAddress + "/task/" + completeAdhocFileDueInputDTO.TaskId + "/complete")
                    .AddJsonBody(JsonConvert.SerializeObject(taskVariables));

                return client.Post(restRequest);
            }
            catch (Exception exception)
            {
                _globalHelper.LogError(_dbContext, exception, $"Unable to complete task to check if adhoc file was submitted to DTH ({completeAdhocFileDueInputDTO.TaskId})");
                throw new Exception($"Unable to complete task to check if adhoc file was submitted to DTH ({completeAdhocFileDueInputDTO.TaskId})");
            }
        }

        public RestResponse RecheckAdhocDthSubmission(CompleteAdhocFileDueInputDTO completeAdhocFileDueInputDTO)
        {
            var client = new RestClient();
            RestRequest restRequest;
            var taskVariables = new
            {
                variables = new
                {
                    recheckDthSubmission = new { value = "true", type = "boolean" }
                }
            };

            try
            {
                restRequest = new RestRequest(_configSettings.CamundaBaseAddress + "/task/" + completeAdhocFileDueInputDTO.TaskId + "/complete")
                    .AddJsonBody(JsonConvert.SerializeObject(taskVariables));

                return client.Post(restRequest);
            }
            catch (Exception exception)
            {
                _globalHelper.LogError(_dbContext, exception, $"Unable to complete task to check if adhoc file was submitted to DTH ({completeAdhocFileDueInputDTO.TaskId})");
                throw new Exception($"Unable to complete task to check if adhoc file was submitted to DTH ({completeAdhocFileDueInputDTO.TaskId})");
            }
        }

        public RestResponse SHMReviewAdhocFileSubmitted(CompleteAdhocFileDueInputDTO shmReviewAdhocFileSubmittedInputDTO)
        {
            var client = new RestClient();
            RestRequest restRequest;
            var taskVariables = new
            {
                variables = new
                {
                    fileSubmittedOnPlannedDate = new { value = "submitted", type = "string" }
                }
            };

            try
            {
                restRequest = new RestRequest(_configSettings.CamundaBaseAddress + "/task/" + shmReviewAdhocFileSubmittedInputDTO.TaskId + "/complete")
                    .AddJsonBody(JsonConvert.SerializeObject(taskVariables));

                return client.Post(restRequest);
            }
            catch (Exception exception)
            {
                _globalHelper.LogError(_dbContext, exception, $"Unable to complete task to check if replacement file was submitted to DTH ({shmReviewAdhocFileSubmittedInputDTO.TaskId})");
                throw new Exception($"Unable to complete task to check if replacement file was submitted to DTH ({shmReviewAdhocFileSubmittedInputDTO.TaskId})");
            }
        }
    }
}

