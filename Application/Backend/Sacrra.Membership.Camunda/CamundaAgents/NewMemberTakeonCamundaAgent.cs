using System.Collections.Generic;
using System.Threading.Tasks;
using Camunda.Api.Client;
using Camunda.Api.Client.ExternalTask;
using Microsoft.Extensions.DependencyInjection;
using Sacrra.Membership.Business.Services;
using Sacrra.Membership.Database.Enums;

namespace Sacrra.Membership.Camunda.CamundaAgents;

public class NewMemberTakeonCamundaAgent: BaseCamundaAgent
{
    public NewMemberTakeonCamundaAgent(CamundaClient camundaClient) : base(camundaClient) { }

    public override async Task Process(string topicName, ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        switch (topicName)
        {
            case "disqualify-member-and-email":
                DisqualifyMemberAndEmail(task, completeExternalTask, serviceScope);
                break;
            
            case "allocate-stake-holder-administrator":
                AllocateStakeHolderAdministrator(task, completeExternalTask, serviceScope);
                break;
            
            case "member-approve":
                Member<PERSON><PERSON>rove(task, completeExternalTask, serviceScope);
                break;
            
            case "notify-member-of-application-approval":
                NotifyMemberOfApplicationApproval(task, completeExternalTask, serviceScope);
                break;
            
            case "new-membership-update-member-status":
                NewMembershipUpdateMemberStatus(task, completeExternalTask, serviceScope);
                break;
            
            case "notify-ALG-leader-on-client-takeon":
                NotifyALGLeaderOnClientTakeon(task, completeExternalTask, serviceScope);
                break;
            
            case "send-member-assessment-payment-reminder":
            case "send-member-application-cancellation-email":
            case "send-payment-and-constitution-reminder":
            case "send-member-onboarding-payment-reminder":
                break;
        }
            
        task.Complete(completeExternalTask);
    }

    private void NotifyALGLeaderOnClientTakeon(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var memberId = GetMemberVariable(task, "OrganisationID", serviceScope);

        GetNewMemberTakeonCamundaService(serviceScope).NotifyALGLeaderOnClientRegistration(memberId);
    }

    private void NewMembershipUpdateMemberStatus(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var memberId = GetMemberVariable(task, "OrganisationID", serviceScope);

        GetNewMemberTakeonCamundaService(serviceScope).UpdateMemberStatus(ApplicationStatuses.MemberRegistrationCompleted, memberId);
    }

    private void NotifyMemberOfApplicationApproval(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var memberId = GetMemberVariable(task, "OrganisationID", serviceScope);

        GetNewMemberTakeonCamundaService(serviceScope).NotifyMemberOfApplicationApproval(memberId);
    }

    private void MemberApprove(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var memberId = GetMemberVariable(task, "OrganisationID", serviceScope);

        GetNewMemberTakeonCamundaService(serviceScope).UpdateMemberStatus(ApplicationStatuses.MemberRegistrationApproved, memberId);
        GetNewMemberTakeonCamundaService(serviceScope).NotifyMemberOfApplicationApproval(memberId);
    }

    private void AllocateStakeHolderAdministrator(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var randomUser = GetRandomUser("StakeHolderAdministrator");

        completeExternalTask.Variables = new Dictionary<string, VariableValue>
        {
            ["stakeHolderManagerManagerAssignee"] = VariableValue.FromObject(randomUser.Id)
        };
    }

    private void DisqualifyMemberAndEmail(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var memberId = GetMemberVariable(task, "OrganisationID", serviceScope);

        GetNewMemberTakeonCamundaService(serviceScope).NotifyMemberOfApplicationDisqualification(memberId);
    }
    
    private static NewMemberTakeonCamundaService GetNewMemberTakeonCamundaService(IServiceScope serviceScope)
    {
        return serviceScope.ServiceProvider.GetRequiredService<NewMemberTakeonCamundaService>();
    }
}
