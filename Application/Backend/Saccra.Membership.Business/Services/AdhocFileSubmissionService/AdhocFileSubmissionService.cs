using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using RestSharp;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.Services.DataWarehouseService;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;
using Sacrra.Membership.Notification.Repositories;
using Sacrra.Membership.Reporting.Helper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Sacrra.Membership.Business.DTOs.AdHocFIlesDTO;
using Sacrra.Membership.Business.DTOs.AdhocFileSubmissionDTO;
using Sacrra.Membership.Business.DTOs.AdhocFileSubmissionDTOs;
using Sacrra.Membership.Business.DTOs.ReplacementFileSubmissionDTOs;
using Sacrra.Membership.Business.Resources.MemberChangeRequest;

namespace Sacrra.Membership.Business.Services.AdhocFilesService
{
    public class AdhocFileSubmissionService
    {
        private AppDbContext _dbContext;
        private readonly ConfigSettings _configSettings;
        private readonly EmailService _emailService;
        private DataWarehouseService.DataWarehouseService _dataWarehouseService;
        private ReportTables _reportTables;
        public IMapper _mapper { get; }

        public AdhocFileSubmissionService(AppDbContext dbContext, IOptions<ConfigSettings> configSettings, IMapper mapper, EmailService emailService, DataWarehouseService.DataWarehouseService dataWarehouseService)
        {
            _dbContext = dbContext;
            _configSettings = configSettings.Value;
            _emailService = emailService;
            _dataWarehouseService = dataWarehouseService;
            _reportTables = _dataWarehouseService.ReportTables;
            _mapper = mapper;
        }

        public RestResponse RequestAdhocFileSubmission(AdhocFileSubmissionInputDTO inputDTO)
        {
            SRN adhocFileSRN;
            Member adhocFileMember;
            var restClient = new RestClient();
            object variables;
            AdhocFileSubmission adhocFileSubmission;
            DateTime currentDate = DateTime.Now;

            adhocFileSRN = _dbContext.SRNs
                .Where(x => x.SRNNumber == inputDTO.SRNNumber)
                .FirstOrDefault();

            if (adhocFileSRN != null)
            {
                adhocFileMember = _dbContext.Members
                    .Where(x => x.Id == adhocFileSRN.MemberId)
                    .Include(x => x.StakeholderManager)
                    .FirstOrDefault();
            }
            else
            {
                throw new Exception($"SRN ({inputDTO.SRNNumber}) was not found.");
            }

            adhocFileSubmission = new AdhocFileSubmission
            {
                SRNId = adhocFileSRN.Id,
                MemberId = adhocFileMember.Id,
                FileName = inputDTO.FileName,
                NumberOfRecords = inputDTO.NumberOfRecords,
                AdhocFileSubmissionStatusId = (int)ReplacementFileSubmissionStatuses.Requested,
                SubmissionStatusDate = DateTime.Now,
                AdhocFileSubmissionReasonId = inputDTO.ReasonForAdhocFileId,
                SACRRAIndustry = EnumHelper.GetEnumIdValuePair<IndustryClassifications>((int)adhocFileMember.IndustryClassificationId)?.Value,
                SACRRAAccountType = _dbContext.AccountTypes.Where(x => x.Id == adhocFileSRN.AccountTypeId).FirstOrDefault()?.Name,
                IsDeleted = false,
                CreatedAt = currentDate,
                LastUpdatedAt = currentDate,
                PlannedSubmissionDate = DateTime.Parse(inputDTO.PlannedSubmissionDate),
                ActualSubmissionDate = null,
                Comments = inputDTO.Comments
            };

            try
            {
                _dbContext.AdhocFileSubmissions.Add(adhocFileSubmission);
                var user = Helpers.Helpers.GetUserByAuth0Id(_dbContext);
                //Build eventlog var
                var updateDetailsBlob = JsonConvert.SerializeObject(adhocFileSubmission);
                var stagingChangeLog = new StagingChange
                {
                    Name = "Adhoc Submission",
                    OldValue = "",
                    NewValue = inputDTO.FileName
                };
                var stagingDetailsBlob = JsonConvert.SerializeObject(stagingChangeLog);
                //Update EventLog
                await Helpers.Helpers
                       .CreateEventLog(_dbContext, user.Id, "Adhoc Submission", inputDTO.FileName, updateDetailsBlob, stagingDetailsBlob, adhocFileMember.Id, "Adhoc");

                _dbContext.SaveChanges();
            }
            catch (Exception exception)
            {
                throw new Exception("Unable to save ad-hoc file request to DB.", exception);
            }

            variables = new
            {
                variables = new
                {
                    SRNId = new
                    {
                        value = adhocFileSRN.Id,
                        type = "long"
                    },
                    SHMId = new
                    {
                        value = adhocFileMember.StakeholderManagerId,
                        type = "long"
                    },
                    FileSubmissionRequestId = new
                    {
                        value = adhocFileSubmission.Id,
                        type = "long"
                    },
                    AdHocFileSubmissionFileName = new
                    {
                        value = inputDTO.FileName,
                        type = "string"
                    }
                }
            };

            try
            {
                var response = restClient.Post(new RestRequest(_configSettings.CamundaBaseAddress + "/process-definition/key/Ad-Hoc-File-Submissions/start")
                    .AddJsonBody(JsonConvert.SerializeObject(variables)));

                return response;
            }
            catch (Exception exception)
            {
                adhocFileSubmission.IsDeleted = true;
                adhocFileSubmission.ReasonForDeletion = "Camunda task creation failed.";

                _dbContext.AdhocFileSubmissions.Update(adhocFileSubmission);
                _dbContext.SaveChanges();

                throw new Exception("Camunda task creation failed.", exception);
            }
        }

        public List<AdhocFileSubmissionOutputDTO> GetApprovedAdhocFiles()
        {
            var approvedAdhocFileRequestList = new List<AdhocFileSubmissionOutputDTO>();
            var approvedAdhocFileRequests = _dbContext.AdhocFileSubmissions
                .Where(x => x.AdhocFileSubmissionStatusId == (int)ReplacementFileSubmissionStatuses.Submitted)
                .Include(x => x.SRN)
                .Include(x => x.Member)
                .ThenInclude(x => x.StakeholderManager)
                .Include(x => x.FileSubmissionReason)
                .ToList();
            approvedAdhocFileRequests.ForEach(approvedAdhocFileRequest =>
            {
                var dto = new AdhocFileSubmissionOutputDTO
                {
                    Id = approvedAdhocFileRequest.Id,
                    FileSubmissionReasonConstId = approvedAdhocFileRequest.FileSubmissionReason.FileSubmissionReasonConstId,
                    FileSubmissionReasonId = approvedAdhocFileRequest.AdhocFileSubmissionReasonId,
                    NumberOfRecords = approvedAdhocFileRequest.NumberOfRecords,
                    SRNNumber = approvedAdhocFileRequest.SRN == null ? "N/A" : approvedAdhocFileRequest.SRN.SRNNumber,
                    MemberName = approvedAdhocFileRequest.Member.RegisteredName,
                    FileSubmissionReason = _dbContext.AdhocFileSubmissionReason.Where(reason => reason.Id == approvedAdhocFileRequest.AdhocFileSubmissionReasonId).FirstOrDefault().Name,
                    SRNDisplayName = approvedAdhocFileRequest.SRN == null ? "N/A" : approvedAdhocFileRequest.SRN.TradingName,
                    StakeHolderManager = approvedAdhocFileRequest.Member.StakeholderManager.FullName,
                    PlannedSubmissionDate = approvedAdhocFileRequest.PlannedSubmissionDate.ToString("yyyy-MM-dd"),
                    AdhocFileName = approvedAdhocFileRequest.FileName,
                    SPNumber = approvedAdhocFileRequest.SRN == null ? _dbContext.SPGroups.Where(x => x.Id == approvedAdhocFileRequest.SPId).FirstOrDefault().SPNumber : "N/A",
                    ActualSubmissionDate = approvedAdhocFileRequest.ActualSubmissionDate == null ? "N/A" : approvedAdhocFileRequest.ActualSubmissionDate?.ToString("yyyy-MM-dd")
                };
                approvedAdhocFileRequestList.Add(dto);
            });
            return approvedAdhocFileRequestList;
        }

        //public List<AdhocFileSubmissionOutputDTO> GetAdhocFileNames()
        //{
        //    string fileName = "XY2089_ALL_L702_A_20241104_1_1.TXT.PGP";                
        //    var approvedAdhocFileRequestList = new List<AdhocFileSubmissionOutputDTO>();
        //    var approvedAdhocFileRequests = _dbContext.AdhocFileSubmissions
        //        .Where(x => x.FileName == fileName)
        //            .ToList();                 
        //    approvedAdhocFileRequests.ForEach(approvedAdhocFileRequest =>
        //    {
        //        var dto = new AdhocFileSubmissionOutputDTO
        //        {
        //            Id = approvedAdhocFileRequest.Id,                    
        //            AdhocFileName = approvedAdhocFileRequest.FileName                    
        //        };
        //        approvedAdhocFileRequestList.Add(dto);
        //    });
        //    return approvedAdhocFileRequestList;
        //}

        public void SubmitUnsuccessfulAdhocLoad(BureauUnsuccessfulLoadInputDTO inputDTO, ClaimsPrincipal user)
        {
            try
            {
                var auth0User = Helpers.Helpers.GetLoggedOnUser(_dbContext, user);
                var currentUser = _dbContext.Users
                    .Where(x => x.Auth0Id == auth0User.Auth0Id)
                    .Include(x => x.Members)
                    .FirstOrDefault();
                var bureauUnsuccessfulLoadModel = _dbContext.AdhocFileSchedule
                    .Where(x => x.AdhocFileSubmissionId == inputDTO.AdhocFileSubmissionId && x.BureauId == currentUser.Members.First().MemberId)
                    .FirstOrDefault();
                var adhocFileSubmission = _dbContext.AdhocFileSubmissions
                    .Where(x => x.FileName == inputDTO.AdHocFileName)
                    .Include(x => x.SRN)
                    .FirstOrDefault();
                var srn = _dbContext.SRNs
                    .Where(x => x.Id == adhocFileSubmission.SRNId)
                    .Include(x => x.Contacts)
                    .FirstOrDefault();
                var member = _dbContext.Members
                    .Where(x => x.Id == srn.MemberId)
                    .Include(x => x.Contacts)
                    .FirstOrDefault();

                bureauUnsuccessfulLoadModel.UnnsuccessfulLoadReasonId = inputDTO.UnsuccessfullLoadReasonId;
                bureauUnsuccessfulLoadModel.AdhocFileBureauStatusId = ReplacementFileBureauStatuses.BureauLoadUnsuccessful;

                _dbContext.AdhocFileSchedule.Update(bureauUnsuccessfulLoadModel);
                _dbContext.SaveChanges();

                var placeholders = new List<KeyValuePair<string, string>>
                    {
                        new KeyValuePair<string, string>("[Bureau]", member.RegisteredName),
                        new KeyValuePair<string, string>("[FileName]", inputDTO.AdHocFileName)
                    };
                if (srn == null)
                {
                    var dataContact = member.Contacts.FirstOrDefault(x => x.ContactTypeId == 1);
                    _emailService.SendEmail(dataContact.Email, dataContact.FirstName, "Bureau Unsuccessful Load", "BureauUnsuccessfulLoad.html", placeholders, null, "", "", srn.Id, WorkflowEnum.NotApplicable, EmailReasonEnum.BureauUnsuccessfulLoad, EmailRecipientTypeEnum.Member);
                }
            }
            catch (Exception ex)
            {
                throw new Exception("Unable to create unsuccessful load.");
            }
        }

        public void SubmitAdhocLoadStats(BureauLoadStatsInputDTO inputDTO, ClaimsPrincipal User)
        {
            try
            {
                var auth0User = Helpers.Helpers.GetLoggedOnUser(_dbContext, User);
                var currentUser = _dbContext.Users
                    .Where(x => x.Auth0Id == auth0User.Auth0Id)
                    .Include(x => x.Members)
                    .FirstOrDefault();
                var bureauLoadStats = _mapper.Map<BureauLoadStats>(inputDTO);



                var schedule = _dbContext.AdhocFileSchedule.Where(x => x.BureauId == currentUser.Members.FirstOrDefault().MemberId && x.AdhocFileSubmissionId == inputDTO.AdHocFileSubmissionId).FirstOrDefault();
                schedule.AdhocFileBureauStatusId = ReplacementFileBureauStatuses.BureauLoadSuccessful;

                bureauLoadStats.MemberId = currentUser.Members.FirstOrDefault().MemberId;

                _dbContext.BureauLoadStats.Add(bureauLoadStats);
                _dbContext.SaveChanges();
            }
            catch
            {
                throw;
            }
        }

        public bool DoesAdHocSubmissionHaveLoadStats(int adhocFileSubmissionId)
        {
            try
            {
                var adhocFileSubmission = _dbContext.AdhocFileSubmissions
                    .Where(x => x.Id == adhocFileSubmissionId)
                    .FirstOrDefault();

                if (adhocFileSubmission != null)
                {
                    return true;
                }

                return false;
            }
            catch
            {
                throw;
            }
        }

        public BureauLoadStatsOutputDTO GetBureauLoadStats(int adhocFileSubmissionId, ClaimsPrincipal User, string? bureauName = null)
        {
            try
            {
                BureauLoadStats bureauLoadStats;
                if (bureauName != null && !bureauName.Equals("undefined"))
                {
                    bureauLoadStats = _dbContext.BureauLoadStats
                      .Include(x => x.Member)
                      .Where(x => x.AdHocFileSubmissionId == adhocFileSubmissionId && x.Member.RegisteredName.Contains(bureauName.ToLower()))
                      .FirstOrDefault();
                }
                else
                {
                    var currentUser = _dbContext.Users
                    .Where(x => x.Auth0Id == User.Identity.Name)
                    .Include(x => x.Members)
                    .FirstOrDefault();

                    bureauLoadStats = _dbContext.BureauLoadStats
                        .Include(x => x.Member)
                        .Where(x => x.AdHocFileSubmissionId == adhocFileSubmissionId && x.MemberId == currentUser.Members.FirstOrDefault().MemberId)
                        .FirstOrDefault();
                }

                var outputDTO = new BureauLoadStatsOutputDTO();

                if (bureauLoadStats != null)
                {
                    outputDTO.AdHocFileSubmission = bureauLoadStats.AdHocFileSubmission;
                    outputDTO.AdHocFileSubmissionId = bureauLoadStats.AdHocFileSubmissionId;
                    outputDTO.DateNewQE1ExtractSharedPostCleanup = bureauLoadStats.DateNewQE1ExtractSharedPostCleanup?.ToShortDateString();
                    outputDTO.Id = bureauLoadStats.Id;
                    outputDTO.NumberOfDuplicatesRemovedFromDBBasedOnExtract = bureauLoadStats.NumberOfDuplicatesRemovedFromDBBasedOnExtract;
                    outputDTO.NumberOfRecordsMatched = bureauLoadStats.NumberOfRecordsMatched;
                    outputDTO.NumberOfRecordsMatchedAndUpdated = bureauLoadStats.NumberOfRecordsMatchedAndUpdated;
                    outputDTO.NumberOfRecordsMatchedButNotUpdated = bureauLoadStats.NumberOfRecordsMatchedButNotUpdated;
                    outputDTO.NumberOfRecordsMatchedSuccessfullyConverted = bureauLoadStats.NumberOfRecordsMatchedSuccessfullyConverted;
                    outputDTO.NumberOfRecordsMergedAcrossSRNs = bureauLoadStats.NumberOfRecordsMergedAcrossSRNs;
                    outputDTO.NumberOfRecordsMergedWithinSRN = bureauLoadStats.NumberOfRecordsMergedWithinSRN;
                    outputDTO.NumberOfRecordsMigrated = bureauLoadStats.NumberOfRecordsMigrated;
                    outputDTO.NumberOfRecordsReceived = bureauLoadStats.NumberOfRecordsReceived;
                    outputDTO.NumberOfRecordsUnmatched = bureauLoadStats.NumberOfRecordsUnmatched;
                    outputDTO.TotalNumberOfQE1RecordRemainingOnDBPostCleanup = bureauLoadStats.TotalNumberOfQE1RecordRemainingOnDBPostCleanup;

                    return outputDTO;
                }

                return null;
            }
            catch
            {
                throw;
            }
        }

        public void UpdateAdhocLoadStats(BureauLoadStatsInputDTO inputDTO, ClaimsPrincipal user)
        {
            try
            {
                var bureauLoadStats = _mapper.Map<BureauLoadStats>(inputDTO);
                var adHocFileSubmission = _dbContext.AdhocFileSubmissions
                    .Include(x => x.Member)
                    .Where(x => x.Id == inputDTO.AdHocFileSubmissionId)
                    .FirstOrDefault();
                var bureauLoadStatsId = _dbContext.BureauLoadStats
                    .Where(x => x.AdHocFileSubmissionId == adHocFileSubmission.Id)
                    .AsNoTracking()
                    .FirstOrDefault()
                    .Id;

                bureauLoadStats.Id = bureauLoadStatsId;

                if (bureauLoadStats.MemberId == null || bureauLoadStats.MemberId == 0)
                {
                    var currentUser = _dbContext.Users
                   .Where(x => x.Auth0Id == user.Identity.Name)
                   .Include(x => x.Members)
                   .FirstOrDefault();

                    bureauLoadStats.MemberId = currentUser.Members.FirstOrDefault().MemberId;
                }

                _dbContext.BureauLoadStats.Update(bureauLoadStats);
                _dbContext.SaveChanges();
            }
            catch
            {
                throw;
            }
        }
    }
}

