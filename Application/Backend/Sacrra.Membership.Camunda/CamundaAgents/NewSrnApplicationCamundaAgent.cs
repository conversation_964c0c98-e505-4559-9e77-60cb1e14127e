using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Camunda.Api.Client;
using Camunda.Api.Client.ExternalTask;
using Camunda.Api.Client.User;
using Microsoft.Extensions.DependencyInjection;
using Sacrra.Membership.Business.Services;
using Sacrra.Membership.Camunda.CamundaServices;

namespace Sacrra.Membership.Camunda.CamundaAgents;

public class NewSrnApplicationCamundaAgent : BaseCamundaAgent
{
    private readonly CamundaClient _camundaClient;
    public NewSrnApplicationCamundaAgent(CamundaClient camundaClient) : base(camundaClient)
    {
        _camundaClient = camundaClient;
    }

    public override async Task Process(string topicName, ExternalTaskResource task, CompleteExternalTask completeExternalTask,
        IServiceScope serviceScope)
    {
        switch (topicName)
        {
            case "create-srn-number":
                CreateSrnNumber(task, completeExternalTask, serviceScope);
                break;

            case "allocate-stake-holder-manager":
                AllocateStakeholderManager(task, completeExternalTask, serviceScope);
                break;

            case "notify-applicant-of-srn-application-rejection":
                NotifyApplicantOfSrnApplicationRejection(task, serviceScope);
                break;

            case "email-bureaus-before-srn-testing":
                EmailBureausBeforeSrnTesting(task, completeExternalTask, serviceScope);
                break;

            case "update-srn-status-to-live":
                UpdateSrnStatusToLiveNoTestingRequired(task, serviceScope);
                break;

            case "email-member":
                EmailMember(task, completeExternalTask, serviceScope);
                break;

            case "call_file_test_subprocess":
                CallFileTestSubprocess(task, completeExternalTask, serviceScope);
                break;

            case "email-bureaus-on-srn-go-live":
                EmailBureausOnSrnGoLive(task, completeExternalTask, serviceScope);
                break;
        }

        task.Complete(completeExternalTask);
    }

    private UserProfileInfo getRandomUser(string group)
    {
        var usersInGroup = _camundaClient.Users.Query(new UserQuery()
        {
            MemberOfGroup = group
        }).List();
        var randomUser = usersInGroup.PickRandom();

        return randomUser;
    }
    
    private void EmailBureausOnSrnGoLive(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var srnId = GetIntegerVariable(task, "SRNId", serviceScope);
        var randomUser = getRandomUser("SACRRAAdministrator"); 

        GetNewSrnApplicationCamundaService(serviceScope).EmailBureausOnSRNGoLive(srnId);
        completeExternalTask.Variables = new Dictionary<string, VariableValue>
        {
            ["SACRRAAdminAssignee"] = VariableValue.FromObject(randomUser.Id)
        };
    }

    private void CallFileTestSubprocess(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var srnId = GetIntegerVariable(task, "SRNId", serviceScope);

        GetNewSrnApplicationCamundaService(serviceScope).CallFileTestSubprocess(task, serviceScope);
    }

    private void EmailMember(ExternalTaskResource task, CompleteExternalTask completeExternalTask,
        IServiceScope serviceScope)
    {
        var srnId = GetIntegerVariable(task, "SRNId", serviceScope);
        var randomUser = GetRandomUser("SACRRAAdministrator");

        GetNewSrnApplicationCamundaService(serviceScope).EmailMember(srnId);
        completeExternalTask.Variables = new Dictionary<string, VariableValue>
        {
            ["SACRRAAdminAssignee"] = VariableValue.FromObject(randomUser.Id)
        };
    }

    private static void UpdateSrnStatusToLiveNoTestingRequired(ExternalTaskResource task,
        IServiceScope serviceScope)
    {
        var srnId = GetIntegerVariable(task, "SRNId", serviceScope);
        // Looks like the stakeHolderManagerAssignee is the last person to interact with the workflow
        // so the updatedByUserId is the stakeHolderManagerAssignee
        var updatedbyUserId = GetIntegerVariable(task, "stakeHolderManagerAssignee", serviceScope);
        var taskInfo = task.Get();

        if (taskInfo == null)
            throw new Exception("Task information is null");
        
        GetNewSrnApplicationCamundaService(serviceScope).UpdateSrnStatusToLiveNoTestingRequired(task, srnId, "Live", updatedbyUserId);
    }

    private static void EmailBureausBeforeSrnTesting(ExternalTaskResource task,
        CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var srnId = GetIntegerVariable(task, "SRNId", serviceScope);

        GetNewSrnApplicationCamundaService(serviceScope).EmailBureausBeforeSrnTesting(srnId, task);
    }

    private static void NotifyApplicantOfSrnApplicationRejection(ExternalTaskResource task, IServiceScope serviceScope)
    {
        var srnId = GetIntegerVariable(task, "SRNId", serviceScope);

        // GetNewSrnApplicationCamundaService(serviceScope).UpdateSrnStatus("Rejected", srnId);
        GetNewSrnApplicationCamundaService(serviceScope).NotifyApplicantOfSrnApplicationRejection(srnId);
    }

    private static void AllocateStakeholderManager(ExternalTaskResource task,
        CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var memberId = GetIntegerVariable(task, "MemberId", serviceScope);
        var member = GetNewSrnApplicationCamundaService(serviceScope).GetMember(memberId);
        var srnId = GetIntegerVariable(task, "SRNId", serviceScope);
        var processInstanceId = GetGenericVariable(task, "ProcessInstanceId", serviceScope);
        
        GetNewSrnApplicationCamundaService(serviceScope).UpdateSrnStatusToPmVerification(srnId, processInstanceId);
        
        completeExternalTask.Variables = new Dictionary<string, VariableValue>
        {
            ["stakeHolderManagerAssignee"] = VariableValue.FromObject(member.StakeholderManager.Id.ToString())
        };
    }

    private static void CreateSrnNumber(ExternalTaskResource task, CompleteExternalTask completeExternalTask,
        IServiceScope serviceScope)
    {
        var srnId = GetIntegerVariable(task, "SRNId", serviceScope);

        GetNewSrnApplicationCamundaService(serviceScope).CreateSrnNumber(srnId);
    }

    private static NewSrnApplicationCamundaService GetNewSrnApplicationCamundaService(IServiceScope serviceScope)
    {
        return serviceScope.ServiceProvider.GetRequiredService<NewSrnApplicationCamundaService>();
    }
}
