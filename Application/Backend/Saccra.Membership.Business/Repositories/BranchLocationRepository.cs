using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.ListParams;
using Sacrra.Membership.Business.Resources.BranchLocation;
using Sacrra.Membership.Business.Resources.IdValuePair;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Models;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Sacrra.Membership.Business.Repositories
{
    public class BranchLocationRepository
    {
        private readonly AppDbContext _dbContext;
        public IMapper _mapper { get; }

        public BranchLocationRepository(AppDbContext dbContext, IMapper mapper)
        {
            this._mapper = mapper;
            this._dbContext = dbContext;
        }

        public BranchLocationGetResource Get(int id)
        {
            var selectRecord = await _dbContext.Set<BranchLocation>()
                    .Include(i => i.SRN)
                    .AsNoTracking()
                    .FirstOrDefaultAsync(s => s.Id == id);

            var returnRecord = _mapper.Map<BranchLocationGetResource>(selectRecord);

            return returnRecord;
        }

        public async Task<PagedList<IdValuePairResource>> List(NameListParams listParams)
        {
            var query = _dbContext.Set<BranchLocation>()
                    .AsQueryable();
            if (listParams != null)
            {
                if (listParams.Name != null)
                    query = query.Where(u => u.Name.ToLower().Contains(listParams.Name.ToLower()));
            }
            if (listParams.SortDirection == "asc")
            {
                switch (listParams.SortBy)
                {
                    default:
                        query = query.OrderBy(u => u.Name);
                        break;
                }
            }
            else
            {
                switch (listParams.SortBy)
                {
                    default:
                        query = query.OrderByDescending(u => u.Name);
                        break;
                }
            }

            var count = query.Count();
            var pageNumber = listParams.PageNumber;
            if (count / listParams.PageSize < listParams.PageNumber)
                pageNumber = (count / listParams.PageSize) + 1;

            var queryItems = query.Skip((pageNumber - 1) * listParams.PageSize).Take(listParams.PageSize).ToList();
            var itemsToReturn = _mapper.Map<IEnumerable<IdValuePairResource>>(queryItems).ToList();

            return new PagedList<IdValuePairResource>(itemsToReturn, count, pageNumber, listParams.PageSize);
        }
        public BranchLocationGetResource Update(BranchLocationUpdateResource modelForUpdate)
        {
            var model = _mapper.Map<BranchLocation>(modelForUpdate);

            _dbContext.Set<BranchLocation>().Update(model);

            _dbContext.SaveChanges();

            return Get(model.Id);
        }

        public int Create(BranchLocationCreateResource modelForCreate)
        {
            var model = _mapper.Map<BranchLocation>(modelForCreate);

            await _dbContext.Set<BranchLocation>().AddAsync(model);

            _dbContext.SaveChanges();

            return model.Id;
        }
        public void Delete(int id)
        {
            var entity = await _dbContext.Set<BranchLocation>().FindAsync(id);

            _dbContext.Set<BranchLocation>().Remove(entity);
            _dbContext.SaveChanges();
        }
    }
}

