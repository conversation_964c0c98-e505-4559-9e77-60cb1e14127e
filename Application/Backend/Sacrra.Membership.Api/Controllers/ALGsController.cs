using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sacrra.Membership.Api.Helpers;
using Sacrra.Membership.Business.ListParams;
using Sacrra.Membership.Business.Resources;
using Sacrra.Membership.Business.Resources.IdValuePair;
using System.Collections.Generic;
using System.Threading.Tasks;
using Sacrra.Membership.Business.Repositories;

namespace Sacrra.Membership.Api.Controllers
{
    [Produces("application/json")]
    [ApiController]
    [Route("api/v{v:apiVersion}/[controller]")]
    public class ALGsController : Controller
    {
        private readonly ALGRepository _repository;

        public ALGsController(ALGRepository ALGRepository)
        {
            _repository = ALGRepository;
        }

        [Authorize]
        [HttpGet("{id}", Name = "GetALG")]
        [ProducesResponseType(typeof(ALGGetResource), 200)]
        [ProducesResponseType(typeof(ALGGetResource), 404)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult Get(int id)
        {
            var entity = _repository.Get(id);
            if (entity == null)
                return NotFound();

            return Ok(entity);
        }

        [Authorize]
        [HttpGet]
        [ProducesResponseType(type: typeof(List<IdValuePairResource>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult List([FromQuery]NameListParams listParams)
        {
            var ALGs = _repository.List(listParams);

            Response.AddPagination(ALGs.CurrentPage, ALGs.PageSize, ALGs.TotalCount, ALGs.TotalPages);

            return Ok(ALGs);
        }

        [Authorize(Roles = "Group Stakeholder Manager")]
        [HttpPost]
        [ProducesResponseType(typeof(int), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult Create([FromBody]ALGCreateResource modelForCreate)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var id = _repository.Create(modelForCreate);

            return CreatedAtRoute("GetALG", new { controller = "ALGs", id }, id);
        }

        [Authorize(Roles = "Group Stakeholder Manager")]
        [HttpPut("{id}")]
        [ProducesResponseType(typeof(ALGGetResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult Update(int id, [FromBody]ALGUpdateResource modelForUpdate)
        {
            // validate request
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            return Ok(_repository.Update(modelForUpdate));
        }

        [Authorize(Roles = "Group Stakeholder Manager")]
        [HttpDelete("{id}")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult Delete(int id)
        {
            _repository.Delete(id);
            return Ok();
        }
    }
}

