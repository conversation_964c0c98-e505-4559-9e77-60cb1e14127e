using Camunda.Api.Client;
using Camunda.Api.Client.ExternalTask;
using Microsoft.Extensions.DependencyInjection;
using System.Threading.Tasks;
using Sacrra.Membership.Business.Services;

namespace Sacrra.Membership.Camunda.CamundaAgents
{
    public class AdHocFileSubmissionsCamundaAgent : BaseCamundaAgent
    {
        public AdHocFileSubmissionsCamundaAgent(CamundaClient camundaClient) : base(camundaClient) { }

        public void Process(string topicName, ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
        {
            switch (topicName)
            {
                case "adhoc_file_submission_update_status_declined":
                    AdhocFileSubmissionUpdateStatusDeclined(task, completeExternalTask, serviceScope);
                    break;

                case "adhoc_file_submission_check_file_submitted_to_dth":
                    AdhocFileSubmissionCheckFileSubmittedToDth(task, completeExternalTask, serviceScope);
                    break;

                case "adhoc_file_submission_update_status_approved":
                    AdhocFileSubmissionUpdateStatusApproved(task, completeExternalTask, serviceScope);
                    break;

                case "adhoc_file_submission_email_member_bureaus_approved":
                    AdhocFileSubmissionEmailMemberBureausApproved(task, completeExternalTask, serviceScope);
                    break;

                case "adhoc_file_submission_create_file_ftpserver":
                    AdhocFileSubmissionCreateFileFtpserver(task, completeExternalTask, serviceScope);
                    break;

                case "adhoc_file_submission_email_member_declined":
                    AdhocFileSubmissionEmailMemberDeclined(task, completeExternalTask, serviceScope);
                    break;

                case "adhoc_file_submission_update_submitted_cancelled":
                    AdhocFileSubmissionUpdateSubmittedCancelled(task, completeExternalTask, serviceScope);
                    break;

                case "adhoc_file_submission_email_member_bureaus_cancelled":
                    AdhocFileSubmissionEmailMemberBureausCancelled(task, completeExternalTask, serviceScope);
                    break;
            }
            
            task.Complete(completeExternalTask);
        }

        private void AdhocFileSubmissionEmailMemberBureausCancelled(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
        {
            var camundaTask = task.Get();

            GetAdHocFileSubmissionsCamundaService(serviceScope).AdHocFileSubmissionEmailMemberBureausCancelled(camundaTask.ProcessInstanceId);
        }

        private void AdhocFileSubmissionUpdateSubmittedCancelled(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
        {
            var camundaTask = task.Get();

            GetAdHocFileSubmissionsCamundaService(serviceScope).AdHocFileSubmissionUpdateFileSubmissionToSubmittedOrCancelled(camundaTask);
        }

        private void AdhocFileSubmissionEmailMemberDeclined(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
        {
            var camundaTask = task.Get();

            GetAdHocFileSubmissionsCamundaService(serviceScope).AdHocFileSubmissionEmailMemberDeclined(camundaTask.ProcessInstanceId);
        }

        private void AdhocFileSubmissionCreateFileFtpserver(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
        {
            var camundaTask = task.Get();

            GetAdHocFileSubmissionsCamundaService(serviceScope).AdHocFileSubmissionCreateFileOnFTPServer(camundaTask.ProcessInstanceId);
        }

        private void AdhocFileSubmissionEmailMemberBureausApproved(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
        {
            var camundaTask = task.Get();

            GetAdHocFileSubmissionsCamundaService(serviceScope).AdHocFileSubmissionEmailMemberApproved(camundaTask.ProcessInstanceId);
        }

        private void AdhocFileSubmissionUpdateStatusDeclined(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
        {
            var camundaTask = task.Get();

            GetAdHocFileSubmissionsCamundaService(serviceScope).UpdateAdHocFileSubmissionStatusToDeclined(camundaTask.ProcessInstanceId);
        }

        private void AdhocFileSubmissionCheckFileSubmittedToDth(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
        {
            var camundaTask = task.Get();

            GetAdHocFileSubmissionsCamundaService(serviceScope).AdHocFileSubmissionCheckFileSubmittedToDTH(camundaTask);
        }

        private void AdhocFileSubmissionUpdateStatusApproved(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
        {
            var camundaTask = task.Get();

            GetAdHocFileSubmissionsCamundaService(serviceScope).UpdateAdHocFileSubmissionStatusToApproved(camundaTask.ProcessInstanceId);
        }
        
        private static AdHocFileSubmissionsCamundaService GetAdHocFileSubmissionsCamundaService(IServiceScope serviceScope)
        {
            return serviceScope.ServiceProvider.GetRequiredService<AdHocFileSubmissionsCamundaService>();
        }
    }
}
