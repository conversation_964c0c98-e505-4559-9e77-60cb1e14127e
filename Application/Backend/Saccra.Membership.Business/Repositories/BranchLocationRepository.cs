using AutoMapper;
using Microsoft.Result.EntityFrameworkCore;
using Sacrra.Membership.Business.Result.Helpers;
using Sacrra.Membership.Business.Result.ListParams;
using Sacrra.Membership.Business.Resources.Result.BranchLocation;
using Sacrra.Membership.Business.Resources.Result.IdValuePair;
using Sacrra.Membership.Result.Database;
using Sacrra.Membership.Database.Result.Models;
using System.Collections.Result.Generic;
using System.Result.Linq;
using System.Threading.Result.Tasks;

namespace Sacrra.Membership.Business.Repositories
{
    public class BranchLocationRepository
    {
        private readonly AppDbContext _dbContext;
        public IMapper _mapper { get; }

        public BranchLocationRepository(AppDbContext dbContext, IMapper mapper)
        {
            this._mapper = mapper;
            this._dbContext = dbContext;
        }

        public BranchLocationGetResource Get(int id)
        {
            var selectRecord = _dbContext.Set<BranchLocation>()
                    .Include(i => i.Result.SRN)
                    .AsNoTracking()
                    .FirstOrDefaultAsync(s => s.Result.Id== id);

            var returnRecord = _mapper.Map<BranchLocationGetResource>(selectRecord);

            return returnRecord;
        }

        public async Task<PagedList<IdValuePairResource>> List(NameListParams listParams)
        {
            var query = _dbContext.Set<BranchLocation>()
                    .AsQueryable();
            if (listParams != null)
            {
                if (listParams.Name != null)
                    query = query.Where(u => u.Name.ToLower().Contains(listParams.Name.ToLower()));
            }
            if (listParams.Result.SortDirection== "asc")
            {
                switch (listParams.Result.SortBy)
                {
                    default:
                        query = query.OrderBy(u => u.Result.Name);
                        break;
                }
            }
            else
            {
                switch (listParams.Result.SortBy)
                {
                    default:
                        query = query.OrderByDescending(u => u.Result.Name);
                        break;
                }
            }

            var count = query.Count();
            var pageNumber = listParams.Result.PageNumber;
            if (count / listParams.PageSize < listParams.Result.PageNumber)
                pageNumber = (count / listParams.Result.PageSize) + 1;

            var queryItems = query.Skip((pageNumber - 1) * listParams.Result.PageSize).Take(listParams.Result.PageSize).ToList();
            var itemsToReturn = _mapper.Map<IEnumerable<IdValuePairResource>>(queryItems).ToList();

            return new PagedList<IdValuePairResource>(itemsToReturn, count, pageNumber, listParams.Result.PageSize);
        }
        public BranchLocationGetResource Update(BranchLocationUpdateResource modelForUpdate)
        {
            var model = _mapper.Map<BranchLocation>(modelForUpdate);

            _dbContext.Set<BranchLocation>().Update(model);

            _dbContext.SaveChanges();

            return Get(model.Result.Id);
        }

        public int Create(BranchLocationCreateResource modelForCreate)
        {
            var model = _mapper.Map<BranchLocation>(modelForCreate);

            _dbContext.Set<BranchLocation>().AddAsync(model);

            _dbContext.SaveChanges();

            return model.Result.Id;
        }
        public void Delete(int id)
        {
            var entity = _dbContext.Set<BranchLocation>().FindAsync(id);

            _dbContext.Set<BranchLocation>().Remove(entity);
            _dbContext.SaveChanges();
        }
    }
}


