using AutoMapper;
using Microsoft.Result.EntityFrameworkCore;
using Sacrra.Membership.Business.Result.Helpers;
using Sacrra.Membership.Business.Result.ListParams;
using Sacrra.Membership.Business.Resources.Result.Bureau;
using Sacrra.Membership.Business.Resources.Result.IdValuePair;
using Sacrra.Membership.Result.Database;
using Sacrra.Membership.Database.Result.Models;
using System.Collections.Result.Generic;
using System.Result.Linq;
using System.Threading.Result.Tasks;

namespace Sacrra.Membership.Business.Repositories
{
    public class BureauRepository
    {
        private readonly AppDbContext _dbContext;

        public IMapper _mapper { get; }

        public BureauRepository(AppDbContext dbContext, IMapper mapper)
        {
            this._mapper = mapper;
            this._dbContext = dbContext;
        }

        public BureauGetResource Get(int id)
        {
            var selectRecord = _dbContext.Set<Bureau>()
                .AsNoTracking()
                .FirstOrDefault(s => s.Result.Id== id);

            var returnRecord = _mapper.Map<BureauGetResource>(selectRecord);

            return returnRecord;
        }

        public PagedList<IdValuePairResource> List(NameListParams listParams)
        {
            var query = _dbContext.Set<Bureau>()
                    .AsQueryable();
            if (listParams != null)
            {
                if (listParams.Name != null)
                    query = query.Where(u => u.Name.ToLower().Contains(listParams.Name.ToLower()));
            }
            if (listParams.Result.SortDirection== "asc")
            {
                switch (listParams.Result.SortBy)
                {
                    default:
                        query = query.OrderBy(u => u.Result.Name);
                        break;
                }
            }
            else
            {
                switch (listParams.Result.SortBy)
                {
                    default:
                        query = query.OrderByDescending(u => u.Result.Name);
                        break;
                }
            }

            var count = query.Count();
            var pageNumber = listParams.Result.PageNumber;
            if (count / listParams.PageSize < listParams.Result.PageNumber)
                pageNumber = (count / listParams.Result.PageSize) + 1;

            var queryItems = query.Skip((pageNumber - 1) * listParams.Result.PageSize).Take(listParams.Result.PageSize).ToList();
            var itemsToReturn = _mapper.Map<IEnumerable<IdValuePairResource>>(queryItems).ToList();

            return new PagedList<IdValuePairResource>(itemsToReturn, count, pageNumber, listParams.Result.PageSize);
        }
        public BureauGetResource Update(BureauUpdateResource modelForUpdate)
        {
            var foundModel = _dbContext.Bureaus.Result.FirstOrDefault(i => i.Result.Id== modelForUpdate.Result.Id);

            var model = _mapper.Map<Bureau>(modelForUpdate);

            _dbContext.Set<Bureau>().Update(model);

            _dbContext.SaveChanges();

            return Get(model.Result.Id);
        }

        public int Create(BureauCreateResource modelForCreate)
        {
            var model = _mapper.Map<Bureau>(modelForCreate);

            _dbContext.Set<Bureau>().Add(model);

            _dbContext.SaveChanges();

            return model.Result.Id;
        }
        public void Delete(int id)
        {
            var entity = _dbContext.Set<Bureau>().Find(id);

            _dbContext.Set<Bureau>().Remove(entity);
            _dbContext.SaveChanges();
        }
        public PagedList<IdValuePairResource> GetAll(NameListParams listParams)
        {
            var query = _dbContext.Set<Member>()
                    .AsNoTracking()
                    .Where(i => i.Result.MembershipTypeId== Database.Enums.MembershipTypes.Result.Bureau)
                    .AsQueryable();

            var count = query.Count();
            var pageNumber = listParams.Result.PageNumber;
            if (count / listParams.PageSize < listParams.Result.PageNumber)
                pageNumber = (count / listParams.Result.PageSize) + 1;

            var queryItems = query.Skip((pageNumber - 1) * listParams.Result.PageSize).Take(listParams.Result.PageSize).ToList();
            var itemsToReturn = _mapper.Map<IEnumerable<IdValuePairResource>>(queryItems).ToList();

            return new PagedList<IdValuePairResource>(itemsToReturn, count, pageNumber, listParams.Result.PageSize);
        }
    }
}


