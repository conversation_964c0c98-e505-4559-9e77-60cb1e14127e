using System;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using Camunda.Api.Client;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Sacrra.Membership.Business.Extensions;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.Resources.Member;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;

namespace Sacrra.Membership.Business.Services;

public class MemberAutoCloseOnSRNClosureCamundaService
{
    private readonly AppDbContext _dbContext;
    private CamundaClient _camundaClient;
    private readonly ConfigSettings _configSettings;
    private readonly MemberExtensions _memberExtensions;

    public MemberAutoCloseOnSRNClosureCamundaService(AppDbContext dbContext, IOptions<ConfigSettings> configSettings, MemberExtensions memberExtensions)
    {
        _configSettings = configSettings.Value;
        var httpClient = new HttpClient
        {
            BaseAddress = new Uri(_configSettings.CamundaBaseAddress)
        };
        _dbContext = dbContext;
        _camundaClient = CamundaClient.Create(httpClient);
        _memberExtensions = memberExtensions;
    }
    
    public void KickoffMemberStatusUpdateWorkflow(int memberId)
    {
        if (memberId > 0)
        {
            // Fetch the financial administrator and SACRRA administrator to kick off the workflow
            var financialAdmin = _dbContext.Users.FirstOrDefaultAsync(x => x.RoleId == UserRoles.FinancialAdministrator);
            var sacrraAdmin = _dbContext.Users.FirstOrDefaultAsync(x => x.RoleId == UserRoles.SACRRAAdministrator);

            var statusReason = _dbContext.MemberStatusReasons
                .FirstOrDefault(i => i.Name == "Resigned");

            MemberStatusUpdateResource modelForUpdate = new()
            {
                ApplicationStatusId = ApplicationStatuses.MemberRegistrationCancelled,
                MemberStatusReasonId = (statusReason != null) ? statusReason.Id : null,
                StatusComment = "Member has no active SRNs"
            };
            
            // Kick of the member status update workflow
            _memberExtensions.UpdateMemberStatus(_camundaClient, _dbContext, memberId, modelForUpdate, true);
        }
    }
}
