using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Camunda.Api.Client;
using Camunda.Api.Client.ExternalTask;
using Microsoft.Extensions.DependencyInjection;
using Sacrra.Membership.Business.Services;

namespace Sacrra.Membership.Camunda.CamundaAgents;

public class SrnSellingCamundaAgent : BaseCamundaAgent
{
    public SrnSellingCamundaAgent(CamundaClient camundaClient) : base(camundaClient)
    {
    }

    public override async Task Process(string topicName, ExternalTaskResource task,
        CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        switch (topicName)
        {
            case "re-allocate-srn-from-seller-to-buyer":
                ReAllocateSrnFromSellerToBuyer(task, completeExternalTask, serviceScope);
                break;

            case "notify-member-of-SRN-sale-request-cancellation":
                NotifyMemberOfSrnSaleRequestCancellation(task, completeExternalTask, serviceScope);
                break;

            case "notify-seller-about-unregistered-buyer":
                NotifySellerAboutUnregisteredBuyer(task, completeExternalTask, serviceScope);
                break;

            case "notify-buyer-member-to-start-new-srn-registration-process":
                NotifyBuyerMemberToStartNewSrnRegistrationProcess(task, completeExternalTask, serviceScope);
                break;

            case "notify-bureus-and-shm-of-sale-file-submission-completion":
                NotifyBureusAndShmOfSaleFileSubmissionCompletion(task, completeExternalTask, serviceScope);
                break;

            case "notify-member-about-srn-split-or-merge-cancellation":
                NotifyMemberAboutSrnSplitOrMKergeCancellation(task, completeExternalTask, serviceScope);
                break;

            case "notify-seller-and-shm-of-sale-request-commencement":
                NotifySellerAndShmOfSaleRequestCommencement(task, completeExternalTask, serviceScope);
                break;

            case "split-merge-sale-update-srn-status-after-testing":
                SplitMergeSaleUpdateSrnStatusAfterTesting(task, completeExternalTask, serviceScope);
                break;

            case "split-merge-sale-email-bureaus-on-srn-go-live":
                SplitMergeSaleEmailBureausOnSrnGoLive(task, completeExternalTask, serviceScope);
                break;
        }

        task.Complete(completeExternalTask);
    }

    private void SplitMergeSaleEmailBureausOnSrnGoLive(ExternalTaskResource task,
        CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var requestType = GetGenericVariable(task, "RequestType", serviceScope);

        if (requestType.ToLower() == "sale")
        {
            var srnId = GetMemberVariable(task, "BuyerSRNId", serviceScope);

            if (srnId > 0)
            {
                GetSrnSellingCamundaService(serviceScope).EmailBureausOnSRNGoLive(srnId);
            }
        }
        else if (requestType.ToLower() == "split")
        {
            var splitListVariable = GetGenericVariable(task, "SRNIdSplitList", serviceScope);
            if (!string.IsNullOrEmpty(splitListVariable))
            {
                var splitList = splitListVariable.Split(',');

                foreach (var srnId in splitList)
                {
                    GetSrnSellingCamundaService(serviceScope).EmailBureausOnSRNGoLive(Convert.ToInt32(srnId));
                }
            }
        }
        else if (requestType.ToLower() == "merge")
        {
            var mergeToSRNId = GetGenericVariable(task, "MergeToSRNId", serviceScope);
            if (!string.IsNullOrEmpty(mergeToSRNId))
            {
                GetSrnSellingCamundaService(serviceScope).EmailBureausOnSRNGoLive(Convert.ToInt32(mergeToSRNId));
            }
        }

        var randomUser =
            GetRandomUser("SACRRAAdministrator"); //TODO: Waiting for Leon to confirm how do we allocate
        // the SACRRA Admin. For now, I just select any random user that
        // has the SACRRA Administrator role.

        completeExternalTask.Variables = new Dictionary<string, VariableValue>
        {
            ["SACRRAAdminAssignee"] = VariableValue.FromObject(randomUser.Id)
        };
    }

    private static void SplitMergeSaleUpdateSrnStatusAfterTesting(ExternalTaskResource task,
        CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var srnId = 0;

        var requestType = GetGenericVariable(task, "RequestType", serviceScope);

        if (requestType.ToLower() == "sale")
        {
            srnId = GetMemberVariable(task, "BuyerSRNId", serviceScope);
            var taskInfo = task.Get();

            var processInstanceId = string.Empty;
            if (taskInfo != null)
            {
                processInstanceId = taskInfo.ProcessInstanceId;
            }

            if (srnId > 0)
            {
                GetSrnSellingCamundaService(serviceScope).UpdateSRNStatus(srnId, "Test", 0, processInstanceId);
            }
        }
        else if (requestType.ToLower() == "split")
        {
            var splitListVariable = GetGenericVariable(task, "SRNIdSplitList", serviceScope);
            if (!string.IsNullOrEmpty(splitListVariable))
            {
                var splitList = splitListVariable.Split(',');
                var taskInfo = task.Get();

                var processInstanceId = string.Empty;
                if (taskInfo != null)
                {
                    processInstanceId = taskInfo.ProcessInstanceId;
                }

                foreach (var splitToId in splitList)
                {
                    GetSrnSellingCamundaService(serviceScope)
                        .UpdateSRNStatus(Convert.ToInt32(splitToId), "Test", 0, processInstanceId);
                }
            }
        }
        else if (requestType.ToLower() == "merge")
        {
            var mergeToSRNId = GetGenericVariable(task, "MergeToSRNId", serviceScope);
            var taskInfo = task.Get();

            var processInstanceId = string.Empty;
            if (taskInfo != null)
            {
                processInstanceId = taskInfo.ProcessInstanceId;
            }

            if (!string.IsNullOrEmpty(mergeToSRNId))
            {
                GetSrnSellingCamundaService(serviceScope)
                    .UpdateSRNStatus(Convert.ToInt32(mergeToSRNId), "Test", 0, processInstanceId);
            }
        }
    }

    private static void NotifySellerAndShmOfSaleRequestCommencement(ExternalTaskResource task,
        CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var srnId = GetIntegerVariable(task, "SRNIdToBeSold", serviceScope);

        GetSrnSellingCamundaService(serviceScope).NotifySellerAndSHMOfSaleRequestCommencement(srnId);
    }

    private static void NotifyMemberAboutSrnSplitOrMKergeCancellation(ExternalTaskResource task,
        CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var requestType = GetGenericVariable(task, "RequestType", serviceScope);

        if (requestType.ToLower() == "split")
        {
            var splitFromSRNId = GetGenericVariable(task, "SplitFromSRNId", serviceScope);
            if (!string.IsNullOrEmpty(splitFromSRNId))
            {
                var splitType = GetGenericVariable(task, "SaleType", serviceScope);
                if (splitType.ToLower() == "full")
                {
                    var initialSRNStatusId = GetGenericVariable(task, "InitialStatusId", serviceScope);

                    if (!string.IsNullOrEmpty(initialSRNStatusId))
                    {
                        GetSrnSellingCamundaService(serviceScope).UpdateSRNStatus(Convert.ToInt32(splitFromSRNId),
                            Convert.ToInt32(initialSRNStatusId));
                    }
                }

                GetSrnSellingCamundaService(serviceScope)
                    .NotifyMemberOfSRNSplitCancellation(Convert.ToInt32(splitFromSRNId));
            }
        }
        else if (requestType.ToLower() == "merge")
        {
            var mergeToSRNId = GetGenericVariable(task, "MergeToSRNId", serviceScope);

            if (!string.IsNullOrEmpty(mergeToSRNId))
            {
                var mergeType = GetGenericVariable(task, "SaleType", serviceScope);
                if (mergeType.ToLower() == "full")
                {
                    var initialSRNStatusIds = GetGenericVariable(task, "MergeListInitialStatusIds", serviceScope);
                    if (!string.IsNullOrEmpty(initialSRNStatusIds))
                    {
                        var srnsToBeUpdated = initialSRNStatusIds.Split(',');
                        foreach (var srn in srnsToBeUpdated)
                        {
                            var idStatusPair = srn.Split(':');
                            if (idStatusPair.Length > 1)
                            {
                                var srnId = Convert.ToInt32(idStatusPair[0]);
                                var statusId = Convert.ToInt32(idStatusPair[1]);

                                if (srnId > 0 && statusId > 0)
                                {
                                    GetSrnSellingCamundaService(serviceScope).UpdateSRNStatus(srnId, statusId);
                                }
                            }
                        }
                    }
                }

                GetSrnSellingCamundaService(serviceScope)
                    .NotifyMemberOfSRNMergeCancellation(Convert.ToInt32(mergeToSRNId));
            }
        }
        else if (requestType.ToLower() == "sale")
        {
            var srnToBeSoldId = GetGenericVariable(task, "SRNIdToBeSold", serviceScope);
            if (!string.IsNullOrEmpty(srnToBeSoldId))
            {
                var reviewComments = GetGenericVariable(task, "ReviewCommentsBuyer", serviceScope);

                if (!string.IsNullOrEmpty(srnToBeSoldId))
                    GetSrnSellingCamundaService(serviceScope)
                        .NotifyMemberOfSRNSaleRequestCancellation(Convert.ToInt32(srnToBeSoldId), reviewComments);
            }
        }
    }

    private static void NotifyBureusAndShmOfSaleFileSubmissionCompletion(ExternalTaskResource task,
        CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var srnId = 0;

        var requestType = GetGenericVariable(task, "RequestType", serviceScope);

        if (requestType == "split")
            srnId = GetIntegerVariable(task, "SplitFromSRNId", serviceScope);
        else if (requestType == "merge")
            srnId = GetIntegerVariable(task, "MergeToSRNId", serviceScope);
        else if (requestType == "sale")
            srnId = GetIntegerVariable(task, "SRNIdToBeSold", serviceScope);

        GetSrnSellingCamundaService(serviceScope).NotifyBureusAndSHMOfSaleFileSubmissionCompletion(srnId);
    }

    private static void NotifyBuyerMemberToStartNewSrnRegistrationProcess(ExternalTaskResource task,
        CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var requestType = GetGenericVariable(task, "RequestType", serviceScope);

        if (requestType.ToLower() == "sale")
        {
            var srnId = GetMemberVariable(task, "SRNIdToBeSold", serviceScope);
            var buyerMemberId = GetMemberVariable(task, "BuyerMemberId", serviceScope);

            if (srnId > 0)
            {
                GetSrnSellingCamundaService(serviceScope)
                    .NotifyBuyerMemberToStartNewSRNRegistrationProcess(srnId, buyerMemberId);
            }
        }
        else if (requestType.ToLower() == "split")
        {
            var splitFromSRNId = GetGenericVariable(task, "SplitFromSRNId", serviceScope);
            if (!string.IsNullOrEmpty(splitFromSRNId))
            {
                GetSrnSellingCamundaService(serviceScope)
                    .NotifyBuyerMemberToStartNewSRNRegistrationProcess_SplitOrMerge(Convert.ToInt32(splitFromSRNId),
                        requestType.ToLower());
            }
        }
        else if (requestType.ToLower() == "merge")
        {
            var mergeToSRNId = GetGenericVariable(task, "MergeToSRNId", serviceScope);
            if (!string.IsNullOrEmpty(mergeToSRNId))
            {
                GetSrnSellingCamundaService(serviceScope)
                    .NotifyBuyerMemberToStartNewSRNRegistrationProcess_SplitOrMerge(Convert.ToInt32(mergeToSRNId),
                        requestType.ToLower());
            }
        }
    }

    private static void NotifySellerAboutUnregisteredBuyer(ExternalTaskResource task,
        CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var requestType = GetGenericVariable(task, "RequestType", serviceScope);

        if (requestType != null)
        {
            if (requestType == "sale")
            {
                var srnId = GetIntegerVariable(task, "SRNIdToBeSold", serviceScope);
                GetSrnSellingCamundaService(serviceScope).NotifySellerAboutUnregisteredBuyer(srnId);
            }
        }
    }

    private static void NotifyMemberOfSrnSaleRequestCancellation(ExternalTaskResource task,
        CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var srnId = GetIntegerVariable(task, "SRNIdToBeSold", serviceScope);
        var reviewComments = GetGenericVariable(task, "ReviewCommentsSeller", serviceScope);

        GetSrnSellingCamundaService(serviceScope).NotifyMemberOfSRNSaleRequestCancellation(srnId, reviewComments);
    }

    private static void ReAllocateSrnFromSellerToBuyer(ExternalTaskResource task,
        CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var taskObject = task.Get();
        var processInstanceId = taskObject.ProcessInstanceId;

        GetSrnSellingCamundaService(serviceScope).ReAllocateSRNFromSellerToBuyer(processInstanceId);
    }

    private static SrnSellingCamundaService GetSrnSellingCamundaService(IServiceScope serviceScope)
    {
        return serviceScope.ServiceProvider.GetRequiredService<SrnSellingCamundaService>();
    }
}
